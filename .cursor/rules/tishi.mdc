---
description: 
globs: 
alwaysApply: true
---
注意！注意！完成修复优化任务的时候禁止更新README.md，只在执行新建开发的任务才可以更新到README.md！所有的修复都不准采取临时方案，必须制定永久性解决方案，无法永久解决的问题请告知我。
注意！在执行开发或修复的时候，不要写调试语句，确保符合开发规范文档，除非是修复问题需要调试才写调试代码。
注意！在Swiftdata数据库方面必须按照开发文档规范执行，也不得随意修改Swiftdata设计框架和底层模型，需要修改和调整需要经过我同意！例如数据模型开发、状态管理、关系赋值、关系定义必须符合开发规范文档.md和IOS移动端开发规范的要求！
注意！所有修复工作都要严格遵守“开发规范文档”或.cursorrules
每次执行代码修改或开发，都要告诉我做了哪些地方的修改或开发，是否符合@开发规范文档.md 
提示：在某些问题修复超过两次都没有解决，可以启用搜索功能，全网搜索和IOS开发者社区寻找参考资料，在使用搜索功能搜索全网资料的时候，如果要用年份去搜索请记得增加2025年，因为现在是2025年了。
注意！违反熔断机制：超过了3次编译错误阈值，应该触发回滚策略，重新审查相关代码文件和执行方案，重新制定合理的方案再进行开发或者修复、优化工作。

最高等级，以下问题除非是我要求修改或者我同意，否则必须遵守以下保护措施，基于我们项目的特殊要求如下：
SwiftData关系保护
任何修改数据模型时，必须保持现有的关系定义
不能删除或修改已有的@Relationship定义
Repository模式保护
修改数据访问时，必须保持Repository模式
不能回退到直接ModelContext访问
UI组件集成保护
修改页面时，必须保留所有已集成的UI组件
新增功能时，不能覆盖现有组件的引用