import SwiftUI

// MARK: - Color Hex 扩展

extension Color {
    /// 从十六进制字符串创建Color
    /// - Parameter hex: 十六进制颜色字符串，支持"#FFFFFF"或"FFFFFF"格式
    /// - Returns: Color实例
    static func hexColor(_ hex: String) -> Color {
        var cleanHex = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        cleanHex = cleanHex.replacingOccurrences(of: "#", with: "")
        
        var rgbValue: UInt64 = 0
        Scanner(string: cleanHex).scanHexInt64(&rgbValue)
        
        let red = Double((rgbValue & 0xFF0000) >> 16) / 255.0
        let green = Double((rgbValue & 0x00FF00) >> 8) / 255.0
        let blue = Double(rgbValue & 0x0000FF) / 255.0
        
        return Color(red: red, green: green, blue: blue)
    }
} 