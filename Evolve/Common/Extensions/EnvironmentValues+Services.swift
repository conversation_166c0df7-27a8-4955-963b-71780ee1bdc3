//
//  EnvironmentValues+Services.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-27.
//

import SwiftUI
import SwiftData
import Foundation
import Combine

// MARK: - AI提醒管理器环境值

@preconcurrency
private struct AIInAppReminderManagerKey: EnvironmentKey {
    nonisolated static let defaultValue: EAAIInAppReminderManager = {
        return MainActor.assumeIsolated {
            EAAIInAppReminderManager()
        }
    }()
}

// MARK: - 通知代理环境值

@preconcurrency
private struct NotificationDelegateKey: EnvironmentKey {
    nonisolated static let defaultValue: NotificationDelegate = {
        return MainActor.assumeIsolated {
            NotificationDelegate()
        }
    }()
}

// MARK: - 性能监控器环境值

@preconcurrency
private struct RepositoryPerformanceMonitorKey: EnvironmentKey {
    nonisolated static let defaultValue: EARepositoryPerformanceMonitor = {
        return MainActor.assumeIsolated {
            EARepositoryPerformanceMonitor()
        }
    }()
}

// MARK: - Environment扩展

extension EnvironmentValues {
    
    /// AI应用内提醒管理器
    var aiInAppReminderManager: EAAIInAppReminderManager {
        get { self[AIInAppReminderManagerKey.self] }
        set { self[AIInAppReminderManagerKey.self] = newValue }
    }
    
    /// 通知代理
    var notificationDelegate: NotificationDelegate {
        get { self[NotificationDelegateKey.self] }
        set { self[NotificationDelegateKey.self] = newValue }
    }
    
    /// Repository性能监控器
    var repositoryPerformanceMonitor: EARepositoryPerformanceMonitor {
        get { self[RepositoryPerformanceMonitorKey.self] }
        set { self[RepositoryPerformanceMonitorKey.self] = newValue }
    }
}

// MARK: - 视图扩展：便捷方法

extension View {
    
    /// 注入AI提醒管理器到环境
    func aiInAppReminderManager(_ manager: EAAIInAppReminderManager) -> some View {
        self.environment(\.aiInAppReminderManager, manager)
    }
    
    /// 注入通知代理到环境
    func notificationDelegate(_ delegate: NotificationDelegate) -> some View {
        self.environment(\.notificationDelegate, delegate)
    }
    
    /// 注入性能监控器到环境
    func repositoryPerformanceMonitor(_ monitor: EARepositoryPerformanceMonitor) -> some View {
        self.environment(\.repositoryPerformanceMonitor, monitor)
    }
    
    #if DEBUG
    /// 注入Mock服务到环境（仅用于测试和预览）
    func mockServices() -> some View {
        let mockAIManager = EAAIInAppReminderManager()
        let mockDelegate = NotificationDelegate()
        let mockMonitor = EARepositoryPerformanceMonitor()
        
        return self
            .environment(\.aiInAppReminderManager, mockAIManager)
            .environment(\.notificationDelegate, mockDelegate)
            .environment(\.repositoryPerformanceMonitor, mockMonitor)
    }
    #endif
} 