import SwiftUI
import Combine

/// 键盘适配扩展
/// 为SwiftUI视图提供键盘弹出时的自动布局调整功能
extension View {
    /// 添加键盘适配功能
    /// 当键盘弹出时，视图会自动向上移动避免被遮挡
    func keyboardAdaptive() -> some View {
        modifier(KeyboardAdaptiveModifier())
    }
}

/// 键盘适配修饰符
/// 监听键盘显示/隐藏事件，自动调整视图位置
struct KeyboardAdaptiveModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .padding(.bottom, keyboardHeight)
            .onReceive(Publishers.keyboardHeight) { height in
                withAnimation(.easeOut(duration: 0.3)) {
                    keyboardHeight = height
                }
            }
    }
}

/// 键盘高度发布者
/// 监听系统键盘通知，发布键盘高度变化
extension Publishers {
    static var keyboardHeight: AnyPublisher<CGFloat, Never> {
        let willShow = NotificationCenter.default
            .publisher(for: UIResponder.keyboardWillShowNotification)
            .map { notification -> CGFloat in
                guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else {
                    return 0
                }
                return keyboardFrame.height
            }
        
        let willHide = NotificationCenter.default
            .publisher(for: UIResponder.keyboardWillHideNotification)
            .map { _ in CGFloat(0) }
        
        return MergeMany(willShow, willHide)
            .eraseToAnyPublisher()
    }
} 