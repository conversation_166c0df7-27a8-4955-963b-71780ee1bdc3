import SwiftData
import Foundation
import UIKit

/// 功能可用性管理器
/// 根据当前SwiftData模型容器的配置，智能管理功能的可用性
@MainActor
class EAFeatureManager: ObservableObject {
    
    // MARK: - 功能可用性状态
    @Published var isAIFeaturesAvailable: Bool = false
    @Published var isPaymentFeaturesAvailable: Bool = false
    @Published var isAdvancedAnalyticsAvailable: Bool = false
    @Published var isHabitPathsAvailable: Bool = false
    @Published var isContentLibraryAvailable: Bool = false
    @Published var isNotificationSettingsAvailable: Bool = false
    @Published var isProMember: Bool = false
    @Published var availableFeatures: Set<String> = []
    
    // MARK: - 系统信息
    @Published var currentIOSVersion: String = ""
    
    // MARK: - 模型可用性
    private var availableModels: Set<String> = []
    private var modelContainer: ModelContainer?
    
    init() {
        loadFeatureConfiguration()
    }
    
    // MARK: - 配置方法
    
    /// 根据模型容器配置功能可用性
    func configureFeatures(with modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        detectSystemInfo()
        detectAvailableModels(in: modelContainer)
        updateFeatureAvailability()
        logFeatureStatus()
    }
    
    /// 检测系统信息
    private func detectSystemInfo() {
        currentIOSVersion = UIDevice.current.systemVersion
        print("📱 系统信息: iOS \(currentIOSVersion)")
    }
    
    /// 检测可用的模型
    private func detectAvailableModels(in container: ModelContainer) {
        availableModels.removeAll()
        
        // 检测各个模型是否可用
        let modelTypes: [(String, any PersistentModel.Type)] = [
            ("EAUser", EAUser.self),
            ("EAHabit", EAHabit.self),
            ("EACompletion", EACompletion.self),
            ("EAUserSettings", EAUserSettings.self),
            ("EAAIMessage", EAAIMessage.self),
            ("EAPayment", EAPayment.self),
            ("EAContent", EAContent.self),
            ("EAAnalytics", EAAnalytics.self),
            ("EAPath", EAPath.self)
        ]
        
        for (name, modelType) in modelTypes {
            if isModelAvailable(modelType, in: container) {
                availableModels.insert(name)
            }
        }
    }
    
    /// 检查特定模型是否在容器中可用
    private func isModelAvailable(_ modelType: any PersistentModel.Type, in container: ModelContainer) -> Bool {
        do {
            let context = container.mainContext
            
            // 根据模型类型创建相应的FetchDescriptor
            switch String(describing: modelType) {
            case "EAUser":
                let descriptor = FetchDescriptor<EAUser>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAHabit":
                let descriptor = FetchDescriptor<EAHabit>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EACompletion":
                let descriptor = FetchDescriptor<EACompletion>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAUserSettings":
                let descriptor = FetchDescriptor<EAUserSettings>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAAIMessage":
                let descriptor = FetchDescriptor<EAAIMessage>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAPayment":
                let descriptor = FetchDescriptor<EAPayment>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAContent":
                let descriptor = FetchDescriptor<EAContent>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAAnalytics":
                let descriptor = FetchDescriptor<EAAnalytics>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            case "EAPath":
                let descriptor = FetchDescriptor<EAPath>(predicate: #Predicate { _ in false })
                _ = try context.fetch(descriptor)
                return true
            default:
                // 未知模型类型
                print("⚠️ 未知模型类型: \(String(describing: modelType))")
                return false
            }
        } catch {
            // 模型检测失败，静默处理
            return false
        }
    }
    
    /// 更新功能可用性
    private func updateFeatureAvailability() {
        // 核心模型检查
        let hasCoreModels = availableModels.contains("EAUser") && 
                           availableModels.contains("EAHabit") && 
                           availableModels.contains("EACompletion")
        
        // 所有功能在iOS 17.0+上都应该完全可用
        // 只有在模型真正不可用时才禁用功能
        
        // AI功能：需要AI对话模型
        isAIFeaturesAvailable = availableModels.contains("EAAIMessage")
        
        // 支付功能：需要支付记录模型
        isPaymentFeaturesAvailable = availableModels.contains("EAPayment")
        
        // 高级分析：需要用户分析模型
        isAdvancedAnalyticsAvailable = availableModels.contains("EAAnalytics")
        
        // 习惯路径：需要路径相关模型
        isHabitPathsAvailable = availableModels.contains("EAPath")
        
        // 内容库：需要内容模型
        isContentLibraryAvailable = availableModels.contains("EAContent")
        
        // 通知设置：需要通知设置模型
        isNotificationSettingsAvailable = availableModels.contains("EAUserSettings")
        
        // 如果核心模型不可用，这是严重问题，需要报告
        if !hasCoreModels {
            print("🚨 严重错误：核心模型不可用，这可能是数据损坏或配置错误")
        }
    }
    
    /// 记录功能状态
    private func logFeatureStatus() {
        print("📊 功能可用性状态:")
        print("   🤖 AI功能: \(isAIFeaturesAvailable ? "✅" : "❌")")
        print("   💳 支付功能: \(isPaymentFeaturesAvailable ? "✅" : "❌")")
        print("   📈 高级分析: \(isAdvancedAnalyticsAvailable ? "✅" : "❌")")
        print("   🛤️ 习惯路径: \(isHabitPathsAvailable ? "✅" : "❌")")
        print("   📚 内容库: \(isContentLibraryAvailable ? "✅" : "❌")")
        print("   🔔 通知设置: \(isNotificationSettingsAvailable ? "✅" : "❌")")
        print("   📦 可用模型: \(availableModels.sorted().joined(separator: ", "))")
        print("   📱 系统版本: iOS \(currentIOSVersion) - 完全兼容SwiftData")
        
        // 如果有模型不可用，提供诊断信息
        let allExpectedModels: Set<String> = [
            "EAUser", "EAHabit", "EACompletion", "EAAIMessage",
            "EAPayment", "EAContent", "EAPath", "EAAnalytics",
            "EAUserSettings"
        ]
        
        let missingModels = allExpectedModels.subtracting(availableModels)
        if !missingModels.isEmpty {
            print("⚠️ 缺失模型: \(missingModels.sorted().joined(separator: ", "))")
            print("💡 建议：检查模型定义和容器配置")
        }
    }
    
    // MARK: - 功能检查方法
    
    /// 检查AI功能是否可用
    func checkAIFeatures() -> Bool {
        return isAIFeaturesAvailable
    }
    
    /// 检查支付功能是否可用
    func checkPaymentFeatures() -> Bool {
        return isPaymentFeaturesAvailable
    }
    
    /// 检查分析功能是否可用
    func checkAnalyticsFeatures() -> Bool {
        return isAdvancedAnalyticsAvailable
    }
    
    /// 检查习惯路径功能是否可用
    func checkHabitPaths() -> Bool {
        return isHabitPathsAvailable
    }
    
    /// 检查内容库功能是否可用
    func checkContentLibrary() -> Bool {
        return isContentLibraryAvailable
    }
    
    /// 检查通知设置功能是否可用
    func checkNotificationSettings() -> Bool {
        return isNotificationSettingsAvailable
    }
    
    /// 获取功能可用性详情
    func getFeatureAvailabilityDetails() -> [String: Any] {
        return [
            "ai": isAIFeaturesAvailable,
            "payment": isPaymentFeaturesAvailable,
            "analytics": isAdvancedAnalyticsAvailable,
            "habitPaths": isHabitPathsAvailable,
            "content": isContentLibraryAvailable,
            "notifications": isNotificationSettingsAvailable,
            "system": [
                "version": currentIOSVersion,
                "swiftDataSupported": true // iOS 17.0+都支持SwiftData
            ],
            "availableModels": Array(availableModels),
            "totalModels": availableModels.count
        ]
    }
    
    // MARK: - 状态信息方法
    
    /// 获取AI功能的状态信息
    func getAIStatusMessage() -> String {
        if isAIFeaturesAvailable {
            return "AI功能完全可用"
        } else {
            return "AI功能暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取分析功能的状态信息
    func getAnalyticsStatusMessage() -> String {
        if isAdvancedAnalyticsAvailable {
            return "高级分析功能完全可用"
        } else {
            return "高级分析功能暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取内容库的状态信息
    func getContentLibraryStatusMessage() -> String {
        if isContentLibraryAvailable {
            return "智慧宝库完全可用"
        } else {
            return "智慧宝库暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取支付功能的状态信息
    func getPaymentStatusMessage() -> String {
        if isPaymentFeaturesAvailable {
            return "Pro功能完全可用"
        } else {
            return "Pro功能暂时不可用，请检查数据配置"
        }
    }
    
    /// 获取功能状态信息（智能版本）
    func getFeatureStatusMessage(for feature: String) -> String {
        switch feature {
        case "ai":
            return getAIStatusMessage()
        case "analytics":
            return getAnalyticsStatusMessage()
        case "content":
            return getContentLibraryStatusMessage()
        case "payment":
            return getPaymentStatusMessage()
        case "paths":
            return isHabitPathsAvailable ? "习惯路径功能可用" : "习惯路径功能暂时不可用，请检查数据配置"
        case "notifications":
            return isNotificationSettingsAvailable ? "高级通知设置可用" : "高级通知设置暂时不可用，请检查数据配置"
        default:
            return "功能状态未知"
        }
    }
    
    /// 是否所有功能都可用
    var allFeaturesAvailable: Bool {
        return isAIFeaturesAvailable && 
               isPaymentFeaturesAvailable && 
               isAdvancedAnalyticsAvailable && 
               isHabitPathsAvailable && 
               isContentLibraryAvailable && 
               isNotificationSettingsAvailable
    }
    
    /// 获取整体状态信息
    var overallStatusMessage: String {
        if allFeaturesAvailable {
            return "所有功能在iOS \(currentIOSVersion)上完全可用"
        } else {
            let availableCount = [
                isAIFeaturesAvailable,
                isPaymentFeaturesAvailable,
                isAdvancedAnalyticsAvailable,
                isHabitPathsAvailable,
                isContentLibraryAvailable,
                isNotificationSettingsAvailable
            ].filter { $0 }.count
            
            return "\(availableCount)/6 个功能模块可用，其余功能可能需要检查数据配置"
        }
    }
    
    // MARK: - 功能可用性检查扩展
    
    /// 安全执行AI相关操作
    func safeExecuteAIOperation<T>(_ operation: () async throws -> T, fallback: () -> T) async -> T {
        if isAIFeaturesAvailable {
            do {
                return try await operation()
            } catch {
                print("AI操作失败: \(error)")
                return fallback()
            }
        } else {
            print("AI功能不可用，使用降级方案")
            return fallback()
        }
    }
    
    /// 安全执行支付相关操作
    func safeExecutePaymentOperation<T>(_ operation: () async throws -> T, fallback: () -> T) async -> T {
        if isPaymentFeaturesAvailable {
            do {
                return try await operation()
            } catch {
                print("支付操作失败: \(error)")
                return fallback()
            }
        } else {
            print("支付功能不可用，使用降级方案")
            return fallback()
        }
    }
    
    // MARK: - 功能检查
    
    func isFeatureEnabled(_ feature: String) -> Bool {
        return availableFeatures.contains(feature)
    }
    
    func enableProFeatures() {
        isProMember = true
        availableFeatures.formUnion([
            "unlimited_habits",
            "ai_coaching",
            "advanced_analytics",
            "custom_themes",
            "data_export"
        ])
    }
    
    func disableProFeatures() {
        isProMember = false
        availableFeatures = Set([
            "basic_habits",
            "simple_tracking"
        ])
    }
    
    // MARK: - 配置加载
    
    private func loadFeatureConfiguration() {
        // 基础功能始终可用
        availableFeatures = Set([
            "basic_habits",
            "simple_tracking"
        ])
        
        // 检查Pro会员状态
        checkProMembershipStatus()
    }
    
    private func checkProMembershipStatus() {
        // 这里可以添加Pro会员状态检查逻辑
        // 暂时设为false
        isProMember = false
    }
    
    // MARK: - 数据模型验证
    
    func validateDataModels() -> Bool {
        let modelTypes: [(String, any PersistentModel.Type)] = [
            ("EAUser", EAUser.self),
            ("EAHabit", EAHabit.self),
            ("EACompletion", EACompletion.self),
            ("EAUserSettings", EAUserSettings.self),
            ("EAAIMessage", EAAIMessage.self),
            ("EAPayment", EAPayment.self),
            ("EAContent", EAContent.self),
            ("EAAnalytics", EAAnalytics.self),
            ("EAPath", EAPath.self)
        ]
        
        // 验证所有模型类型是否正确注册
        for (name, type) in modelTypes {
            print("✅ 验证模型: \(name) - \(type)")
        }
        
        return true
    }
    
    // MARK: - 数据库健康检查
    
    func performDatabaseHealthCheck(context: ModelContext) -> Bool {
        // 检查核心模型
        do {
            let descriptor = FetchDescriptor<EAUser>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            print("❌ EAUser模型检查失败: \(error)")
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EACompletion>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            print("❌ EACompletion模型检查失败: \(error)")
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EAAIMessage>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            print("❌ EAAIMessage模型检查失败: \(error)")
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EAPayment>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            print("❌ EAPayment模型检查失败: \(error)")
            return false
        }
        
        // 检查独立模型
        do {
            let descriptor = FetchDescriptor<EAPath>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            print("❌ EAPath模型检查失败: \(error)")
            return false
        }
        
        do {
            let descriptor = FetchDescriptor<EAAnalytics>(predicate: #Predicate { _ in false })
            _ = try context.fetch(descriptor)
        } catch {
            print("❌ EAAnalytics模型检查失败: \(error)")
            return false
        }
        
        print("✅ 数据库健康检查通过")
        return true
    }
} 