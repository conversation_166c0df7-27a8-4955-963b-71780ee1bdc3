import Foundation
import SwiftData

// MARK: - 宇宙探索挑战模型
@Model
final class EAUniverseChallenge {
    var id: UUID = UUID()
    var title: String
    var challengeDescription: String
    var targetHabitCategory: String
    var startDate: Date
    var endDate: Date
    var creationDate: Date = Date()
    var stellarReward: Int = 0
    var participantCount: Int = 0
    var maxParticipants: Int = 1000
    var isActive: Bool = true
    
    // ✅ 正确：SwiftData关系而非外键
    @Relationship(deleteRule: .cascade, inverse: \EAUniverseChallengeParticipation.challenge)
    var participations: [EAUniverseChallengeParticipation]
    
    // 挑战类型和规则
    var challengeType: String = "habit_completion" // habit_completion, streak_goal, community_goal
    var targetValue: Int = 7 // 目标天数或次数
    var difficulty: String = "normal" // easy, normal, hard, extreme
    
    // 宇宙主题属性
    var universeRegion: String = "银河系"
    var challengeBadge: String = "explorer_badge"
    
    // 挑战状态管理
    var status: String = "upcoming" // upcoming, active, completed, expired
    var completionRate: Double = 0.0 // 整体完成率
    
    // 数字宇宙扩展属性
    var cosmicDifficulty: Int = 1 // 1-5 宇宙难度等级
    var energyMultiplier: Double = 1.0 // 能量倍数
    var specialRewards: [String] = [] // 特殊奖励列表
    
    // Day 9新增：业务逻辑支持属性
    var currentState: String = "upcoming" // 当前状态（与status同步）
    var isArchived: Bool = false // 是否已归档
    
    init(title: String, challengeDescription: String, targetHabitCategory: String, startDate: Date, endDate: Date, stellarReward: Int = 100) {
        self.title = title
        self.challengeDescription = challengeDescription
        self.targetHabitCategory = targetHabitCategory
        self.startDate = startDate
        self.endDate = endDate
        self.stellarReward = stellarReward
        self.specialRewards = []
        self.participations = [] // ✅ 在init中初始化关系集合
    }
    
    // MARK: - 挑战状态管理方法
    
    /// 检查挑战是否已开始
    var hasStarted: Bool {
        Date() >= startDate
    }
    
    /// 检查挑战是否已结束
    var hasEnded: Bool {
        Date() > endDate
    }
    
    /// 获取挑战剩余时间（秒）
    var remainingTime: TimeInterval {
        max(0, endDate.timeIntervalSinceNow)
    }
    
    /// 获取挑战持续时间（天）
    var durationInDays: Int {
        Calendar.current.dateComponents([.day], from: startDate, to: endDate).day ?? 0
    }
    
    /// 更新挑战状态
    func updateStatus() {
        if hasEnded {
            status = "completed"
        } else if hasStarted {
            status = "active"
        } else {
            status = "upcoming"
        }
    }
    
    /// 计算基础星际能量奖励
    func calculateBaseStellarReward() -> Int {
        let baseReward = stellarReward
        let difficultyMultiplier: Double
        
        switch difficulty {
        case "easy":
            difficultyMultiplier = 1.0
        case "normal":
            difficultyMultiplier = 1.5
        case "hard":
            difficultyMultiplier = 2.0
        case "extreme":
            difficultyMultiplier = 3.0
        default:
            difficultyMultiplier = 1.0
        }
        
        return Int(Double(baseReward) * difficultyMultiplier * energyMultiplier)
    }
    
    /// 获取挑战难度描述
    var difficultyDescription: String {
        switch difficulty {
        case "easy":
            return "新手探索者"
        case "normal":
            return "星际旅者"
        case "hard":
            return "宇宙领航员"
        case "extreme":
            return "传奇征服者"
        default:
            return "未知等级"
        }
    }
    
    /// 获取宇宙区域描述
    var universeRegionDescription: String {
        switch universeRegion {
        case "银河系":
            return "🌌 银河系边缘"
        case "仙女座":
            return "✨ 仙女座星云"
        case "猎户座":
            return "⭐ 猎户座星团"
        case "天鹅座":
            return "🦢 天鹅座星域"
        default:
            return "🌟 未知星域"
        }
    }
    
    /// 检查是否可以参与
    func canParticipate() -> Bool {
        return isActive && !hasEnded && participantCount < maxParticipants
    }
    
    /// 增加参与者数量
    func addParticipant() {
        if canParticipate() {
            participantCount += 1
        }
    }
    
    /// 更新完成率
    func updateCompletionRate(_ rate: Double) {
        completionRate = max(0.0, min(1.0, rate))
    }
    
    /// 获取挑战类型描述
    var challengeTypeDescription: String {
        switch challengeType {
        case "habit_completion":
            return "习惯完成挑战"
        case "streak_goal":
            return "连续完成目标"
        case "community_goal":
            return "社区协作目标"
        default:
            return "未知挑战类型"
        }
    }
    
    /// 获取挑战徽章图标
    var badgeIcon: String {
        switch challengeBadge {
        case "explorer_badge":
            return "star.circle.fill"
        case "warrior_badge":
            return "shield.fill"
        case "master_badge":
            return "crown.fill"
        case "legend_badge":
            return "sparkles"
        default:
            return "star.fill"
        }
    }
}

// MARK: - 挑战参与记录模型
@Model
final class EAUniverseChallengeParticipation {
    var id: UUID = UUID()
    var joinDate: Date = Date()
    var currentProgress: Int = 0
    var isCompleted: Bool = false
    var completionDate: Date?
    var earnedReward: Int = 0
    
    // 参与状态
    var status: String = "active" // active, completed, abandoned
    var progressPercentage: Double = 0.0
    
    // 数字宇宙扩展
    var stellarEnergyEarned: Int = 0
    var milestoneRewards: [String] = []
    var personalBest: Bool = false
    
    // Day 9新增：业务逻辑支持属性
    var lastUpdateDate: Date = Date()
    var achievedMilestones: [Int] = [] // 已达成的里程碑百分比
    var finalRanking: Int = 0 // 最终排名
    var isPerfectCompletion: Bool = false // 是否完美完成
    var isArchived: Bool = false // 是否已归档
    
    // ✅ 正确：SwiftData关系而非外键
    var user: EAUser?
    var challenge: EAUniverseChallenge?
    
    init() {
        self.milestoneRewards = []
        self.achievedMilestones = []
    }
    
    /// 更新进度
    func updateProgress(_ progress: Int, targetValue: Int) {
        currentProgress = progress
        progressPercentage = min(1.0, Double(progress) / Double(targetValue))
        lastUpdateDate = Date()
        
        if progress >= targetValue && !isCompleted {
            markAsCompleted()
        }
    }
    
    /// 标记为完成
    func markAsCompleted() {
        isCompleted = true
        completionDate = Date()
        status = "completed"
        lastUpdateDate = Date()
    }
    
    /// 添加里程碑奖励
    func addMilestoneReward(_ reward: String) {
        if !milestoneRewards.contains(reward) {
            milestoneRewards.append(reward)
        }
    }
    
    /// 获取进度描述
    var progressDescription: String {
        return "\(currentProgress) / \(Int(progressPercentage * 100))%"
    }
}

// MARK: - 挑战类型枚举
enum EAChallengeType: String, CaseIterable {
    case habitCompletion = "habit_completion"
    case streakGoal = "streak_goal"
    case communityGoal = "community_goal"
    
    var displayName: String {
        switch self {
        case .habitCompletion:
            return "习惯完成挑战"
        case .streakGoal:
            return "连续完成目标"
        case .communityGoal:
            return "社区协作目标"
        }
    }
    
    var icon: String {
        switch self {
        case .habitCompletion:
            return "checkmark.circle.fill"
        case .streakGoal:
            return "flame.fill"
        case .communityGoal:
            return "person.3.fill"
        }
    }
}

// MARK: - 挑战难度枚举
enum EAChallengeDifficulty: String, CaseIterable {
    case easy = "easy"
    case normal = "normal"
    case hard = "hard"
    case extreme = "extreme"
    
    var displayName: String {
        switch self {
        case .easy:
            return "新手探索者"
        case .normal:
            return "星际旅者"
        case .hard:
            return "宇宙领航员"
        case .extreme:
            return "传奇征服者"
        }
    }
    
    var multiplier: Double {
        switch self {
        case .easy:
            return 1.0
        case .normal:
            return 1.5
        case .hard:
            return 2.0
        case .extreme:
            return 3.0
        }
    }
    
    var color: String {
        switch self {
        case .easy:
            return "green"
        case .normal:
            return "blue"
        case .hard:
            return "purple"
        case .extreme:
            return "red"
        }
    }
} 