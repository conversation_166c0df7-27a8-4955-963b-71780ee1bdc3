//
//  EAUserSocialProfile.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData

/// 用户社交档案模型
/// 负责管理用户的社交关系数据，从EAUser模型中分离出来以降低复杂度
/// 遵循开发规范文档的"数据模型复杂度控制规范"
@Model
final class EAUserSocialProfile: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // MARK: - 核心关系
    
    /// 关联的用户（SwiftData标准关系）
    var user: EAUser?
    
    // 注意：关注关系已移至Repository查询模式，不再直接维护关系
    
    /// 关注用户的人（一对多）- 作为被关注者  
    @Relationship(deleteRule: .cascade, inverse: \EACommunityFollow.followeeProfile)
    var followers: [EACommunityFollow]
    
    /// ✅ 修复：添加posts关系的inverse定义，解决发布帖子崩溃问题
    /// 用户发布的帖子（一对多关系）- 通过社交档案管理
    @Relationship(deleteRule: .cascade, inverse: \EACommunityPost.authorSocialProfile)
    var posts: [EACommunityPost]
    
    // 注意：关注关系已移至Repository查询模式，不再直接维护关系
    
    // MARK: - 社交统计属性
    
    /// 关注数量缓存
    var followingCount: Int = 0
    
    /// 粉丝数量缓存
    var followersCount: Int = 0
    
    /// 最后活跃时间
    var lastActiveDate: Date = Date()
    
    /// 社交活跃度分数
    var socialActivityScore: Double = 0.0
    
    // MARK: - 数字宇宙星际探索者属性（兼容性扩展，所有属性为可选类型）
    
    /// 星际等级 (1-20级) - 可选类型确保兼容性
    var stellarLevel: Int? = nil
    
    /// 星际能量总数 - 可选类型确保兼容性
    var totalStellarEnergy: Int? = nil
    
    /// 探索者称号 (新手探索者、星际旅者、宇宙守护者等) - 可选类型确保兼容性
    var explorerTitle: String? = nil
    
    /// 所属宇宙区域 (银河系、仙女座、猎户座等) - 可选类型确保兼容性
    var universeRegion: String? = nil
    
    /// 宇宙探索徽章列表 (JSON编码的徽章ID数组) - 可选类型确保兼容性
    var explorationBadges: [String]? = nil
    
    /// 星际能量每日获得记录 (用于计算连续活跃天数) - 可选类型确保兼容性
    var dailyEnergyHistory: [String]? = nil
    
    /// 宇宙贡献值 (分享内容、帮助他人等获得) - 可选类型确保兼容性
    var cosmicContribution: Int? = nil
    
    /// 探索里程碑达成日期记录 - 可选类型确保兼容性
    var explorationMilestones: [String]? = nil
    
    // MARK: - 初始化
    
    init() {
        self.followers = []
        self.posts = []
    }
    
    // MARK: - 社交功能便捷方法
    
    /// 获取用户关注的人数（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserFollowing(userId:) 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserFollowing(userId:) 替代")
    func getFollowingCount() -> Int {
        return 0 // 返回默认值，实际需要通过Repository查询
    }
    
    /// 获取关注用户的人数
    func getFollowersCount() -> Int {
        return followers.filter { $0.isActive }.count
    }
    
    /// 获取用户发布的可见帖子数量（需要通过Repository查询）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserPosts(userId:) 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserPosts(userId:) 替代")
    func getVisiblePostsCount() -> Int {
        return 0 // 返回默认值，实际需要通过Repository查询
    }
    
    /// 检查是否关注了某个用户（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.isUserFollowing 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.isUserFollowing 替代")
    func isFollowing(userId: UUID) -> Bool {
        return false // 返回默认值，实际需要通过Repository查询
    }
    
    /// 检查是否被某个用户关注
    /// ✅ 修复：使用关系链访问，而非外键模式
    func isFollowedBy(userId: UUID) -> Bool {
        return followers.contains { $0.followerProfile?.user?.id == userId && $0.isActive }
    }
    
    /// 更新社交统计数据
    func updateSocialStats() {
        // 注意：关注数需要通过Repository查询，这里使用缓存值
        // followingCount 应该通过 EACommunityRepository.fetchUserFollowing 更新
        followersCount = getFollowersCount()
        lastUpdateDate = Date()
        
        // 计算社交活跃度分数（不包含帖子数，需要单独通过Repository计算）
        socialActivityScore = Double(followingCount) * 1.0 + Double(followersCount) * 1.5
    }
    
    /// 获取互相关注的用户（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchMutualFollows 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchMutualFollows 替代")
    func getMutualFollows() -> [EACommunityFollow] {
        return [] // 返回空数组，实际需要通过Repository查询
    }
    
    /// 获取最近关注的用户（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserFollowing 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserFollowing 替代")
    func getRecentFollowing(limit: Int = 10) -> [EACommunityFollow] {
        return [] // 返回空数组，实际需要通过Repository查询
    }
    
    /// 获取最近的粉丝
    func getRecentFollowers(limit: Int = 10) -> [EACommunityFollow] {
        return followers
            .filter { $0.isActive }
            .sorted { $0.creationDate > $1.creationDate }
            .prefix(limit)
            .map { $0 }
    }
    
    /// 获取最近的帖子（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.fetchUserPosts(userId:) 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.fetchUserPosts(userId:) 替代")
    func getRecentPosts(limit: Int = 10) -> [EACommunityPost] {
        return [] // 返回空数组，实际需要通过Repository查询
    }
    
    // MARK: - 安全关系操作方法
    
    /// 安全添加关注关系（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.createFollow 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.createFollow 替代")
    @MainActor
    func safelyAddFollowing(_ follow: EACommunityFollow, in context: ModelContext) throws {
        // 方法已弃用，不执行任何操作
        throw DataModelError.methodDeprecated
    }
    
    /// 安全添加粉丝关系
    @MainActor
    func safelyAddFollower(_ follow: EACommunityFollow, in context: ModelContext) throws {
        guard follow.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.followers.append(follow)
        updateSocialStats()
        try context.save()
    }
    
    /// 安全添加帖子（已弃用，请使用Repository）
    /// 注意：此方法已弃用，请使用 EACommunityRepository.createPost 替代
    @available(*, deprecated, message: "使用 EACommunityRepository.createPost 替代")
    @MainActor
    func safelyAddPost(_ post: EACommunityPost, in context: ModelContext) throws {
        // 方法已弃用，不执行任何操作
        throw DataModelError.methodDeprecated
    }
    
    // MARK: - 数字宇宙星际系统方法
    
    /// 初始化数字宇宙数据（兼容性方法）
    func initializeDigitalUniverseData() {
        guard stellarLevel == nil else { return } // 避免重复初始化
        
        stellarLevel = 1
        totalStellarEnergy = 0
        explorerTitle = "新手探索者"
        universeRegion = "银河系边缘"
        explorationBadges = []
        dailyEnergyHistory = []
        cosmicContribution = 0
        explorationMilestones = []
    }
    
    /// 获得星际能量
    func gainStellarEnergy(_ amount: Int) {
        // 确保数字宇宙数据已初始化
        initializeDigitalUniverseData()
        
        let currentEnergy = totalStellarEnergy ?? 0
        totalStellarEnergy = currentEnergy + amount
        recordDailyEnergyChange(amount, reason: "习惯完成")
        checkLevelUp()
    }
    
    /// ✅ 新增：记录每日能量变化（支持增加和减少）
    func recordDailyEnergyChange(_ amount: Int, reason: String) {
        initializeDigitalUniverseData()
        
        let today = ISO8601DateFormatter().string(from: Date())
        var history = dailyEnergyHistory ?? []
        
        // 记录格式：日期:变化量:原因
        let record = "\(today):\(amount):\(reason)"
        history.append(record)
        
        // 保留最近30天的记录
        if history.count > 30 {
            history.removeFirst()
        }
        dailyEnergyHistory = history
        
        // 如果是正数，检查升级
        if amount > 0 {
            checkLevelUp()
        }
    }
    
    /// 记录每日能量获得（保持向后兼容）
    private func recordDailyEnergyGain(_ amount: Int) {
        recordDailyEnergyChange(amount, reason: "习惯完成")
    }
    
    /// 检查是否应该升级星际等级
    private func checkLevelUp() {
        let newLevel = calculateStellarLevel()
        let currentLevel = stellarLevel ?? 1
        if newLevel > currentLevel {
            stellarLevel = newLevel
            updateExplorerTitle()
        }
    }
    
    /// 根据星际能量计算等级
    private func calculateStellarLevel() -> Int {
        // 星际等级算法：每1000能量升1级，最高20级
        let energy = totalStellarEnergy ?? 0
        let baseLevel = min(energy / 1000, 19) + 1
        return baseLevel
    }
    
    /// 更新探索者称号
    private func updateExplorerTitle() {
        let currentLevel = stellarLevel ?? 1
        switch currentLevel {
        case 1...3:
            explorerTitle = "新手探索者"
        case 4...7:
            explorerTitle = "星际旅者"
        case 8...12:
            explorerTitle = "宇宙航行者"
        case 13...16:
            explorerTitle = "星系守护者"
        case 17...20:
            explorerTitle = "宇宙大师"
        default:
            explorerTitle = "传奇探索者"
        }
    }
    
    /// 添加探索徽章
    func addExplorationBadge(_ badgeId: String) {
        initializeDigitalUniverseData()
        
        var badges = explorationBadges ?? []
        if !badges.contains(badgeId) {
            badges.append(badgeId)
        }
        explorationBadges = badges
    }
    
    /// 记录探索里程碑
    func recordExplorationMilestone(_ milestone: String) {
        initializeDigitalUniverseData()
        
        let timestamp = ISO8601DateFormatter().string(from: Date())
        var milestones = explorationMilestones ?? []
        milestones.append("\(timestamp):\(milestone)")
        explorationMilestones = milestones
    }
    
    /// 获取连续活跃天数
    func getConsecutiveActiveDays() -> Int {
        guard let history = dailyEnergyHistory else { return 0 }
        
        let dateFormatter = ISO8601DateFormatter()
        let today = Date()
        var consecutiveDays = 0
        
        // 从最近的记录往前计算连续天数
        for record in history.reversed() {
            guard let colonIndex = record.firstIndex(of: ":"),
                  let date = dateFormatter.date(from: String(record[..<colonIndex])) else {
                continue
            }
            
            let daysDiff = Calendar.current.dateComponents([.day], from: date, to: today).day ?? 0
            if daysDiff == consecutiveDays {
                consecutiveDays += 1
            } else {
                break
            }
        }
        
        return consecutiveDays
    }
    
    /// 计算星际影响力 (综合社交和宇宙数据)
    func calculateStellarInfluence() -> Double {
        let socialScore = socialActivityScore
        let energyScore = Double(totalStellarEnergy ?? 0) / 100.0
        let levelBonus = Double(stellarLevel ?? 1) * 10.0
        let contributionScore = Double(cosmicContribution ?? 0)
        
        return socialScore + energyScore + levelBonus + contributionScore
    }
} 