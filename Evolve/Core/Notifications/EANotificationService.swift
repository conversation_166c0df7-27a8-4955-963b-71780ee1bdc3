import Foundation
import UserNotifications
import UIKit
import SwiftData

// MARK: - 通知服务
@MainActor
class EANotificationService: ObservableObject {
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    private let notificationCenter = UNUserNotificationCenter.current()
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    /// ✅ 修复：添加sessionManager依赖注入
    init(sessionManager: EASessionManager) {
        self.sessionManager = sessionManager
        // 检查当前授权状态
        Task {
            await checkAuthorizationStatus()
        }
    }
    
    // MARK: - 权限管理
    
    /// 请求通知权限
    func requestNotificationPermission() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(
                options: [.alert, .sound, .badge]
            )
            await checkAuthorizationStatus()
            return granted
        } catch {
            // 错误处理：记录权限请求失败
            await checkAuthorizationStatus()
            return false
        }
    }
    
    /// 检查当前授权状态
    func checkAuthorizationStatus() async {
        let settings = await notificationCenter.notificationSettings()
        authorizationStatus = settings.authorizationStatus
    }
    
    /// 打开系统设置页面
    func openNotificationSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    // MARK: - 习惯提醒调度
    
    /// 为习惯安排提醒通知（单个时间 - 向后兼容）
    func scheduleHabitReminder(
        habitId: UUID,
        habitName: String,
        reminderTime: Date,
        isEnabled: Bool
    ) async {
        await scheduleMultipleHabitReminders(
            habitId: habitId,
            habitName: habitName,
            reminderTimes: [reminderTime],
            isEnabled: isEnabled,
            isPushNotificationEnabled: true
        )
    }
    
    /// 为习惯安排多个提醒通知（新功能）
    func scheduleMultipleHabitReminders(
        habitId: UUID,
        habitName: String,
        reminderTimes: [Date],
        isEnabled: Bool,
        isPushNotificationEnabled: Bool
    ) async {
        // 先移除旧的通知
        await removeHabitReminder(habitId: habitId)
        
        // 如果未启用提醒，直接返回
        guard isEnabled else { return }
        
        // 如果不启用推送通知，记录AI应用内提醒
        if !isPushNotificationEnabled {
            await scheduleAIInAppReminders(
                habitId: habitId,
                habitName: habitName,
                reminderTimes: reminderTimes
            )
            return
        }
        
        // 检查权限
        guard authorizationStatus == .authorized else {
            // 权限未授权，无法安排系统推送提醒
            return
        }
        
        // 为每个时间创建通知
        for (index, reminderTime) in reminderTimes.enumerated() {
            await scheduleSingleReminder(
                habitId: habitId,
                habitName: habitName,
                reminderTime: reminderTime,
                index: index
            )
        }
        
        // 成功安排习惯多时间提醒
    }
    
    /// 安排单个提醒通知
    private func scheduleSingleReminder(
        habitId: UUID,
        habitName: String,
        reminderTime: Date,
        index: Int
    ) async {
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "计划提醒"
        content.body = "该完成「\(habitName)」了！坚持就是胜利 💪"
        content.sound = .default
        content.badge = 1
        
        // 设置用户信息，用于处理通知点击
        content.userInfo = [
            "type": "habit_reminder",
            "habitId": habitId.uuidString,
            "habitName": habitName,
            "reminderIndex": index
        ]
        
        // 创建时间触发器（每天重复）
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: reminderTime)
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: components,
            repeats: true
        )
        
        // 创建通知请求（使用索引区分多个提醒）
        let identifier = "habit_reminder_\(habitId.uuidString)_\(index)"
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: trigger
        )
        
        // 添加通知
        do {
            try await notificationCenter.add(request)
            // 成功安排单个习惯提醒
        } catch {
            // 安排习惯提醒失败，静默处理
            // 通知调度失败，静默处理
        }
    }
    
    /// 安排AI应用内提醒
    private func scheduleAIInAppReminders(
        habitId: UUID,
        habitName: String,
        reminderTimes: [Date]
    ) async {
        // 将AI应用内提醒信息存储到UserDefaults或发送通知给AI系统
        let reminderData: [String: Any] = [
            "habitId": habitId.uuidString,
            "habitName": habitName,
            "reminderTimes": reminderTimes.map { DateFormatter.shortTime.string(from: $0) },
            "type": "ai_in_app_reminder"
        ]
        
        // 发送通知给AI系统
        NotificationCenter.default.post(
            name: .aiInAppReminderScheduled,
            object: nil,
            userInfo: reminderData
        )
        
        // 成功安排AI应用内提醒
    }
    
    /// 移除习惯提醒（向后兼容方法）
    func removeHabitReminder(habitId: UUID) async {
        await removeHabitReminders(habitId: habitId)
    }
    
    /// 移除所有习惯提醒
    func removeAllHabitReminders() async {
        let pendingRequests = await notificationCenter.pendingNotificationRequests()
        let habitReminderIds = pendingRequests
            .filter { $0.identifier.hasPrefix("habit_reminder_") }
            .map { $0.identifier }
        
        notificationCenter.removePendingNotificationRequests(withIdentifiers: habitReminderIds)
        // 移除所有习惯提醒完成
    }
    
    /// 获取待处理的通知数量
    func getPendingNotificationCount() async -> Int {
        let pendingRequests = await notificationCenter.pendingNotificationRequests()
        return pendingRequests.filter { $0.identifier.hasPrefix("habit_reminder_") }.count
    }
    
    /// 获取所有习惯的提醒总数（Repository模式）
    func getTotalReminderCount(repositoryContainer: EARepositoryContainer?) async -> Int {
        // 使用Repository模式获取活跃习惯
        if let repositories = repositoryContainer,
           let currentUser = sessionManager.currentUser {
            let activeHabits = await (try? repositories.habitRepository.fetchActiveHabits(for: currentUser.id)) ?? []
            
            // 计算所有习惯的提醒总数
            let totalReminders = activeHabits.reduce(0) { total, habit in
                return total + habit.reminderTimes.count
            }
            
            return totalReminders
        } else {
            // 降级：通过SessionManager获取当前用户的习惯
            if let currentUser = sessionManager.currentUser {
                let activeHabits = currentUser.habits.filter { $0.isActive }
                let totalReminders = activeHabits.reduce(0) { total, habit in
                    return total + habit.reminderTimes.count
                }
                return totalReminders
            }
            return 0
        }
    }
    
    /// 获取所有习惯的提醒总数（包括AI应用内提醒）- 保持向后兼容
    func getTotalReminderCount() async -> Int {
        // 获取系统推送提醒数量
        let systemReminders = await getPendingNotificationCount()
        
        // 获取AI应用内提醒数量（从UserDefaults或其他存储中读取）
        let aiReminders = getAIInAppReminderCount()
        
        return systemReminders + aiReminders
    }
    
    /// 为习惯安排多个提醒时间（新版本）
    func scheduleMultipleHabitReminders(for habit: EAHabit, times: [Date]) async {
        // 先移除该习惯的所有现有提醒
        await removeHabitReminders(habitId: habit.id)
        
        // 检查权限
        let authorizationStatus = await requestNotificationPermission()
        guard authorizationStatus else {
            // 通知权限未授权，无法安排系统推送提醒
            return
        }
        
        // 为每个时间创建通知
        for (index, reminderTime) in times.enumerated() {
            await scheduleSingleReminder(
                habitId: habit.id,
                habitName: habit.name,
                reminderTime: reminderTime,
                index: index
            )
        }
        
        // 成功安排习惯多时间提醒
    }
    
    /// 为习惯安排AI应用内提醒
    func scheduleAIInAppReminders(for habit: EAHabit, times: [Date]) async {
        // 将AI应用内提醒信息存储到UserDefaults或发送通知给AI系统
        let reminderData: [String: Any] = [
            "habitId": habit.id.uuidString,
            "habitName": habit.name,
            "reminderTimes": times.map { DateFormatter.shortTime.string(from: $0) },
            "type": "ai_in_app_reminder"
        ]
        
        // 发送通知给AI系统
        NotificationCenter.default.post(
            name: .aiInAppReminderScheduled,
            object: nil,
            userInfo: reminderData
        )
        
        // 成功安排AI应用内提醒
    }
    
    /// 移除习惯的所有提醒（支持多个提醒）
    func removeHabitReminders(habitId: UUID) async {
        // 获取所有待处理的通知
        let pendingRequests = await notificationCenter.pendingNotificationRequests()
        
        // 找到所有与该习惯相关的通知标识符
        let habitReminderIds = pendingRequests
            .filter { $0.identifier.hasPrefix("habit_reminder_\(habitId.uuidString)") }
            .map { $0.identifier }
        
        // 移除所有相关通知
        notificationCenter.removePendingNotificationRequests(withIdentifiers: habitReminderIds)
        
        // 同时移除AI应用内提醒
        NotificationCenter.default.post(
            name: .aiInAppReminderRemoved,
            object: nil,
            userInfo: ["habitId": habitId.uuidString]
        )
        
        // 移除习惯提醒完成
    }
    
    /// 获取AI应用内提醒数量
    private func getAIInAppReminderCount() -> Int {
        // 这里可以从UserDefaults或其他存储中读取AI提醒数量
        // 暂时返回0，实际实现时需要根据存储方式调整
        return UserDefaults.standard.integer(forKey: "ai_reminder_count")
    }
    
    /// 更新AI应用内提醒数量
    func updateAIInAppReminderCount(_ count: Int) {
        UserDefaults.standard.set(count, forKey: "ai_reminder_count")
    }
    
    // MARK: - 通知处理
    
    /// 处理通知点击事件
    func handleNotificationResponse(_ response: UNNotificationResponse) {
        let userInfo = response.notification.request.content.userInfo
        
        guard let type = userInfo["type"] as? String,
              type == "habit_reminder",
              let habitIdString = userInfo["habitId"] as? String,
              let habitId = UUID(uuidString: habitIdString) else {
            return
        }
        
        // 处理通知点击事件，跳转到特定习惯页面
        // 发送通知给其他组件
        NotificationCenter.default.post(
            name: .habitReminderTapped,
            object: nil,
            userInfo: ["habitId": habitId]
        )
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let habitReminderTapped = Notification.Name("habitReminderTapped")
    static let aiInAppReminderScheduled = Notification.Name("aiInAppReminderScheduled")
    static let aiInAppReminderRemoved = Notification.Name("aiInAppReminderRemoved")
}

// MARK: - DateFormatter扩展
extension DateFormatter {
    static let shortTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
} 