import Foundation
import SwiftData

// MARK: - Repository错误类型
enum EARepositoryError: Error, LocalizedError {
    case userNotFound
    case habitNotFound
    case dataNotFound
    case saveFailed
    case deleteError
    case concurrencyError
    case contextMismatch  // iOS 18.2新增：上下文不匹配错误
    
    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "用户未找到"
        case .habitNotFound:
            return "习惯未找到"
        case .dataNotFound:
            return "数据未找到"
        case .saveFailed:
            return "保存失败"
        case .deleteError:
            return "删除失败"
        case .concurrencyError:
            return "并发操作错误"
        case .contextMismatch:
            return "数据上下文不匹配，请重试"
        }
    }
}

// MARK: - 用户数据访问协议
protocol EAUserRepository {
    func fetchUser(id: UUID) async throws -> EAUser?
    func fetchCurrentUser() async throws -> EAUser?
    func saveUser(_ user: EAUser) async throws
    func deleteUser(_ user: EAUser) async throws
    func createUser(username: String, email: String?) async throws -> EAUser
    
    // 🔑 头像数据管理方法
    func updateUserAvatar(userId: UUID, avatarData: EAAvatarData?) async throws
    
    // AI数据桥接所需方法
    func fetchUser(by userId: UUID) async -> EAUser?
    func fetchUserSettings(userId: UUID) async -> EAUserSettings?
    
    // 认证相关方法
    func validateCredentials(phoneNumber: String, password: String) async throws -> EAUser?
    func createUserWithAuth(username: String, email: String?, phoneNumber: String, password: String) async throws -> EAUser
    func fetchUserByPhone(phoneNumber: String) async throws -> EAUser?
    func fetchUserByEmail(email: String) async throws -> EAUser?
    
    // 测试数据管理方法
    #if DEBUG
    func deleteAllTestUsers() async throws
    #endif
}

// MARK: - 用户设置数据访问协议
protocol EAUserSettingsRepository {
    func fetchSettings(for userID: UUID) async throws -> EAUserSettings?
    func saveSettings(_ settings: EAUserSettings) async throws
    func createDefaultSettings(for userID: UUID) async throws -> EAUserSettings
}

// MARK: - 习惯数据访问协议
protocol EAHabitRepository {
    func fetchHabits(for userID: UUID) async throws -> [EAHabit]
    func fetchActiveHabits(for userID: UUID) async throws -> [EAHabit]
    func fetchHabit(id: UUID) async throws -> EAHabit?
    func saveHabit(_ habit: EAHabit) async throws
    func deleteHabit(_ habit: EAHabit) async throws
    func createHabit(
        name: String, 
        iconName: String, 
        targetFrequency: Int, 
        frequencyType: String,
        category: String,
        difficulty: String,
        for userID: UUID
    ) async throws -> EAHabit
    
    /// iOS 18.2安全创建方法：完整的习惯创建流程，确保上下文一致性
    func createHabitSafely(
        name: String,
        iconName: String,
        targetFrequency: Int,
        frequencyType: String,
        selectedWeekdays: [Int],
        dailyTarget: Int,
        monthlyTarget: Int,
        monthlyMode: String,
        selectedMonthlyDates: [Int],
        category: String,
        difficulty: String,
        reminderTimes: [String],
        reminderEnabled: Bool,
        preferredTimeSlot: String?,
        for userID: UUID
    ) async throws -> EAHabit
    
    // AI数据桥接所需方法
    func fetchUserHabits(userId: UUID) async -> [EAHabit]
    func fetchRecentCompletions(userId: UUID, days: Int) async -> [EACompletion]
}

// MARK: - 完成记录数据访问协议
protocol EACompletionRepository {
    func fetchCompletions(for habitID: UUID) async throws -> [EACompletion]
    func fetchTodayCompletions(for userID: UUID) async throws -> [EACompletion]
    func fetchCompletions(for userID: UUID, date: Date) async throws -> [EACompletion]
    func saveCompletion(_ completion: EACompletion) async throws
    func deleteCompletion(_ completion: EACompletion) async throws
    func createCompletion(for habitID: UUID, note: String?, energyLevel: Int) async throws -> EACompletion
    
    /// ✅ 新增：获取用户最近的完成记录（用于星际能量计算）
    func fetchRecentCompletions(userId: UUID, days: Int) async throws -> [EACompletion]
}

// MARK: - AI对话数据访问协议
protocol EAAIMessageRepository {
    func fetchMessages(for userID: UUID, limit: Int) async throws -> [EAAIMessage]
    func fetchRecentMessages(for userID: UUID) async throws -> [EAAIMessage]
    func saveMessage(_ message: EAAIMessage) async throws
    func deleteOldMessages(for userID: UUID, keepRecent: Int) async throws
    func createMessage(userID: UUID, userMessage: String, aiResponse: String, type: String) async throws -> EAAIMessage
}

// MARK: - 支付记录数据访问协议
protocol EAPaymentRepository {
    func fetchPayments(for userID: UUID) async throws -> [EAPayment]
    func fetchActivePayments(for userID: UUID) async throws -> [EAPayment]
    func savePayment(_ payment: EAPayment) async throws
    func createPayment(userID: UUID, productId: String, transactionId: String, expirationDate: Date?) async throws -> EAPayment
}

// MARK: - 分析数据访问协议
protocol EAAnalyticsRepository {
    func fetchAnalytics(for userID: UUID, eventType: String?) async throws -> [EAAnalytics]
    func saveAnalytics(_ analytics: EAAnalytics) async throws
    func trackEvent(userID: UUID, eventType: String, eventData: [String: Any]) async throws
    func deleteOldAnalytics(for userID: UUID, olderThan: Date) async throws
}

// MARK: - 内容数据访问协议
protocol EAContentRepository {
    func fetchContents(type: String?, isPro: Bool?) async throws -> [EAContent]
    func fetchContent(id: UUID) async throws -> EAContent?
    func saveContent(_ content: EAContent) async throws
    func createContent(title: String, content: String, type: String, isPro: Bool) async throws -> EAContent
}

// MARK: - 路径数据访问协议
protocol EAPathRepository {
    func fetchPaths(for userID: UUID) async throws -> [EAPath]
    func fetchPath(id: UUID) async throws -> EAPath?
    func savePath(_ path: EAPath) async throws
    func deletePath(_ path: EAPath) async throws
    func createPath(userID: UUID, goalTitle: String, totalStages: Int) async throws -> EAPath
}

// MARK: - 统一Repository接口
@MainActor
protocol EARepositoryContainer {
    var userRepository: EAUserRepository { get }
    var userSettingsRepository: EAUserSettingsRepository { get }
    var habitRepository: EAHabitRepository { get }
    var completionRepository: EACompletionRepository { get }
    var aiMessageRepository: EAAIMessageRepository { get }
    var paymentRepository: EAPaymentRepository { get }
    var analyticsRepository: EAAnalyticsRepository { get }
    var contentRepository: EAContentRepository { get }
    var pathRepository: EAPathRepository { get }
    var communityRepository: EACommunityRepositoryProtocol { get }
    var challengeRepository: EAUniverseChallengeRepository { get }
    
    /// 社区AI数据桥接服务（Phase 2新增）
    var communityAIDataBridge: EACommunityAIDataBridge { get }

    // MARK: - AI数据桥接接口

    /// 获取用户习惯数据摘要（为AI分析准备）
    func getUserHabitSummary(userId: UUID) async throws -> EAAIHabitSummary?

    /// 获取用户社区活动摘要（为AI分析准备）
    func getUserCommunityActivitySummary(userId: UUID) async throws -> EAAICommunityActivitySummary?
    
    func getCurrentUser() async throws -> EAUser?
} 