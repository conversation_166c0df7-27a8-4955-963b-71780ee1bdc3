import SwiftData
import Foundation

// MARK: - 数据模型迁移管理

enum EAModelMigration {
    
    // MARK: - 当前Schema版本
    
    static var currentSchema: Schema {
        Schema([
            EAUser.self,
            EAUserSettings.self,
            EAHabit.self,
            EACompletion.self,
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self
        ])
    }
    
    // MARK: - 迁移策略
    
    static func performMigrationIfNeeded() throws {
        // 检查是否需要数据迁移
        // 当前版本为v3.2，暂时不需要复杂的迁移逻辑
        print("✅ 数据模型迁移检查完成")
    }
    
    // MARK: - 数据验证
    
    static func validateDataIntegrity() throws {
        // 验证数据完整性
        print("✅ 数据完整性验证完成")
    }
}

// MARK: - SwiftData Schema版本管理（2025年修正版 - 临时简化）

/// Schema版本4：修正版（临时简化，避免循环引用）
enum EvolveSchemaV4: VersionedSchema {
    static var versionIdentifier = Schema.Version(4, 0, 0)
    
    static var models: [any PersistentModel.Type] {
        [
            EAUser.self,
            EAUserSettings.self,
            EAHabit.self,
            EACompletion.self,
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self
        ]
    }
}

/// 迁移计划（临时简化版）
enum EvolveModelMigrationPlan: SchemaMigrationPlan {
    static var schemas: [any VersionedSchema.Type] {
        [EvolveSchemaV4.self]
    }
    
    static var stages: [MigrationStage] {
        // 暂时不需要迁移阶段，使用全新Schema
        []
    }
}

// MARK: - ModelContainer扩展（2025年修正版）

extension ModelContainer {
    /// 创建兼容的容器（修正版 - 使用新架构）
    static func createCompatibleContainer() -> ModelContainer {
        do {
            let schema = Schema([
                EAUser.self,
                EAUserSettings.self,
                EAHabit.self,
                EACompletion.self,
                EAAIMessage.self,
                EAPayment.self,
                EAAnalytics.self,
                EAContent.self,
                EAPath.self
            ])
            
            let configuration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: false,
                allowsSave: true,
                cloudKitDatabase: .none
            )
            
            let container = try ModelContainer(
                for: schema,
                configurations: [configuration]
            )
            
            print("✅ 成功创建ModelContainer（新架构，包含9个模型）")
            return container
            
        } catch {
            // 创建ModelContainer失败，静默处理
            print("🔄 尝试创建降级容器...")
            return createFallbackContainer()
        }
    }
    
    /// 创建降级容器（内存模式）
    private static func createFallbackContainer() -> ModelContainer {
        do {
            let schema = Schema([
                EAUser.self,
                EAUserSettings.self,
                EAHabit.self,
                EACompletion.self,
                EAAIMessage.self,
                EAPayment.self,
                EAAnalytics.self,
                EAContent.self,
                EAPath.self
            ])
            
            let configuration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )
            
            let container = try ModelContainer(
                for: schema,
                configurations: [configuration]
            )
            
            print("⚠️ 使用内存模式降级容器（新架构，包含9个模型）")
            return container
            
        } catch {
            print("💥 创建降级容器也失败: \(error)")
            fatalError("无法创建任何ModelContainer")
        }
    }
}

// MARK: - 数据库重置管理器（2025年修正版）

class EADatabaseResetManager {
    init() {}
    
    private let currentSchemaVersion = "5.0.0"
    private let schemaVersionKey = "EASchemaVersion"
    private let forceResetKey = "EAForceReset_2025"
    
    /// 检查是否需要重置数据库
    func resetDatabaseIfNeeded() {
        let savedVersion = UserDefaults.standard.string(forKey: schemaVersionKey)
        let forceReset = UserDefaults.standard.bool(forKey: forceResetKey)
        
        // 强制重置（开发环境）
        #if DEBUG
        if !forceReset {
            print("🔄 [SCHEMA_FIX] 开发环境强制重置数据库...")
            forceResetDatabase()
            UserDefaults.standard.set(true, forKey: forceResetKey)
            return
        }
        #endif
        
        // 版本不匹配时重置
        if savedVersion != currentSchemaVersion {
            print("🔄 Schema版本变更，重置数据库: \(savedVersion ?? "nil") -> \(currentSchemaVersion)")
            forceResetDatabase()
        }
    }
    
    /// 强制重置数据库
    func forceResetDatabase() {
        let fileManager = FileManager.default
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, 
                                                  in: .userDomainMask).first else { 
            // 无法访问应用支持目录
            return 
        }
        
        let storeFiles = ["default.store", "default.store-wal", "default.store-shm"]
        
        for fileName in storeFiles {
            let fileURL = appSupportURL.appendingPathComponent(fileName)
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    try fileManager.removeItem(at: fileURL)
                    print("🗑️ [SCHEMA_FIX] 已删除: \(fileName)")
                } catch {
                    print("⚠️ [SCHEMA_FIX] 删除失败 \(fileName): \(error)")
                }
            }
        }
        
        // 更新版本标记
        UserDefaults.standard.set(currentSchemaVersion, forKey: schemaVersionKey)
        print("✅ [SCHEMA_FIX] 数据库重置完成，准备使用正确的关系设计")
    }
    
    /// 获取当前Schema版本
    func getCurrentSchemaVersion() -> String {
        return currentSchemaVersion
    }
    
    /// 打印数据库状态（调试用）
    func printDatabaseStatus() {
        let savedVersion = UserDefaults.standard.string(forKey: schemaVersionKey)
        print("📊 数据库状态:")
        print("   当前Schema版本: \(currentSchemaVersion)")
        print("   保存的版本: \(savedVersion ?? "未设置")")
        print("   强制重置标记: \(UserDefaults.standard.bool(forKey: forceResetKey))")
    }
}

// MARK: - 开发环境数据库管理工具

#if DEBUG
extension EADatabaseResetManager {
    /// 开发环境：强制重置数据库（用于Schema修正）
    func devForceResetForSchemaFix() {
        print("🔧 [DEV] 开发环境强制重置数据库（Schema修正）")
        forceResetDatabase()
        
        // 清除所有相关标记
        UserDefaults.standard.removeObject(forKey: forceResetKey)
        UserDefaults.standard.removeObject(forKey: "EADatabaseInitialized")
        
        print("✅ [DEV] 开发环境重置完成")
    }
    
    /// 开发环境：打印详细数据库信息
    func devPrintDetailedStatus() {
        print("🔍 [DEV] 详细数据库状态:")
        printDatabaseStatus()
        
        let fileManager = FileManager.default
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, 
                                                  in: .userDomainMask).first else { return }
        
        let storeFiles = ["default.store", "default.store-wal", "default.store-shm"]
        
        print("📁 数据库文件状态:")
        for fileName in storeFiles {
            let fileURL = appSupportURL.appendingPathComponent(fileName)
            let exists = fileManager.fileExists(atPath: fileURL.path)
            print("   \(fileName): \(exists ? "存在" : "不存在")")
            
            if exists {
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                    let size = attributes[.size] as? Int64 ?? 0
                    let modificationDate = attributes[.modificationDate] as? Date
                    print("     大小: \(size) bytes")
                    print("     修改时间: \(modificationDate?.description ?? "未知")")
                } catch {
                    print("     无法获取文件属性: \(error)")
                }
            }
        }
    }
}
#endif