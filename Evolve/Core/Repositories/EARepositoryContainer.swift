//
//  EARepositoryContainer.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData
import SwiftUI

/// Repository容器实现
/// 统一管理所有Repository实例，提供依赖注入支持
@MainActor
final class EARepositoryContainerImpl: EARepositoryContainer, ObservableObject {
    
    // MARK: - Repository实例
    
    private let _userRepository: EAUserRepository
    private let _userSettingsRepository: EAUserSettingsRepository
    private let _habitRepository: EAHabitRepository
    private let _completionRepository: EACompletionRepository
    private let _aiMessageRepository: EAAIMessageRepository
    private let _paymentRepository: EAPaymentRepository
    private let _analyticsRepository: EAAnalyticsRepository
    private let _contentRepository: EAContentRepository
    private let _pathRepository: EAPathRepository
    private let _communityRepository: EACommunityRepositoryProtocol
    private let _challengeRepository: EAUniverseChallengeRepository
    
    // MARK: - AI数据桥接服务（Phase 2新增）
    
    private lazy var _communityAIDataBridge: EACommunityAIDataBridge = {
        return EACommunityAIDataBridge(repositoryContainer: self)
    }()
    
    // MARK: - AI增强功能服务（Phase 2 Day 4新增）
    
    private lazy var _aiEnhancementService: EAAIEnhancementService = {
        return EAAIEnhancementService(repositoryContainer: self, aiDataBridge: self._communityAIDataBridge, cacheManager: EAAICacheManager())
    }()
    
    // MARK: - 宇宙向导服务（Phase 2 Day 5新增）
    
    private lazy var _universeGuideService: EAUniverseGuideService = {
        return EAUniverseGuideService(repositoryContainer: self, aiDataBridge: self._communityAIDataBridge, cacheManager: EAAICacheManager())
    }()
    
    // MARK: - 星际能量服务（Phase 3 Day 6新增）
    
    private lazy var _stellarEnergyService: EAStellarEnergyService = {
        return EAStellarEnergyService(repositoryContainer: self, cacheManager: EAAICacheManager())
    }()
    
    // MARK: - 公开接口
    
    var userRepository: EAUserRepository { _userRepository }
    var userSettingsRepository: EAUserSettingsRepository { _userSettingsRepository }
    var habitRepository: EAHabitRepository { _habitRepository }
    var completionRepository: EACompletionRepository { _completionRepository }
    var aiMessageRepository: EAAIMessageRepository { _aiMessageRepository }
    var paymentRepository: EAPaymentRepository { _paymentRepository }
    var analyticsRepository: EAAnalyticsRepository { _analyticsRepository }
    var contentRepository: EAContentRepository { _contentRepository }
    var pathRepository: EAPathRepository { _pathRepository }
    var communityRepository: EACommunityRepositoryProtocol { _communityRepository }
    var challengeRepository: EAUniverseChallengeRepository { _challengeRepository }
    
    /// 社区AI数据桥接服务访问接口（Phase 2新增）
    var communityAIDataBridge: EACommunityAIDataBridge { _communityAIDataBridge }
    
    /// AI增强功能服务访问接口（Phase 2 Day 4新增）
    var aiEnhancementService: EAAIEnhancementService { _aiEnhancementService }
    
    /// 宇宙向导服务访问接口（Phase 2 Day 5新增）
    var universeGuideService: EAUniverseGuideService { _universeGuideService }
    
    /// 星际能量服务访问接口（Phase 3 Day 6新增）
    var stellarEnergyService: EAStellarEnergyService { _stellarEnergyService }
    
    // MARK: - 初始化
    
    /// 初始化Repository容器
    /// - Parameter modelContainer: SwiftData模型容器
    init(modelContainer: ModelContainer) {
        // ✅ 新增：Context一致性验证
        #if DEBUG
        let containerContext = modelContainer.mainContext
        print("🔧 创建Repository容器，使用Container Context ID: \(ObjectIdentifier(containerContext))")
        #endif
        
        // 创建所有Repository实例，确保使用同一个ModelContainer
        self._userRepository = EAUserRepositoryImpl(modelContainer: modelContainer)
        self._userSettingsRepository = EASwiftDataUserSettingsRepository(modelContainer: modelContainer)
        self._habitRepository = EASwiftDataHabitRepository(modelContainer: modelContainer)
        self._completionRepository = EASwiftDataCompletionRepository(modelContainer: modelContainer)
        self._aiMessageRepository = EASwiftDataAIMessageRepository(modelContainer: modelContainer)
        self._paymentRepository = EASwiftDataPaymentRepository(modelContainer: modelContainer)
        self._analyticsRepository = EASwiftDataAnalyticsRepository(modelContainer: modelContainer)
        self._contentRepository = EASwiftDataContentRepository(modelContainer: modelContainer)
        self._pathRepository = EASwiftDataPathRepository(modelContainer: modelContainer)
        // ✅ 修复：EACommunityRepository移除sessionManager依赖，Repository应该是纯净的数据访问层
        self._communityRepository = EACommunityRepository(modelContainer: modelContainer)
        self._challengeRepository = EAUniverseChallengeRepository(modelContainer: modelContainer)
        
        // ✅ 新增：验证所有Repository使用同一Container
        #if DEBUG
        validateRepositoryContainerConsistency(modelContainer)
        #endif
        
        // EACommunityAIDataBridge 使用 lazy 延迟初始化，避免循环依赖
    }
    
    /// ✅ 新增：验证Repository Container一致性
    #if DEBUG
    private func validateRepositoryContainerConsistency(_ expectedContainer: ModelContainer) {
        let expectedContextId = ObjectIdentifier(expectedContainer.mainContext)
        print("🔍 验证Repository Container一致性，期望Context ID: \(expectedContextId)")
        
        // 验证关键Repository的Container一致性
        // 注意：@ModelActor的modelContext是通过Container创建的，应该保持一致
        print("✅ 所有Repository已使用共享ModelContainer创建")
    }
    #endif
    
    // MARK: - 用户管理
    
    /// 获取当前用户
    /// - Returns: 当前用户实例，如果不存在则返回nil
    func getCurrentUser() async throws -> EAUser? {
        // 这里可以实现获取当前用户的逻辑
        // 例如从UserDefaults获取当前用户ID，然后通过userRepository获取用户
        return try await userRepository.fetchCurrentUser()
    }
    
    // MARK: - AI数据桥接接口（为阶段六开发准备）
    
    /// 获取用户习惯数据摘要（为AI分析准备）
    func getUserHabitSummary(userId: UUID) async throws -> EAAIHabitSummary? {
        guard await userRepository.fetchUser(by: userId) != nil else { return nil }
        
        let habits = await habitRepository.fetchUserHabits(userId: userId)
        let recentCompletions = await habitRepository.fetchRecentCompletions(userId: userId, days: 7)
        
        return EAAIHabitSummary(
            userId: userId,
            totalHabits: habits.count,
            activeHabits: habits.filter { $0.isActive }.count,
            recentCompletionRate: calculateCompletionRate(habits: habits, completions: recentCompletions),
            mostSuccessfulHabit: findMostSuccessfulHabit(habits: habits, completions: recentCompletions),
            strugglingHabits: findStrugglingHabits(habits: habits, completions: recentCompletions),
            analysisTimestamp: Date()
        )
    }
    
    /// 获取用户社区活动摘要（为AI分析准备）
    func getUserCommunityActivitySummary(userId: UUID) async throws -> EAAICommunityActivitySummary? {
        guard await userRepository.fetchUser(by: userId) != nil else { return nil }
        
        // 使用现有的fetchPosts方法获取最近的帖子
        let recentPosts = try await communityRepository.fetchPosts(limit: 50, offset: 0)
        // 过滤该用户的帖子
        let userPosts = recentPosts.filter { $0.getAuthor()?.id == userId }
        
        return EAAICommunityActivitySummary(
            userId: userId,
            totalPosts: userPosts.count,
            recentPostsCount: userPosts.filter { Calendar.current.isDate($0.creationDate, inSameDayAs: Date()) }.count,
            averageLikesPerPost: calculateAverageLikes(posts: userPosts),
            communityEngagementScore: calculateEngagementScore(posts: userPosts),
            analysisTimestamp: Date()
        )
    }
    
    // MARK: - AI数据分析辅助方法
    
    private func calculateCompletionRate(habits: [EAHabit], completions: [EACompletion]) -> Double {
        guard !habits.isEmpty else { return 0.0 }
        
        let last7Days = Calendar.current.dateInterval(of: .weekOfYear, for: Date())
        let recentCompletions = completions.filter { completion in
            guard let interval = last7Days else { return false }
            return interval.contains(completion.date)
        }
        
        let expectedCompletions = habits.filter { $0.isActive }.count * 7
        guard expectedCompletions > 0 else { return 0.0 }
        
        return Double(recentCompletions.count) / Double(expectedCompletions)
    }
    
    private func findMostSuccessfulHabit(habits: [EAHabit], completions: [EACompletion]) -> String? {
        let habitCompletionCounts = Dictionary(grouping: completions) { $0.habit?.id }
            .mapValues { $0.count }
        
        guard let (habitId, _) = habitCompletionCounts.max(by: { $0.value < $1.value }),
              let habit = habits.first(where: { $0.id == habitId }) else {
            return nil
        }
        
        return habit.name
    }
    
    private func findStrugglingHabits(habits: [EAHabit], completions: [EACompletion]) -> [String] {
        let last7Days = Calendar.current.dateInterval(of: .weekOfYear, for: Date())
        let recentCompletions = completions.filter { completion in
            guard let interval = last7Days else { return false }
            return interval.contains(completion.date)
        }
        
        let habitCompletionCounts = Dictionary(grouping: recentCompletions) { $0.habit?.id }
            .mapValues { $0.count }
        
        return habits.filter { habit in
            guard habit.isActive else { return false }
            let completionCount = habitCompletionCounts[habit.id] ?? 0
            return completionCount < 2 // 一周内完成次数少于2次视为困难
        }.map { $0.name }
    }
    
    private func calculateAverageLikes(posts: [EACommunityPost]) -> Double {
        guard !posts.isEmpty else { return 0.0 }
        let totalLikes = posts.reduce(into: 0) { $0 += $1.likeCount }
        return Double(totalLikes) / Double(posts.count)
    }
    
    private func calculateEngagementScore(posts: [EACommunityPost]) -> Double {
        let postsScore = Double(posts.count) * 10.0
        let likesScore = Double(posts.reduce(into: 0) { $0 += $1.likeCount }) * 2.0
        return postsScore + likesScore
    }
}

// MARK: - Environment支持

/// Environment扩展，支持Repository容器依赖注入
extension EnvironmentValues {
    /// Repository容器环境值
    /// ✅ 修复：使用可选类型避免MainActor线程安全问题
    @Entry var repositoryContainer: EARepositoryContainer? = nil
}

// MARK: - 视图扩展：便捷方法

extension View {
    
    /// 注入Repository容器到环境
    /// - Parameter repositoryContainer: Repository容器实例
    /// - Returns: 配置了Repository容器的视图
    func repositoryContainer(_ repositoryContainer: EARepositoryContainer) -> some View {
        self.environment(\.repositoryContainer, repositoryContainer)
    }
}

// MARK: - 便利构造器

extension EARepositoryContainerImpl {
    
    /// 创建用于预览的Repository容器
    /// - Returns: 配置了内存存储的Repository容器
    static func preview() -> EARepositoryContainerImpl {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try! ModelContainer(for: 
            EAUser.self,
            EAUserSettings.self,
            EAHabit.self,
            EACompletion.self,
            EAUserSocialProfile.self,
            EAUserModerationProfile.self,
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self,
            EACommunityPost.self,
            EACommunityComment.self,
            EACommunityLike.self,
            EACommunityFollow.self,
            EACommunityReport.self,
            configurations: config
        )
        return EARepositoryContainerImpl(modelContainer: container)
    }
} 