import Foundation
import SwiftData
import SwiftUI

/// 社区核心业务服务
/// 负责处理社区相关的所有业务逻辑，包括帖子CRUD、点赞评论、搜索等功能
/// 严格遵循项目Repository架构规范，通过Repository层访问数据
@MainActor
@Observable
final class EACommunityService {
    
    // MARK: - 属性
    
    /// 社区数据仓库
    private let repository: EACommunityRepositoryProtocol
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    /// 分页配置
    private let defaultPageSize: Int = 20
    
    /// 错误状态
    var lastError: CommunityServiceError?
    
    // MARK: - 初始化
    
    /// ✅ 修复：添加sessionManager依赖注入
    /// - Parameters:
    ///   - repository: 社区数据仓库
    ///   - sessionManager: 会话管理器
    init(repository: EACommunityRepositoryProtocol, sessionManager: EASessionManager) {
        self.repository = repository
        self.sessionManager = sessionManager
    }
    
    /// ✅ 修复：更新便利构造器，移除sessionManager参数传递给Repository
    /// - Parameters:
    ///   - modelContext: SwiftData模型上下文
    ///   - sessionManager: 会话管理器
    convenience init(modelContext: ModelContext, sessionManager: EASessionManager) {
        // ✅ 修复：Repository不再需要sessionManager，保持纯净的数据访问层
        let repository = EARepositoryFactory.createCommunityRepository(modelContext: modelContext)
        self.init(repository: repository, sessionManager: sessionManager)
    }
    
    // MARK: - 帖子管理
    
    /// 创建新帖子
    /// - Parameters:
    ///   - content: 帖子内容
    ///   - habitName: 相关习惯名称（可选）
    ///   - imageURLs: 图片URL数组（可选）
    /// - Returns: 创建的帖子
    func createPost(
        content: String,
        habitName: String? = nil,
        imageURLs: [String] = []
    ) async throws -> EACommunityPost {
        do {
            // 创建帖子对象
            let post = EACommunityPost(
                content: content,
                habitName: habitName,
                category: habitName != nil ? "achievement" : "general",
                energyLevel: 5
            )
            
            // 设置图片URL
            post.imageURLs = imageURLs
            
            // 获取当前用户ID
            guard let currentUserId = sessionManager.currentUser?.id else {
                throw CommunityServiceError.userNotFound
            }
            
            // 通过Repository创建帖子
            let createdPost = try await repository.createPost(post, authorId: currentUserId)
            
            return createdPost
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .postCreationFailed(repoError.localizedDescription)
            } else {
                serviceError = .postCreationFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 获取帖子列表（分页）
    /// - Parameters:
    ///   - page: 页码（从0开始）
    ///   - pageSize: 每页数量
    /// - Returns: 帖子列表
    func fetchPosts(page: Int = 0, pageSize: Int? = nil) async throws -> [EACommunityPost] {
        do {
            let size = pageSize ?? defaultPageSize
            let offset = page * size
            
            // 通过Repository获取帖子
            let posts = try await repository.fetchPosts(limit: size, offset: offset)
            
            // 过滤可见帖子
            return posts.filter { $0.isVisible }
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .fetchFailed(repoError.localizedDescription)
            } else {
                serviceError = .fetchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 搜索帖子
    /// - Parameter query: 搜索关键词
    /// - Returns: 匹配的帖子列表
    func searchPosts(query: String) async throws -> [EACommunityPost] {
        do {
            guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                return []
            }
            
            // 通过Repository搜索帖子
            let posts = try await repository.searchPosts(query: query, limit: 50)
            
            // 过滤可见帖子
            return posts.filter { $0.isVisible }
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .searchFailed(repoError.localizedDescription)
            } else {
                serviceError = .searchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 删除帖子
    /// - Parameter post: 要删除的帖子
    func deletePost(_ post: EACommunityPost) async throws {
        do {
            // ✅ 修复：使用依赖注入的sessionManager替代单例
            guard let currentUserId = sessionManager.currentUser?.id,
                  post.getAuthor()?.id == currentUserId else {
                throw CommunityServiceError.insufficientPermissions
            }
            
            // 软删除：设置为不可见
            post.isVisible = false
            post.lastEditDate = Date()
            
            // 通过Repository更新帖子
            _ = try await repository.updatePost(post)
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .deletionFailed(repoError.localizedDescription)
            } else {
                serviceError = .deletionFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    // MARK: - 点赞管理
    
    /// 切换帖子点赞状态
    /// - Parameter post: 目标帖子
    /// - Returns: 当前点赞状态
    func togglePostLike(_ post: EACommunityPost) async throws -> Bool {
        do {
            // ✅ 修复：使用依赖注入的sessionManager替代单例
            guard let currentUserId = sessionManager.currentUser?.id else {
                throw CommunityServiceError.userNotFound
            }
            
            // 通过Repository切换点赞状态
            let isLiked = try await repository.toggleLike(postId: post.id, userId: currentUserId)
            
            return isLiked
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .likeFailed(repoError.localizedDescription)
            } else {
                serviceError = .likeFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 检查用户是否点赞了指定帖子
    /// - Parameter post: 目标帖子
    /// - Returns: 是否已点赞
    func checkPostLikeStatus(_ post: EACommunityPost) async throws -> Bool {
        do {
            // ✅ 修复：使用依赖注入的sessionManager替代单例
            guard let currentUserId = sessionManager.currentUser?.id else {
                return false
            }
            
            // 🔑 修复：避免多层可选链，使用安全的比较方式
            let likes = try await repository.fetchLikes(for: post.id)
            return likes.contains { like in
                guard let likeUserId = like.user?.id else { return false }
                return likeUserId == currentUserId
            }
        } catch {
            return false // 查询失败时默认为未点赞
        }
    }
    
    // MARK: - 评论管理
    
    /// 为帖子添加评论
    /// - Parameters:
    ///   - post: 目标帖子
    ///   - content: 评论内容
    ///   - replyToUsername: 回复的用户名（可选）
    /// - Returns: 创建的评论
    func addComment(
        to post: EACommunityPost,
        content: String,
        replyToUsername: String? = nil
    ) async throws -> EACommunityComment {
        do {
            // 获取当前用户ID
            guard let currentUserId = sessionManager.currentUser?.id else {
                throw CommunityServiceError.userNotFound
            }
            
            // 创建评论对象
            let comment = EACommunityComment(
                content: content,
                replyToUsername: replyToUsername
            )
            
            // 通过Repository创建评论
            let createdComment = try await repository.createComment(comment, for: post.id, authorId: currentUserId)
            
            return createdComment
        } catch let error as CommunityServiceError {
            lastError = error
            throw error
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .commentCreationFailed(repoError.localizedDescription)
            } else {
                serviceError = .commentCreationFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    
    /// 获取帖子的评论列表
    /// - Parameter post: 目标帖子
    /// - Returns: 评论列表
    func fetchComments(for post: EACommunityPost) async throws -> [EACommunityComment] {
        do {
            // 通过Repository获取评论
            let comments = try await repository.fetchComments(for: post.id)
            
            // 过滤可见评论
            return comments.filter { $0.isVisible }
        } catch {
            let serviceError: CommunityServiceError
            if let repoError = error as? EACommunityRepositoryError {
                serviceError = .fetchFailed(repoError.localizedDescription)
            } else {
                serviceError = .fetchFailed(error.localizedDescription)
            }
            lastError = serviceError
            throw serviceError
        }
    }
    

}

// MARK: - 错误定义

/// 社区服务错误类型
enum CommunityServiceError: LocalizedError {
    case userNotLoggedIn
    case userNotFound
    case insufficientPermissions
    case postNotFound
    case postCreationFailed(String)
    case deletionFailed(String)
    case fetchFailed(String)
    case searchFailed(String)
    case likeFailed(String)
    case commentCreationFailed(String)
    case networkError
    case dataCorruption
    
    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "用户未登录"
        case .userNotFound:
            return "用户不存在"
        case .insufficientPermissions:
            return "权限不足"
        case .postNotFound:
            return "帖子不存在"
        case .postCreationFailed(let details):
            return "创建帖子失败：\(details)"
        case .deletionFailed(let details):
            return "删除失败：\(details)"
        case .fetchFailed(let details):
            return "加载数据失败：\(details)"
        case .searchFailed(let details):
            return "搜索失败：\(details)"
        case .likeFailed(let details):
            return "点赞操作失败：\(details)"
        case .commentCreationFailed(let details):
            return "创建评论失败：\(details)"
        case .networkError:
            return "网络连接错误"
        case .dataCorruption:
            return "数据异常"
        }
    }
} 