import Foundation
import SwiftData
import SwiftUI

/// 内容审核服务
/// 负责处理社区内容的审核、过滤、举报管理等功能
/// 确保社区内容质量和用户体验安全
@MainActor
@Observable
final class EAContentModerationService {
    
    // MARK: - 属性
    
    /// Repository容器
    private var repositoryContainer: EARepositoryContainer?
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    /// 敏感词库（简化实现，实际应该从服务器获取）
    private let sensitiveWords: Set<String> = [
        "垃圾", "废物", "恶心", "白痴", "傻子", "死",
        "滚", "去死", "杀", "血", "暴力", "色情"
    ]
    
    /// 错误状态
    var lastError: ModerationError?
    
    // MARK: - 初始化
    
    /// ✅ 修复：添加sessionManager依赖注入
    /// - Parameters:
    ///   - sessionManager: 会话管理器
    ///   - repositoryContainer: Repository容器，用于数据访问
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer? = nil) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - 内容过滤
    
    /// 检查内容是否包含敏感词
    /// - Parameter content: 待检查的内容
    /// - Returns: 审核结果
    func moderateContent(_ content: String) -> ContentModerationResult {
        let cleanedContent = content.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查内容长度
        if cleanedContent.isEmpty {
            return .rejected(reason: "内容不能为空")
        }
        
        if cleanedContent.count < 2 {
            return .rejected(reason: "内容过短，至少需要2个字符")
        }
        
        if cleanedContent.count > 1000 {
            return .rejected(reason: "内容过长，不能超过1000个字符")
        }
        
        // 检查敏感词
        for sensitiveWord in sensitiveWords {
            if cleanedContent.contains(sensitiveWord) {
                return .flagged(reason: "内容包含不当词汇：\(sensitiveWord)")
            }
        }
        
        // 检查重复字符（防止灌水）
        if hasExcessiveRepeatedCharacters(cleanedContent) {
            return .flagged(reason: "内容包含过多重复字符")
        }
        
        // 检查是否为纯链接或广告
        if isLikelySpam(cleanedContent) {
            return .flagged(reason: "疑似垃圾信息")
        }
        
        return .approved
    }
    
    /// 检查是否包含过多重复字符
    /// - Parameter content: 内容
    /// - Returns: 是否包含过多重复字符
    private func hasExcessiveRepeatedCharacters(_ content: String) -> Bool {
        var charCount: [Character: Int] = [:]
        
        for char in content {
            charCount[char, default: 0] += 1
        }
        
        // 如果某个字符占总字符数的60%以上，认为是重复字符
        for (_, count) in charCount {
            if Double(count) / Double(content.count) > 0.6 {
                return true
            }
        }
        
        return false
    }
    
    /// 检查是否为垃圾信息
    /// - Parameter content: 内容
    /// - Returns: 是否为垃圾信息
    private func isLikelySpam(_ content: String) -> Bool {
        let spamKeywords = ["加微信", "QQ", "购买", "优惠", "打折", "免费", "赚钱", "兼职"]
        
        for keyword in spamKeywords {
            if content.contains(keyword) {
                return true
            }
        }
        
        // 检查是否包含过多链接
        let urlPattern = #"http[s]?://[^\s]+"#
        if let regex = try? NSRegularExpression(pattern: urlPattern) {
            let matches = regex.matches(in: content, range: NSRange(content.startIndex..., in: content))
            if matches.count > 2 {
                return true
            }
        }
        
        return false
    }
    
    // MARK: - 举报管理
    
    /// 创建举报
    /// - Parameters:
    ///   - targetId: 目标ID
    ///   - targetType: 目标类型（post、comment、user）
    ///   - reason: 举报原因
    ///   - description: 详细描述
    /// - Returns: 举报记录
    func createReport(
        targetId: UUID,
        targetType: String,
        reason: String,
        description: String?
    ) async throws -> EACommunityReport {
            
            // 创建举报记录
            let report = EACommunityReport(
                reportReason: reason,
                targetType: targetType,
            reportDescription: description
        )
        
        // 简化处理：不设置复杂的关系，只保存基本信息
        // 在实际应用中，这些关系应该通过Repository正确设置
            
            return report
    }
    
    /// 获取举报列表
    /// - Parameters:
    ///   - status: 举报状态（可选）
    ///   - limit: 限制数量
    /// - Returns: 举报列表
    func getReports(status: String? = nil, limit: Int = 50) async throws -> [EACommunityReport] {
        // 简化实现：返回空数组
        return []
    }
    
    /// 处理举报
    /// - Parameters:
    ///   - reportId: 举报ID
    ///   - action: 处理动作
    ///   - moderatorNote: 管理员备注
    func processReport(
        reportId: UUID,
        action: ModerationAction,
        moderatorNote: String?
    ) async throws {
        // 简化实现：不执行具体操作
    }
    
    /// 自动审核内容
    /// - Parameter targetId: 目标ID
    /// - Returns: 是否需要人工审核
    func autoModerateContent(targetId: UUID) async -> Bool {
        // 简化实现：总是返回需要人工审核
        return true
    }
    
    /// 执行内容动作
    /// - Parameters:
    ///   - targetId: 目标ID
    ///   - targetType: 目标类型
    ///   - action: 动作
    private func executeContentAction(
        targetId: UUID,
        targetType: String,
        action: ModerationAction
    ) async throws {
        // 简化实现：不执行具体操作
    }
    
    /// 获取审核统计
    /// - Returns: 审核统计信息
    func getModerationStats() async throws -> ModerationStats {
        // 简化实现：返回空统计
            return ModerationStats(
            pendingReports: 0,
            todayReports: 0,
            hiddenPosts: 0,
            hiddenComments: 0,
            totalReports: 0
        )
    }
    
    /// 获取当前用户
    /// - Returns: 当前用户
    private func getCurrentUser() async throws -> EAUser {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = sessionManager.currentUser else {
            throw ModerationError.userNotFound
        }
        return currentUser
    }
    
    /// 检查自动审核
    /// - Parameter report: 举报记录
    private func checkAutoModeration(for report: EACommunityReport) async {
        // 简化实现：不执行自动审核
    }
}

// MARK: - 数据类型定义

/// 内容审核结果
enum ContentModerationResult {
    case approved                    // 通过审核
    case flagged(reason: String)     // 标记为可疑
    case rejected(reason: String)    // 拒绝发布
    
    var isApproved: Bool {
        if case .approved = self {
            return true
        }
        return false
    }
    
    var reason: String? {
        switch self {
        case .approved:
            return nil
        case .flagged(let reason), .rejected(let reason):
            return reason
        }
    }
}

/// 审核动作
enum ModerationAction: String, CaseIterable {
    case approve = "approve"
    case reject = "reject"
    case hide = "hide"
    case delete = "delete"
    case warn = "warn"
    case suspend = "suspend"
}

/// 审核统计信息
struct ModerationStats {
    let pendingReports: Int     // 待处理举报数量
    let todayReports: Int       // 今日举报数量
    let hiddenPosts: Int        // 隐藏帖子数量
    let hiddenComments: Int     // 隐藏评论数量
    let totalReports: Int        // 总举报数量
}

/// 审核服务错误类型
enum ModerationError: LocalizedError {
    case userNotFound
    case reportCreationFailed(String)
    case fetchFailed(String)
    case processingFailed(String)
    case statsFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "用户未找到"
        case .reportCreationFailed(let details):
            return "创建举报失败：\(details)"
        case .fetchFailed(let details):
            return "获取数据失败：\(details)"
        case .processingFailed(let details):
            return "处理失败：\(details)"
        case .statsFailed(let details):
            return "统计数据获取失败：\(details)"
        }
    }
} 