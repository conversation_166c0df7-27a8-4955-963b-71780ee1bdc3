import Foundation
import StoreKit
import SwiftData

/// 支付服务 - 处理StoreKit支付和订阅管理
/// 遵循Repository模式，通过EARepositoryContainer访问数据
class EAPaymentService: ObservableObject {
    
    // Repository容器（可选，支持降级处理）
    private var repositoryContainer: EARepositoryContainer?
    
    // ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var error: PaymentError?
    @Published var errorMessage: String?
    
    // 会员状态相关属性
    @Published var isProMember: Bool = false
    @Published var currentSubscriptionProductID: String?
    @Published var products: [Product] = []
    
    /// ✅ 修复：添加sessionManager依赖注入
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer? = nil) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        // 初始化时检查会员状态
        checkMembershipStatus()
    }
    
    // MARK: - 会员状态管理
    
    /// 检查会员状态
    private func checkMembershipStatus() {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        Task { @MainActor in
            if sessionManager.currentUser != nil {
                // 这里可以通过用户数据或本地存储检查会员状态
                // 简化实现：默认为非会员
                self.isProMember = false
                self.currentSubscriptionProductID = nil
            }
        }
    }
    
    // MARK: - 产品管理
    
    /// 加载产品信息
    func loadProducts() async {
        await MainActor.run {
        isLoading = true
        errorMessage = nil
        }
        
        // 简化实现：创建模拟产品
        let mockProducts = createMockProducts()
        
        await MainActor.run {
            self.products = mockProducts
            self.isLoading = false
        }
    }
    
    /// 创建模拟产品（用于开发测试）
    private func createMockProducts() -> [Product] {
        // 简化实现：返回空数组
        // 在实际应用中，这里应该调用StoreKit API获取真实产品
        return []
    }
    
    // MARK: - 购买处理
    
    /// 购买产品
    /// - Parameter product: 要购买的产品
    /// - Returns: 购买结果
    func purchase(_ product: Product) async -> PurchaseResult {
        await MainActor.run {
        isLoading = true
        errorMessage = nil
        }
        
        do {
            // 简化实现：模拟购买成功
            let paymentRecord = try await processPayment(
                productId: product.id,
                transactionId: UUID().uuidString,
                amount: Decimal(0) // 简化实现
            )
                
                await MainActor.run {
                self.isProMember = true
                self.currentSubscriptionProductID = product.id
                    self.isLoading = false
            }
            
            return .success(paymentRecord)
        } catch {
            await MainActor.run {
                self.errorMessage = "购买失败：\(error.localizedDescription)"
                self.isLoading = false
            }
            return .failure(error)
        }
    }
    
    /// 恢复购买（主要方法）
    func restorePurchases() async {
        await MainActor.run {
        isLoading = true
        errorMessage = nil
        }
        
        do {
            // 简化实现：检查本地存储的购买记录
            let restoredProducts = try await getProductInfo(productIds: ["evolve_pro_monthly", "evolve_pro_yearly", "evolve_pro_lifetime"])
            
            await MainActor.run {
                if !restoredProducts.isEmpty {
                    self.isProMember = true
                    self.currentSubscriptionProductID = restoredProducts.first?.id
                } else {
                    self.errorMessage = "未找到可恢复的购买记录"
                }
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "恢复购买失败：\(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }
    
    // MARK: - 格式化方法
    
    /// 获取格式化的价格
    /// - Parameter product: 产品
    /// - Returns: 格式化的价格字符串
    func getFormattedPrice(for product: Product) -> String {
        // 简化实现：返回默认价格
        switch product.id {
        case "evolve_pro_monthly":
            return "¥18/月"
        case "evolve_pro_yearly":
            return "¥168/年"
        case "evolve_pro_lifetime":
            return "¥398/永久"
        default:
            return "¥0"
        }
    }
    
    /// 获取订阅周期描述
    /// - Parameter product: 产品
    /// - Returns: 订阅周期描述
    func getSubscriptionPeriodDescription(for product: Product) -> String {
        // 简化实现：返回默认描述
        switch product.id {
        case "evolve_pro_monthly":
            return "每月自动续费"
        case "evolve_pro_yearly":
            return "每年自动续费"
        case "evolve_pro_lifetime":
            return "一次性购买，永久有效"
        default:
            return "未知订阅类型"
        }
    }
    
    // MARK: - 支付处理（保持原有实现）
    
    /// 处理支付
    /// - Parameters:
    ///   - productId: 产品ID
    ///   - transactionId: 交易ID
    ///   - amount: 金额（暂时不使用，保持接口兼容性）
    /// - Returns: 支付记录
    func processPayment(
        productId: String,
        transactionId: String,
        amount: Decimal
    ) async throws -> EAPayment {
        
        // 创建支付记录（使用正确的构造器参数）
        let payment = EAPayment(
            productId: productId,
            transactionId: transactionId,
            expirationDate: nil
        )
        
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        if let currentUser = await sessionManager.currentUser {
            // 注意：EAPayment使用userDataProfile关系，不是直接的user关系
            payment.userDataProfile = currentUser.dataProfile
        }
        
        // 简化实现：不保存到数据库
        return payment
    }
    
    /// 验证收据
    /// - Parameter receiptData: 收据数据
    /// - Returns: 验证结果
    func verifyReceipt(receiptData: Data) async throws -> Bool {
        // 简化实现：总是返回true
        return true
    }
    
    /// 获取产品信息
    /// - Parameter productIds: 产品ID列表
    /// - Returns: 产品信息列表
    func getProductInfo(productIds: [String]) async throws -> [Product] {
        // 简化实现：返回空数组
        return []
    }
}

// MARK: - 购买结果类型

/// 购买结果
enum PurchaseResult {
    case success(EAPayment)
    case failure(Error)
    case userCancelled
    case pending
}

// MARK: - 错误类型

enum PaymentError: Error, LocalizedError {
    case productNotFound
    case purchaseFailed(String)
    case verificationFailed
    case userCancelled
    case networkError
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .productNotFound:
            return "产品未找到"
        case .purchaseFailed(let message):
            return "购买失败：\(message)"
        case .verificationFailed:
            return "验证失败"
        case .userCancelled:
            return "用户取消购买"
        case .networkError:
            return "网络错误"
        case .unknownError(let message):
            return "未知错误：\(message)"
        }
    }
} 