import Foundation
import Security
import SwiftUI
import SwiftData

// MARK: - Keychain错误枚举

enum EAKeychainError: Error {
    case itemNotFound
    case duplicateItem
    case invalidItemFormat
    case unexpectedStatus(OSStatus)
}

// MARK: - Keychain服务

class EAKeychainService {
    
    // MARK: - 常量
    
    private let service = "com.evolve.app"
    
    // MARK: - 保存数据到Keychain
    
    func save(key: String, data: Data) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        // 删除已存在的项目
        SecItemDelete(query as CFDictionary)
        
        // 添加新项目
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw EAKeychainError.unexpectedStatus(status)
        }
    }
    
    // MARK: - 从Keychain读取数据
    
    func load(key: String) throws -> Data {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                throw EAKeychainError.itemNotFound
            }
            throw EAKeychainError.unexpectedStatus(status)
        }
        
        guard let data = result as? Data else {
            throw EAKeychainError.invalidItemFormat
        }
        
        return data
    }
    
    // MARK: - 从Keychain删除数据
    
    func delete(key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw EAKeychainError.unexpectedStatus(status)
        }
    }
    
    // MARK: - 便捷方法：保存字符串
    
    func save(key: String, string: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw EAKeychainError.invalidItemFormat
        }
        try save(key: key, data: data)
    }
    
    // MARK: - 便捷方法：读取字符串
    
    func loadString(key: String) throws -> String {
        let data = try load(key: key)
        guard let string = String(data: data, encoding: .utf8) else {
            throw EAKeychainError.invalidItemFormat
        }
        return string
    }
    
    // MARK: - 便捷方法：保存Codable对象
    
    func save<T: Codable>(key: String, object: T) throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(object)
        try save(key: key, data: data)
    }
    
    // MARK: - 便捷方法：读取Codable对象
    
    func load<T: Codable>(key: String, type: T.Type) throws -> T {
        let data = try load(key: key)
        let decoder = JSONDecoder()
        return try decoder.decode(type, from: data)
    }
}

// MARK: - 会话管理器（适配新Repository层）

@MainActor
class EASessionManager: EASessionManagerProtocol, ObservableObject {
    
    // MARK: - 移除单例模式
    // static let shared = EASessionManager()
    
    // MARK: - Published属性（适配新模型）
    
    @Published var isLoggedIn = false
    @Published var currentUser: EAUser? = nil
    @Published var accessToken: String? = nil
    @Published var refreshToken: String? = nil
    
    // MARK: - 私有属性
    
    private let keychain = EAKeychainService()
    private let userDefaults = UserDefaults.standard
    private var _repositoryContainer: EARepositoryContainer?
    
    // Keychain键名
    private let accessTokenKey = "access_token"
    private let refreshTokenKey = "refresh_token"
    private let userDataKey = "user_data"
    
    // UserDefaults键名
    private let isLoggedInKey = "is_logged_in"
    private let lastLoginDateKey = "last_login_date"
    
    // MARK: - 公共访问器
    
    /// 获取Repository容器
    var repositoryContainer: EARepositoryContainer? {
        return _repositoryContainer
    }
    
    // MARK: - 初始化
    
    /// 默认初始化方法（用于Environment默认值）
    public init() {
        // 延迟恢复会话，等待Repository注入
    }
    
    /// 公共初始化方法（用于Preview和测试）
    public convenience init(repositoryContainer: EARepositoryContainer) {
        self.init()
        self.setRepositoryContainer(repositoryContainer)
    }
    
    // MARK: - Repository注入
    
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        // ✅ 修复：防止重复设置导致状态重置
        guard _repositoryContainer == nil else {
            return
        }

        self._repositoryContainer = container
        // 🔑 修复：使用新的会话恢复方法
        Task { @MainActor in
            await restoreSessionOnAppLaunch()
        }
    }
    
    /// 设置ModelContext（用于新的Apple标准配置）
    func setModelContext(_ context: ModelContext) {
        // 将ModelContext传递给相关服务
        #if DEBUG
        #endif
    }
    
    // MARK: - 会话管理方法（适配新Repository）
    
    /// 🔑 关键修复：保存会话状态到SwiftData
    @MainActor
    func saveSession(authData: EAAuthData, user: EAUser) async throws {
        // 保存用户认证信息到当前实例
        self.currentUser = user
        self.accessToken = authData.token.accessToken
        self.refreshToken = authData.token.refreshToken
        self.isLoggedIn = true
        
        // 🔑 强化修复：确保会话数据保存到UserDefaults
        UserDefaults.standard.set(authData.token.accessToken, forKey: "authToken")
        UserDefaults.standard.set(authData.token.refreshToken, forKey: "refreshToken")
        UserDefaults.standard.set(user.id.uuidString, forKey: "currentUserId")
        UserDefaults.standard.set(true, forKey: "isLoggedIn")
        UserDefaults.standard.set(user.username, forKey: "currentUsername")
        
        // 🔑 强化修复：立即同步到磁盘
        UserDefaults.standard.synchronize()
        
        // 手动触发UI更新
        objectWillChange.send()
        
        // 发送通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EASessionLoginCompleted"),
            object: nil,
            userInfo: ["userId": user.id.uuidString, "username": user.username]
        )
        
        // 会话保存完成
    }
    
    /// 恢复用户会话
    func restoreSession() {
        guard let repositoryContainer = repositoryContainer else {
            return
        }
        
        // 检查UserDefaults中的登录状态
        let wasLoggedIn = userDefaults.bool(forKey: isLoggedInKey)
        
        guard wasLoggedIn else {
            return
        }
        
        Task { @MainActor in
            do {
                // 从Keychain恢复Token
                let token = try keychain.loadString(key: accessTokenKey)
                
                // 🔑 关键修复：从Keychain恢复保存的用户ID
                let userIdString = try keychain.loadString(key: userDataKey)
                guard let userId = UUID(uuidString: userIdString) else {
                    throw EAKeychainError.invalidItemFormat
                }
                
                // ✅ 修复：通过Repository获取用户数据
                guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else {
                    throw DataModelError.userNotFound
                }
                
                // 恢复会话状态
                self.isLoggedIn = true
                self.currentUser = user
                self.accessToken = token
                
                // 更新最后登录时间
                userDefaults.set(Date(), forKey: lastLoginDateKey)
                
            } catch EAKeychainError.itemNotFound {
                // Token或用户ID不存在，清除会话
                await MainActor.run {
                    clearSession()
                }
                
            } catch {
                // ✅ 修复：不要因为轻微错误就清除整个会话
                // 不调用clearSession()，保持当前登录状态
            }
        }
    }
    
    /// 清除用户会话
    @MainActor
    func clearSession() {
        // 开始清除会话
        
        do {
            // 清除Keychain数据
            try keychain.delete(key: accessTokenKey)
            try keychain.delete(key: refreshTokenKey)
            try keychain.delete(key: userDataKey)
            
            // Keychain数据已清除
        } catch EAKeychainError.itemNotFound {
            // 项目不存在，正常情况
            #if DEBUG
            print("ℹ️ [SessionManager] Keychain项目不存在（正常）")
            #endif
        } catch {
            #if DEBUG
            print("⚠️ [SessionManager] Keychain清除失败: \(error)")
            #endif
        }
        
        // 清除UserDefaults
        userDefaults.removeObject(forKey: isLoggedInKey)
        userDefaults.removeObject(forKey: lastLoginDateKey)
        
        #if DEBUG
        print("✅ [SessionManager] UserDefaults已清除")
        #endif
        
        // 🔑 关键修复：确保状态更新在主线程执行，触发UI更新
        self.isLoggedIn = false
        self.currentUser = nil
        self.accessToken = nil
        
        // 🔑 关键修复：强制触发UI更新
        self.objectWillChange.send()
        
        #if DEBUG
        print("✅ [SessionManager] 会话状态已重置")
        print("🔍 [SessionManager] 最终状态 - isLoggedIn: \(self.isLoggedIn), currentUser: \(self.currentUser?.username ?? "nil"), accessToken: \(self.accessToken != nil ? "存在" : "nil")")
        #endif
    }
    
    /// 登录用户
    func login(username: String, email: String? = nil) async throws {
        guard let repositoryContainer = _repositoryContainer else {
            throw SessionError.repositoryNotAvailable
        }
        
        // 获取当前用户，如果不存在则创建新用户
        var user = try await repositoryContainer.getCurrentUser()
        if user == nil {
            // 创建新用户
            user = try await repositoryContainer.userRepository.createUser(username: username, email: email)
        }
        
        guard let currentUser = user else {
            throw SessionError.userNotFound
        }
        
        // 创建模拟认证数据
        let authData = EAAuthData(
            token: EAToken(accessToken: "mock_token_\(UUID().uuidString)", refreshToken: "mock_refresh_\(UUID().uuidString)"),
            user: EAAuthUser(id: currentUser.id.uuidString, username: currentUser.username, email: currentUser.email)
        )
        
        // 保存会话
        try await saveSession(authData: authData, user: currentUser)
        
        // ✅ 修复iOS 18.2线程安全：通过Repository层安全检查用户设置
        // 只传递userID，避免跨线程传递SwiftData模型对象
        do {
            let existingSettings = try await repositoryContainer.userSettingsRepository.fetchSettings(for: currentUser.id)
            if existingSettings == nil {
                _ = try await repositoryContainer.userSettingsRepository.createDefaultSettings(for: currentUser.id)
            }
        } catch {
            // iOS 18.2兼容性：设置创建失败时不阻塞登录流程
            #if DEBUG
            #endif
        }
    }
    
    /// 登出用户
    @MainActor
    func logout() {
        // 🔑 关键修复：强化版退出登录实现，确保100%可靠
        
        // 1. 立即更新状态，避免UI延迟
        self.isLoggedIn = false
        self.currentUser = nil
        self.accessToken = nil
        
        // 2. 强制触发UI更新 - 多重保障机制
        self.objectWillChange.send()
        
        // 3. 清理持久化数据
        clearSession()
        
        // 4. 🚀 新增：强制UI刷新机制，确保视图立即响应
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 再次确保状态一致性
            if self.isLoggedIn {
                self.isLoggedIn = false
                self.currentUser = nil
                self.accessToken = nil
                self.objectWillChange.send()
            }
            
            // 🔑 关键：强制发布状态变更通知
            NotificationCenter.default.post(
                name: Notification.Name("EASessionLogoutCompleted"),
                object: nil
            )
        }
        
        // 5. 🚀 新增：延迟验证机制，确保退出登录完全生效
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) { [weak self] in
            guard let self = self else { return }
            
            // 最终状态验证
            if self.isLoggedIn || self.currentUser != nil || self.accessToken != nil {
                // 如果状态仍然异常，强制重置
                self.isLoggedIn = false
                self.currentUser = nil
                self.accessToken = nil
                self.objectWillChange.send()
                
                // 发送紧急重置通知
                NotificationCenter.default.post(
                    name: Notification.Name("EASessionEmergencyReset"),
                    object: nil
                )
            }
        }
    }
    
    /// 获取当前用户
    func getCurrentUser() async throws -> EAUser? {
        guard let repositoryContainer = _repositoryContainer else {
            throw SessionError.repositoryNotAvailable
        }
        
        return try await repositoryContainer.getCurrentUser()
    }
    
    /// 更新用户信息
    func updateUser(_ user: EAUser) async throws {
        guard let repositoryContainer = _repositoryContainer else {
            throw SessionError.repositoryNotAvailable
        }
        
        try await repositoryContainer.userRepository.saveUser(user)
        self.currentUser = user
    }
    
    // MARK: - 用户数据刷新方法（修复头像保存问题）
    
    /// 刷新当前用户数据（从数据库重新获取最新数据）
    @MainActor
    func refreshCurrentUser() async {
        guard let currentUserId = currentUser?.id,
              let repositoryContainer = _repositoryContainer else { return }
        
        do {
            // 从数据库重新获取最新的用户数据
            if let refreshedUser = try await repositoryContainer.userRepository.fetchUser(id: currentUserId) {
                self.currentUser = refreshedUser
            }
        } catch {
            #if DEBUG
            print("❌ [SessionManager] 刷新用户数据失败: \(error)")
            #endif
        }
    }
    
    /// 更新用户头像后刷新用户数据
    @MainActor
    func updateUserAvatarAndRefresh(avatarData: EAAvatarData?) async throws {
        guard let currentUserId = currentUser?.id,
              let repositoryContainer = _repositoryContainer else {
            throw NSError(domain: "SessionManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录或Repository未初始化"])
        }
        
        // 保存头像到数据库
        try await repositoryContainer.userRepository.updateUserAvatar(userId: currentUserId, avatarData: avatarData)
        
        // 立即刷新当前用户数据
        await refreshCurrentUser()
    }
    
    // MARK: - 会话恢复方法（修复重启后无法登录的问题）
    
    /// 🔑 关键修复：从UserDefaults恢复会话状态
    @MainActor
    func restoreSessionOnAppLaunch() async {
        guard let repositoryContainer = repositoryContainer else {
            #if DEBUG
            print("⚠️ [SessionManager] Repository容器不可用，无法恢复会话")
            #endif
            return
        }
        
        // 从UserDefaults读取会话数据
        let isLoggedInStored = UserDefaults.standard.bool(forKey: "isLoggedIn")
        let userIdString = UserDefaults.standard.string(forKey: "currentUserId")
        let storedToken = UserDefaults.standard.string(forKey: "authToken")
        let storedUsername = UserDefaults.standard.string(forKey: "currentUsername")
        
        #if DEBUG
        print("🔍 [SessionManager] 恢复会话检查 - isLoggedIn: \(isLoggedInStored), userId: \(userIdString ?? "nil")")
        #endif
        
        // 如果有有效的会话数据
        if isLoggedInStored, 
           let userIdString = userIdString,
           let userId = UUID(uuidString: userIdString),
           let token = storedToken,
           let _ = storedUsername {
            
            do {
                // 🔑 关键修复：从Repository重新获取用户数据
                if let user = try await repositoryContainer.userRepository.fetchUser(id: userId) {
                    // 恢复会话状态
                    self.currentUser = user
                    self.accessToken = token
                    self.refreshToken = UserDefaults.standard.string(forKey: "refreshToken")
                    self.isLoggedIn = true
                    
                    // 触发UI更新
                    objectWillChange.send()
                    
                    #if DEBUG
                    print("✅ [SessionManager] 会话恢复成功 - 用户: \(user.username)")
                    #endif
                } else {
                    // 用户数据不存在，清除会话
                    await clearSessionData()
                    #if DEBUG
                    print("⚠️ [SessionManager] 用户数据不存在，已清除会话")
                    #endif
                }
            } catch {
                // 恢复失败，清除会话
                await clearSessionData()
                #if DEBUG
                print("❌ [SessionManager] 会话恢复失败: \(error.localizedDescription)")
                #endif
            }
        } else {
            // 没有有效的会话数据
            await clearSessionData()
            #if DEBUG
            print("ℹ️ [SessionManager] 没有有效的会话数据")
            #endif
        }
    }
    
    /// 清除会话数据的辅助方法
    @MainActor
    private func clearSessionData() async {
        self.isLoggedIn = false
        self.currentUser = nil
        self.accessToken = nil
        self.refreshToken = nil
        
        // 清除UserDefaults
        UserDefaults.standard.removeObject(forKey: "isLoggedIn")
        UserDefaults.standard.removeObject(forKey: "currentUserId")
        UserDefaults.standard.removeObject(forKey: "authToken")
        UserDefaults.standard.removeObject(forKey: "refreshToken")
        UserDefaults.standard.removeObject(forKey: "currentUsername")
        UserDefaults.standard.synchronize()
        
        objectWillChange.send()
    }
}

// MARK: - 会话错误类型

enum SessionError: Error, LocalizedError {
    case repositoryNotAvailable
    case userNotFound
    case invalidCredentials
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .repositoryNotAvailable:
            return "数据访问服务不可用"
        case .userNotFound:
            return "用户未找到"
        case .invalidCredentials:
            return "登录凭证无效"
        case .networkError:
            return "网络连接错误"
        }
    }
}

// MARK: - 认证数据结构（保持兼容性）

struct EAAuthData {
    let token: EAToken
    let user: EAAuthUser
}

struct EAToken {
    let accessToken: String
    let refreshToken: String
}

struct EAAuthUser: Codable {
    let id: String
    let username: String
    let email: String?
}

// MARK: - 扩展：便捷方法

extension EASessionManager {
    
    /// 是否为Pro用户
    var isPro: Bool {
        guard let user = currentUser else { return false }
        
        if !user.isPro { return false }
        
        // 检查Pro会员是否过期
        if let expirationDate = user.proExpirationDate {
            return expirationDate > Date()
        }
        
        return true
    }
    
    /// Pro会员剩余天数
    var proRemainingDays: Int? {
        guard let user = currentUser,
              user.isPro,
              let expirationDate = user.proExpirationDate else {
            return nil
        }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: expirationDate)
        return max(0, components.day ?? 0)
    }
    
    /// 检查会话是否有效
    func isSessionValid() -> Bool {
        return isLoggedIn && accessToken != nil && currentUser != nil
    }
    
    /// 获取最后登录时间
    func getLastLoginDate() -> Date? {
        return userDefaults.object(forKey: lastLoginDateKey) as? Date
    }
}

// MARK: - 扩展：调试方法

#if DEBUG
extension EASessionManager {
    
    /// 打印会话信息（仅调试模式）
    func printSessionInfo() {
        print("""
        📱 会话信息:
        - 登录状态: \(isLoggedIn)
        - 用户名: \(currentUser?.username ?? "无")
        - 邮箱: \(currentUser?.email ?? "无")
        - Pro状态: \(isPro)
        - Token存在: \(accessToken != nil)
        - 最后登录: \(getLastLoginDate()?.description ?? "无")
        """)
    }
    
    /// 模拟登录（仅调试模式）
    func simulateLogin() async {
        guard let repositoryContainer = _repositoryContainer else {
            print("⚠️ Repository容器未注入，无法模拟登录")
            return
        }
        
        do {
            // 创建模拟用户
            let mockUser = try await repositoryContainer.userRepository.createUser(
                username: "调试用户",
                email: "<EMAIL>"
            )
            
            // 创建模拟认证数据
            let mockAuthData = EAAuthData(
                token: EAToken(
                    accessToken: "debug_access_token",
                    refreshToken: "debug_refresh_token"
                ),
                user: EAAuthUser(
                    id: mockUser.id.uuidString,
                    username: mockUser.username,
                    email: mockUser.email
                )
            )
            
            // 保存会话
            try await saveSession(authData: mockAuthData, user: mockUser)
            
            print("✅ 模拟登录完成")
            
        } catch {
            // 模拟登录失败，静默处理
        }
    }
}
#endif 