import Foundation
import SwiftData
import SwiftUI

/// 分享提醒服务 - 智能检测分享时机并提供个性化建议
/// 遵循Repository模式，通过EARepositoryContainer访问数据
class EASharingReminderService {
    
    // Repository容器（可选，支持降级处理）
    private var repositoryContainer: EARepositoryContainer?
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    /// 提醒配置
    private let reminderConfig = SharingReminderConfig()
    
    /// 错误状态
    var lastError: SharingReminderError?
    
    /// 当前分享建议
    var currentSuggestion: SharingSuggestion?
    
    /// 是否启用分享提醒
    var isReminderEnabled: Bool = true
    
    /// ✅ 修复：添加sessionManager依赖注入
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer? = nil) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - 主要功能
    
    /// 检查分享时机
    /// - Returns: 分享建议（如果有）
    func checkSharingOpportunity() async throws -> SharingSuggestion? {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = await sessionManager.currentUser else {
            throw NSError(domain: "SharingReminderService", code: 404, userInfo: [NSLocalizedDescriptionKey: "用户未找到"])
            }
            
        // 简化检查逻辑，只返回基本的分享建议
        return await checkBasicSharingOpportunity(for: currentUser)
    }
    
    /// 基本分享时机检查
    /// - Parameter user: 用户
    /// - Returns: 分享建议
    private func checkBasicSharingOpportunity(for user: EAUser) async -> SharingSuggestion? {
        // 简化实现：基于用户创建时间的基本提醒
        let daysSinceCreation = Calendar.current.dateComponents([.day], from: user.creationDate, to: Date()).day ?? 0
        
        if daysSinceCreation >= 7 {
            return SharingSuggestion(
                type: .engagement,
                title: "分享你的进展",
                content: "和大家分享一下你最近的习惯进展吧！",
                priority: .low,
                suggestedAction: .shareProgress
            )
            }
            
            return nil
    }
    
    /// 检查习惯完成里程碑
    /// - Parameter user: 用户
    /// - Returns: 分享建议
    private func checkCompletionMilestones(for user: EAUser) async -> SharingSuggestion? {
        // 简化实现：返回nil
            return nil
    }
    
    /// 检查连续完成记录
    /// - Parameter user: 用户
    /// - Returns: 分享建议
    private func checkStreakMilestones(for user: EAUser) async -> SharingSuggestion? {
        // 简化实现：返回nil
            return nil
    }
    
    /// 检查长时间未分享提醒
    /// - Parameter user: 用户
    /// - Returns: 分享建议
    private func checkInactivityReminder(for user: EAUser) async -> SharingSuggestion? {
        // 简化实现：基于用户创建时间的提醒
        let daysSinceCreation = Calendar.current.dateComponents([.day], from: user.creationDate, to: Date()).day ?? 0
                
        if daysSinceCreation >= 7 {
                    return SharingSuggestion(
                type: .engagement,
                title: "好久没见你分享了",
                content: "和大家分享一下你最近的习惯进展吧！社区的朋友们都很想知道你的近况。",
                        priority: .low,
                suggestedAction: .shareProgress
                )
            }
            
            return nil
    }
    
    /// 获取用户最近的完成记录
    /// - Parameters:
    ///   - user: 用户
    ///   - days: 天数
    /// - Returns: 完成记录列表
    private func getRecentCompletions(for user: EAUser, days: Int) async -> [EACompletion] {
        // 简化实现：返回空数组
            return []
        }
    
    /// 计算连续天数
    /// - Parameter completions: 完成记录
    /// - Returns: 连续天数
    private func calculateStreakFromCompletions(_ completions: [EACompletion]) -> Int {
        // 简化实现：返回0
        return 0
    }
    
    // MARK: - 定期检查
    
    /// 设置定期检查
    private func setupPeriodicCheck() {
        // 每小时检查一次分享机会
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { _ in
            Task { @MainActor in
                await self.performPeriodicCheck()
            }
        }
    }
    
    /// 执行定期检查
    private func performPeriodicCheck() async {
        guard isReminderEnabled else { return }
        
        do {
            if let suggestion = try await checkSharingOpportunity() {
            currentSuggestion = suggestion
            }
        } catch {
            // 简化错误处理：忽略错误
        }
    }
    
    // MARK: - 公共接口
    
    /// 手动触发分享检测
    func triggerSharingCheck() async {
        do {
            currentSuggestion = try await checkSharingOpportunity()
        } catch {
            // 简化错误处理：忽略错误
        }
    }
    
    /// 忽略当前建议
    func dismissCurrentSuggestion() {
        currentSuggestion = nil
    }
    
    /// 启用/禁用分享提醒
    /// - Parameter enabled: 是否启用
    func setReminderEnabled(_ enabled: Bool) {
        isReminderEnabled = enabled
        
        if !enabled {
            currentSuggestion = nil
        }
    }
}

// MARK: - 配置和数据结构

/// 分享提醒配置
struct SharingReminderConfig {
    let dailyCompletionThreshold = 3        // 每日完成阈值
    let streakMilestones = [7, 14, 30, 60, 100]  // 连续天数里程碑
    let inactivityDays = 7                  // 未活跃天数阈值
    let maxRemindersPerDay = 2              // 每日最大提醒次数
}

/// 分享建议
struct SharingSuggestion {
    let type: SuggestionType
    let title: String
    let content: String
    let priority: SuggestionPriority
    let suggestedAction: SuggestedAction
    let timestamp: Date = Date()
}
    
/// 建议类型
    enum SuggestionType {
    case dailyAchievement    // 每日成就
    case streakMilestone     // 连续里程碑
    case engagement          // 参与提醒
    case celebration         // 庆祝时刻
    }
    
/// 建议优先级
enum SuggestionPriority {
    case low, medium, high
    }
    
/// 建议动作
    enum SuggestedAction {
    case shareProgress       // 分享进展
    case shareInsight        // 分享洞察
    case shareAchievement    // 分享成就
    case engageWithCommunity // 参与社区
}

/// 分享提醒错误类型
enum SharingReminderError: LocalizedError {
    case userNotLoggedIn
    case detectionFailed(String)
    case configurationError
    
    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "用户未登录"
        case .detectionFailed(let details):
            return "检测分享时机失败：\(details)"
        case .configurationError:
            return "配置错误"
        }
    }
} 