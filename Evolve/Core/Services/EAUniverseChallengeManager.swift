import Foundation
import SwiftUI

// MARK: - 宇宙挑战管理器（Phase 4 Day 9核心组件）

/// 宇宙挑战管理器：协调挑战生命周期、进度跟踪、奖励发放等业务逻辑
@MainActor
class EAUniverseChallengeManager: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var isInitialized = false
    @Published var activeChallenges: [EAUniverseChallenge] = []
    @Published var userParticipations: [EAUniverseChallengeParticipation] = []
    @Published var challengeStats = EAChallengeStats.empty
    @Published var recentRewards: [EAChallengeReward] = []
    @Published var systemNotifications: [EASystemNotification] = []
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let businessLogic: EAUniverseChallengeBusinessLogic
    private let challengeService: EAUniverseChallengeService
    
    // MARK: - 定时器
    
    private var lifecycleTimer: Timer?
    private var progressTimer: Timer?
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
        
        // 获取依赖服务 - 修复初始化参数
        let cacheManager = EAAICacheManager() // 创建缓存管理器
        let stellarEnergyService = EAStellarEnergyService(repositoryContainer: repositoryContainer, cacheManager: cacheManager)
        let challengeService = EAUniverseChallengeService(repositoryContainer: repositoryContainer, stellarEnergyService: stellarEnergyService)
        
        // 初始化业务逻辑
        self.businessLogic = EAUniverseChallengeBusinessLogic(
            repositoryContainer: repositoryContainer,
            stellarEnergyService: stellarEnergyService,
            challengeService: challengeService
        )
        
        self.challengeService = challengeService
        
        // 启动管理器
        Task {
            await initialize()
        }
    }
    
    // MARK: - 生命周期管理
    
    /// 初始化管理器
    func initialize() async {
        guard !isInitialized else { return }
        
        // 启动定时任务
        startLifecycleManagement()
        startProgressTracking()
        
        // 加载初始数据
        await loadActiveChallenges()
        await loadChallengeStats()
        
        // 绑定业务逻辑状态
        bindBusinessLogicState()
        
        isInitialized = true
        addSystemNotification("挑战管理器已启动")
    }
    
    /// 启动生命周期管理
    private func startLifecycleManagement() {
        lifecycleTimer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performLifecycleManagement()
            }
        }
    }
    
    /// 启动进度跟踪
    private func startProgressTracking() {
        progressTimer = Timer.scheduledTimer(withTimeInterval: 600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performProgressTracking()
            }
        }
    }
    
    /// 执行生命周期管理
    private func performLifecycleManagement() async {
        // 生成周期性挑战
        await businessLogic.generatePeriodicChallenges()
        
        // 管理挑战状态
        await businessLogic.manageChallengeStates()
        
        // 刷新活跃挑战列表
        await loadActiveChallenges()
    }
    
    /// 执行进度跟踪
    private func performProgressTracking() async {
        // 更新所有用户参与的挑战进度
        for participation in userParticipations {
            if let challenge = participation.challenge, !participation.isCompleted {
                let progress = await businessLogic.calculateUserProgress(
                    challengeId: challenge.id,
                    userId: participation.user?.id ?? UUID()
                )
                
                // 更新参与记录
                participation.currentProgress = progress.currentValue
                participation.progressPercentage = Double(progress.percentage) / 100.0
                participation.lastUpdateDate = Date()
            }
        }
        
        // 保存更新
        do {
            try await repositoryContainer.challengeRepository.saveContext()
        } catch {
            addSystemNotification("进度跟踪更新失败：\(error.localizedDescription)")
        }
    }
    
    /// 绑定业务逻辑状态
    private func bindBusinessLogicState() {
        // 监听业务逻辑状态变化
        Task {
            while isInitialized {
                challengeStats = businessLogic.challengeStats
                recentRewards = businessLogic.recentRewards
                systemNotifications = businessLogic.systemNotifications
                
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }
        }
    }
    
    // MARK: - 数据加载
    
    /// 加载活跃挑战
    func loadActiveChallenges() async {
        do {
            activeChallenges = try await repositoryContainer.challengeRepository.fetchActiveChallenges()
        } catch {
            addSystemNotification("加载活跃挑战失败：\(error.localizedDescription)")
        }
    }
    
    /// 加载用户参与记录
    func loadUserParticipations(for userId: UUID) async {
        do {
            userParticipations = try await repositoryContainer.challengeRepository.fetchUserParticipations(for: userId)
        } catch {
            addSystemNotification("加载用户参与记录失败：\(error.localizedDescription)")
        }
    }
    
    /// 加载挑战统计
    func loadChallengeStats() async {
        // 业务逻辑会自动更新统计数据
        challengeStats = businessLogic.challengeStats
    }
    
    // MARK: - 挑战操作
    
    /// 用户参与挑战
    func joinChallenge(_ challenge: EAUniverseChallenge, user: EAUser) async -> Bool {
        do {
            let participation = try await repositoryContainer.challengeRepository.joinChallenge(challenge, user: user)
            
            // 更新本地状态
            userParticipations.append(participation)
            
            // 更新挑战列表
            await loadActiveChallenges()
            
            addSystemNotification("成功参与挑战：\(challenge.title)")
            return true
            
        } catch {
            addSystemNotification("参与挑战失败：\(error.localizedDescription)")
            return false
        }
    }
    
    /// 更新挑战进度
    func updateChallengeProgress(challengeId: UUID, userId: UUID) async {
        let progress = await businessLogic.calculateUserProgress(challengeId: challengeId, userId: userId)
        
        // 更新本地参与记录
        if let participation = userParticipations.first(where: { 
            $0.challenge?.id == challengeId && $0.user?.id == userId 
        }) {
            participation.currentProgress = progress.currentValue
            participation.progressPercentage = Double(progress.percentage) / 100.0
            participation.lastUpdateDate = Date()
        }
    }
    
    /// 手动触发挑战生成
    func generateNewChallenges() async {
        await businessLogic.generatePeriodicChallenges()
        await loadActiveChallenges()
    }
    
    /// 手动触发状态管理
    func manageChallengeStates() async {
        await businessLogic.manageChallengeStates()
        await loadActiveChallenges()
    }
    
    // MARK: - 查询方法
    
    /// 获取用户在特定挑战的参与记录
    func getUserParticipation(challengeId: UUID, userId: UUID) -> EAUniverseChallengeParticipation? {
        return userParticipations.first { participation in
            participation.challenge?.id == challengeId && participation.user?.id == userId
        }
    }
    
    /// 检查用户是否已参与挑战
    func isUserParticipating(challengeId: UUID, userId: UUID) -> Bool {
        return getUserParticipation(challengeId: challengeId, userId: userId) != nil
    }
    
    /// 获取用户活跃参与的挑战
    func getActiveParticipations(for userId: UUID) -> [EAUniverseChallengeParticipation] {
        return userParticipations.filter { participation in
            participation.user?.id == userId && 
            !participation.isCompleted && 
            participation.challenge?.isActive == true
        }
    }
    
    /// 获取用户已完成的挑战
    func getCompletedParticipations(for userId: UUID) -> [EAUniverseChallengeParticipation] {
        return userParticipations.filter { participation in
            participation.user?.id == userId && participation.isCompleted
        }
    }
    
    // MARK: - 清理资源
    
    /// 停止管理器
    func stop() {
        lifecycleTimer?.invalidate()
        progressTimer?.invalidate()
        lifecycleTimer = nil
        progressTimer = nil
        isInitialized = false
        
        addSystemNotification("挑战管理器已停止")
    }
    
    deinit {
        // 修复@MainActor调用问题 - 仅清理timer，避免修改MainActor属性
        lifecycleTimer?.invalidate()
        progressTimer?.invalidate()
        lifecycleTimer = nil
        progressTimer = nil
    }
    
    // MARK: - 辅助方法
    
    /// 添加系统通知
    private func addSystemNotification(_ message: String) {
        let notification = EASystemNotification(
            id: UUID(),
            message: message,
            timestamp: Date(),
            type: .system
        )
        
        systemNotifications.append(notification)
        
        // 保持通知数量在合理范围内
        if systemNotifications.count > 50 {
            systemNotifications.removeFirst(systemNotifications.count - 50)
        }
    }
}

// MARK: - 挑战管理器扩展

extension EAUniverseChallengeManager {
    
    /// 获取挑战统计摘要
    var statsDescription: String {
        return "活跃挑战: \(challengeStats.activeChallenges), 总参与者: \(challengeStats.totalParticipants), 已发放奖励: \(challengeStats.totalRewardsDistributed)"
    }
    
    /// 获取最近奖励摘要
    var recentRewardsDescription: String {
        let totalRewards = recentRewards.reduce(0) { $0 + $1.amount }
        return "最近奖励: \(recentRewards.count) 次, 总计: \(totalRewards) 星际能量"
    }
    
    /// 检查是否有新通知
    var hasNewNotifications: Bool {
        return systemNotifications.contains { notification in
            Date().timeIntervalSince(notification.timestamp) < 300 // 5分钟内
        }
    }
} 