import SwiftData
import Foundation

// MARK: - 数据库测试助手（2025年修正版）

@MainActor
class DatabaseTestHelper {
    
    struct DatabaseTestResult {
        var totalTests: Int = 0
        var passedTests: Int = 0
        var errors: [String] = []
        
        var successRate: Double {
            guard totalTests > 0 else { return 0 }
            return Double(passedTests) / Double(totalTests)
        }
        
        var isAllPassed: Bool {
            return errors.isEmpty && passedTests == totalTests
        }
    }
    
    /// 运行完整的数据库测试套件
    func runFullTestSuite(modelContext: ModelContext) async -> DatabaseTestResult {
        print("🧪 开始数据库测试套件...")
        
        var result = DatabaseTestResult()
        
        // 清理旧的测试数据
        await cleanupTestData(modelContext: modelContext)
        
        // 运行各项测试
        await testBasicOperations(modelContext: modelContext, result: &result)
        await testRelationships(modelContext: modelContext, result: &result)
        await testCompletionOperations(modelContext: modelContext, result: &result)
        await testDataConsistency(modelContext: modelContext, result: &result)
        await testPerformance(modelContext: modelContext, result: &result)
        
        // 输出测试结果
        print("\n📊 测试结果:")
        print("总测试数: \(result.totalTests)")
        print("通过测试: \(result.passedTests)")
        print("成功率: \(String(format: "%.1f", result.successRate * 100))%")
        
        if !result.errors.isEmpty {
            // 错误列表：
            for error in result.errors {
                print("  • \(error)")
            }
        }
        
        return result
    }
    
    /// 测试基本操作
    private func testBasicOperations(modelContext: ModelContext, result: inout DatabaseTestResult) async {
        result.totalTests += 1
        
        do {
            // 创建测试用户
            let testUser = EAUser(
                username: "测试用户_\(Date().timeIntervalSince1970)",
                email: "<EMAIL>"
            )
            
            modelContext.insert(testUser)
            try modelContext.save()
            
            // 验证用户创建 - 使用简单查询避免#Predicate问题
            let descriptor = FetchDescriptor<EAUser>()
            let allUsers = try modelContext.fetch(descriptor)
            
            // 在内存中过滤，避免复杂的#Predicate类型推断问题
            let fetchedUsers = allUsers.filter { user in
                user.id == testUser.id
            }
            
            if fetchedUsers.count == 1 {
                result.passedTests += 1
                print("✅ 基本操作测试通过")
            } else {
                result.errors.append("用户创建验证失败")
            }
            
        } catch {
            result.errors.append("基本操作测试失败: \(error)")
        }
    }
    
    /// 测试关系操作
    private func testRelationships(modelContext: ModelContext, result: inout DatabaseTestResult) async {
        result.totalTests += 1
        
        do {
            // 获取测试用户
            let userDescriptor = FetchDescriptor<EAUser>()
            let users = try modelContext.fetch(userDescriptor)
            
            guard let testUser = users.first else {
                result.errors.append("找不到测试用户")
                return
            }
            
            // 创建测试习惯
            let testHabit = EAHabit(
                name: "测试习惯_\(Date().timeIntervalSince1970)",
                iconName: "⭐",
                targetFrequency: 1
            )
            testHabit.user = testUser
            
            modelContext.insert(testHabit)
            try modelContext.save()
            
            // 验证关系
            if testHabit.user?.id == testUser.id {
                result.passedTests += 1
                print("✅ 关系操作测试通过")
            } else {
                result.errors.append("习惯关系设置失败")
            }
            
        } catch {
            result.errors.append("关系操作测试失败: \(error)")
        }
    }
    
    /// 测试完成记录操作
    private func testCompletionOperations(modelContext: ModelContext, result: inout DatabaseTestResult) async {
        result.totalTests += 1
        
        do {
            // 获取测试用户和习惯
            let userDescriptor = FetchDescriptor<EAUser>()
            let users = try modelContext.fetch(userDescriptor)
            
            let habitDescriptor = FetchDescriptor<EAHabit>()
            let habits = try modelContext.fetch(habitDescriptor)
            
            guard let _ = users.first,
                  let testHabit = habits.first else {
                result.errors.append("找不到测试用户或习惯")
                return
            }
            
            // 创建完成记录
            let completionRecord = EACompletion(
                completionNote: "测试完成",
                energyLevel: 5
            )
            completionRecord.habit = testHabit
            
            modelContext.insert(completionRecord)
            try modelContext.save()
            
            // 验证关系
            if completionRecord.habit?.id == testHabit.id {
                result.passedTests += 1
                print("✅ 完成记录操作测试通过")
            } else {
                result.errors.append("完成记录关系设置失败")
            }
            
        } catch {
            result.errors.append("完成记录操作测试失败: \(error)")
        }
    }
    
    /// 测试数据一致性
    private func testDataConsistency(modelContext: ModelContext, result: inout DatabaseTestResult) async {
        result.totalTests += 1
        
        do {
            // 获取所有数据
            let userDescriptor = FetchDescriptor<EAUser>()
            let users = try modelContext.fetch(userDescriptor)
            
            let habitDescriptor = FetchDescriptor<EAHabit>()
            let habits = try modelContext.fetch(habitDescriptor)
            
            let completionDescriptor = FetchDescriptor<EACompletion>()
            let completions = try modelContext.fetch(completionDescriptor)
            
            // 检查关系一致性
            var consistencyErrors: [String] = []
            
            // 检查习惯的用户关系
            for habit in habits {
                if let user = habit.user {
                    if !users.contains(where: { $0.id == user.id }) {
                        consistencyErrors.append("习惯「\(habit.name)」的用户关系无效")
                    }
                }
            }
            
            // 检查完成记录的关系
            for completion in completions {
                if let habit = completion.habit {
                    if !habits.contains(where: { $0.id == habit.id }) {
                        consistencyErrors.append("完成记录的习惯关系无效")
                    }
                }
            }
            
            if consistencyErrors.isEmpty {
                result.passedTests += 1
                print("✅ 数据一致性测试通过")
            } else {
                result.errors.append(contentsOf: consistencyErrors)
            }
            
        } catch {
            result.errors.append("数据一致性测试失败: \(error)")
        }
    }
    
    /// 测试性能
    private func testPerformance(modelContext: ModelContext, result: inout DatabaseTestResult) async {
        result.totalTests += 1
        
        let startTime = Date()
        
        do {
            // 批量创建数据测试性能
            guard let testUser = try modelContext.fetch(FetchDescriptor<EAUser>()).first else {
                result.errors.append("找不到测试用户")
                return
            }
            
            // 创建10个习惯
            for i in 1...10 {
                let habit = EAHabit(
                    name: "性能测试习惯_\(i)",
                    iconName: "⭐",
                    targetFrequency: 1
                )
                habit.user = testUser
                
                modelContext.insert(habit)
                
                // 为每个习惯创建5个完成记录
                for j in 1...5 {
                    let completion = EACompletion(
                        completionNote: "性能测试完成_\(j)",
                        energyLevel: j
                    )
                    completion.habit = habit
                    modelContext.insert(completion)
                }
            }
            
            try modelContext.save()
            
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            
            if duration < 5.0 { // 5秒内完成认为性能合格
                result.passedTests += 1
                print("✅ 性能测试通过 (耗时: \(String(format: "%.2f", duration))秒)")
            } else {
                result.errors.append("性能测试失败，耗时过长: \(String(format: "%.2f", duration))秒")
            }
            
        } catch {
            result.errors.append("性能测试失败: \(error)")
        }
    }
    
    /// 清理测试数据
    func cleanupTestData(modelContext: ModelContext) async {
        print("🧹 清理测试数据...")
        
        do {
            // 删除所有测试数据
            let completionDescriptor = FetchDescriptor<EACompletion>()
            let completions = try modelContext.fetch(completionDescriptor)
            for completion in completions {
                modelContext.delete(completion)
            }
            
            let habitDescriptor = FetchDescriptor<EAHabit>()
            let habits = try modelContext.fetch(habitDescriptor)
            for habit in habits {
                modelContext.delete(habit)
            }
            
            let userDescriptor = FetchDescriptor<EAUser>()
            let users = try modelContext.fetch(userDescriptor)
            for user in users {
                if user.username.contains("测试") {
                    modelContext.delete(user)
                }
            }
            
            try modelContext.save()
            print("✅ 测试数据清理完成")
            
        } catch {
            // 清理测试数据失败，静默处理
        }
    }
    
    /// 创建示例数据
    func createSampleData(modelContext: ModelContext) async {
        print("📝 创建示例数据...")
        
        do {
            // 创建示例用户
            let sampleUser = EAUser(
                username: "示例用户",
                email: "<EMAIL>"
            )
            
            modelContext.insert(sampleUser)
            // fetch一次，确保上下文一致
            let sampleUserId = sampleUser.id
            let userDescriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == sampleUserId })
            guard let contextUser = try? modelContext.fetch(userDescriptor).first else {
                // 测试数据生成：无法fetch用户
                return
            }
            // 创建示例习惯
            let habitNames = ["早起", "运动", "阅读", "冥想", "喝水"]
            for habitName in habitNames {
                let habit = EAHabit(
                    name: habitName,
                    iconName: "⭐",
                    targetFrequency: 1
                )
                modelContext.insert(habit)
                habit.user = contextUser // ✅ 只用当前context fetch的user赋值
                for day in 1...7 {
                    if Bool.random() {
                        let completion = EACompletion(
                            completionNote: "第\(day)天完成",
                            energyLevel: Int.random(in: 3...5)
                        )
                        modelContext.insert(completion)
                        completion.habit = habit
                    }
                }
            }
            try modelContext.save()
            // ✅ 修复：测试环境不设置全局SessionManager状态
            // 测试数据创建完成，不需要设置全局状态
            
            print("✅ 示例数据创建完成")
            
        } catch {
            // 创建示例数据失败，静默处理
        }
    }
} 