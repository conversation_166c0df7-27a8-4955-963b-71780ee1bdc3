import SwiftUI
import SwiftData

/// 数据持久化测试视图（阶段4：认证流程集成测试）
/// 用于在应用内直接验证数据持久化机制是否正常工作
struct EADataPersistenceTestView: View {
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.sessionManager) private var sessionManager
    
    @State private var testHelper: EATestDataHelper?
    @State private var testResult: PersistenceTestResult?
    @State private var isRunningTest = false
    @State private var testMessage = ""
    @State private var lastCreatedUser: EAUser?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                
                // 测试标题
                VStack(spacing: 8) {
                    Text("数据持久化测试工具")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("验证Repository和SwiftData集成")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Divider()
                
                // 测试状态显示
                if let result = testResult {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("测试结果")
                            .font(.headline)
                        
                        Text(result.summary)
                            .font(.system(.body, design: .monospaced))
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                }
                
                // 测试消息
                if !testMessage.isEmpty {
                    Text(testMessage)
                        .font(.body)
                        .foregroundColor(.blue)
                        .multilineTextAlignment(.center)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
                
                Spacer()
                
                // 测试按钮组
                VStack(spacing: 16) {
                    
                    // 创建测试用户
                    Button("创建测试用户") {
                        Task {
                            await createTestUser()
                        }
                    }
                    .disabled(isRunningTest)
                    .buttonStyle(.borderedProminent)
                    
                    // 测试登录验证
                    if lastCreatedUser != nil {
                        Button("测试登录验证") {
                            Task {
                                await testLoginValidation()
                            }
                        }
                        .disabled(isRunningTest)
                        .buttonStyle(.bordered)
                    }
                    
                    // 验证数据持久化
                    Button("验证数据持久化") {
                        Task {
                            await verifyPersistence()
                        }
                    }
                    .disabled(isRunningTest)
                    .buttonStyle(.bordered)
                    
                    // 运行完整测试
                    Button("运行完整测试") {
                        Task {
                            await runFullTest()
                        }
                    }
                    .disabled(isRunningTest)
                    .buttonStyle(.borderedProminent)
                    
                    // 清理测试数据
                    Button("清理测试数据") {
                        Task {
                            await cleanupTestData()
                        }
                    }
                    .disabled(isRunningTest)
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
                .padding(.horizontal)
                
                if isRunningTest {
                    ProgressView("测试进行中...")
                        .progressViewStyle(CircularProgressViewStyle())
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("测试工具")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                setupTestHelper()
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func setupTestHelper() {
        if let container = repositoryContainer {
            testHelper = EATestDataHelper(repositoryContainer: container)
        }
    }
    
    // MARK: - 测试方法
    
    @MainActor
    private func createTestUser() async {
        guard let helper = testHelper else {
            testMessage = "测试工具未初始化"
            return
        }
        
        isRunningTest = true
        testMessage = "正在创建测试用户..."
        
        do {
            let user = try await helper.createTestUser()
            lastCreatedUser = user
            testMessage = "✅ 成功创建测试用户: \(user.username)"
            
            // 自动验证持久化
            await verifyPersistence()
            
        } catch {
            testMessage = "❌ 创建测试用户失败: \(error.localizedDescription)"
        }
        
        isRunningTest = false
    }
    
    @MainActor
    private func testLoginValidation() async {
        guard let helper = testHelper else { return }
        
        isRunningTest = true
        testMessage = "正在测试登录验证..."
        
        do {
            let user = try await helper.testUserLogin(
                phoneNumber: "13800138000",
                password: "TestPassword123"
            )
            testMessage = "✅ 登录验证成功: \(user.username)"
        } catch {
            testMessage = "❌ 登录验证失败: \(error.localizedDescription)"
        }
        
        isRunningTest = false
    }
    
    @MainActor
    private func verifyPersistence() async {
        guard let helper = testHelper else { return }
        
        isRunningTest = true
        testMessage = "正在验证数据持久化..."
        
        do {
            let result = try await helper.verifyDataPersistence()
            testResult = result
            
            if result.isHealthy {
                testMessage = "✅ 数据持久化机制正常"
            } else {
                testMessage = "⚠️ 发现数据完整性问题"
            }
        } catch {
            testMessage = "❌ 验证失败: \(error.localizedDescription)"
        }
        
        isRunningTest = false
    }
    
    @MainActor
    private func runFullTest() async {
        guard let helper = testHelper else { return }
        
        isRunningTest = true
        
        do {
            // 步骤1：创建测试用户
            testMessage = "第1步：创建测试用户..."
            let user = try await helper.createTestUser()
            lastCreatedUser = user
            
            // 步骤2：测试登录验证
            testMessage = "第2步：测试登录验证..."
            _ = try await helper.testUserLogin(
                phoneNumber: "13800138000",
                password: "TestPassword123"
            )
            
            // 步骤3：创建测试习惯
            testMessage = "第3步：创建测试习惯..."
            _ = try await helper.createTestHabit(for: user)
            
            // 步骤4：验证数据持久化
            testMessage = "第4步：验证数据持久化..."
            let result = try await helper.verifyDataPersistence()
            testResult = result
            
            if result.isHealthy {
                testMessage = "🎉 完整测试通过！数据持久化机制工作正常"
            } else {
                testMessage = "⚠️ 测试完成，但发现数据完整性问题"
            }
            
        } catch {
            testMessage = "❌ 完整测试失败: \(error.localizedDescription)"
        }
        
        isRunningTest = false
    }
    
    @MainActor
    private func cleanupTestData() async {
        guard let helper = testHelper else { return }
        
        isRunningTest = true
        testMessage = "正在清理测试数据..."
        
        do {
            try await helper.cleanupTestData()
            testMessage = "✅ 测试数据已清理"
            testResult = nil
            lastCreatedUser = nil
        } catch {
            testMessage = "❌ 清理失败: \(error.localizedDescription)"
        }
        
        isRunningTest = false
    }
}

// MARK: - 预览

#Preview {
    EADataPersistenceTestView()
        .modelContainer(for: EAUser.self, inMemory: true)
} 