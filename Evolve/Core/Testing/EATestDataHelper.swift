import SwiftData
import Foundation

/// 测试数据辅助工具（阶段4：认证流程集成测试）
/// 用于验证数据持久化机制是否正常工作
@MainActor
class EATestDataHelper {
    
    private let repositoryContainer: EARepositoryContainer
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - 测试用户创建和验证
    
    /// 创建测试用户并验证持久化
    func createTestUser() async throws -> EAUser {
        let phoneNumber = "13800138000"
        let password = "TestPassword123"
        let username = "测试用户\(Int.random(in: 1000...9999))"
        
        // 创建用户（包含认证信息）
        let user = try await repositoryContainer.userRepository.createUserWithAuth(
            username: username,
            email: nil,
            phoneNumber: phoneNumber,
            password: password
        )
        
        // 验证用户是否成功保存
        guard let savedUser = await repositoryContainer.userRepository.fetchUser(by: user.id) else {
            throw TestError.userNotSaved
        }
        
        // 验证认证信息是否正确关联
        guard let authInfo = savedUser.dataProfile?.authInfo else {
            throw TestError.authInfoMissing
        }
        
        // 验证基本关系是否正确建立
        assert(authInfo.phoneNumber == phoneNumber, "电话号码不匹配")
        assert(savedUser.username == username, "用户名不匹配")
        assert(savedUser.settings != nil, "用户设置未创建")
        assert(savedUser.socialProfile != nil, "社交档案未创建")
        assert(savedUser.dataProfile != nil, "数据档案未创建")
        
        return savedUser
    }
    
    /// 测试用户登录验证
    func testUserLogin(phoneNumber: String, password: String) async throws -> EAUser {
        guard let user = try await repositoryContainer.userRepository.validateCredentials(
            phoneNumber: phoneNumber,
            password: password
        ) else {
            throw TestError.loginValidationFailed
        }
        
        return user
    }
    
    /// 为用户创建测试习惯
    func createTestHabit(for user: EAUser) async throws -> EAHabit {
        let habitName = "测试习惯_\(Int.random(in: 1000...9999))"
        
        // 创建测试习惯
        let habit = try await repositoryContainer.habitRepository.createHabit(
            name: habitName,
            iconName: "heart.fill",
            targetFrequency: 1,
            frequencyType: "daily",
            category: "health",
            difficulty: "easy",
            for: user.id
        )
        
        // 验证关系是否正确建立
        let userHabits = try await repositoryContainer.habitRepository.fetchHabits(for: user.id)
        guard userHabits.contains(where: { $0.id == habit.id }) else {
            throw TestError.habitCreationFailed
        }
        
        return habit
    }
    
    /// 验证数据持久化机制
    func verifyDataPersistence() async throws -> PersistenceTestResult {
        var issues: [String] = []
        var testedCount = 0
        
        // 测试1：验证当前用户是否可以获取
        do {
            if (try await repositoryContainer.getCurrentUser()) != nil {
                testedCount += 1
                // 当前用户获取成功
            } else {
                issues.append("无法获取当前用户")
            }
        } catch {
            issues.append("获取当前用户失败: \(error.localizedDescription)")
        }
        
        return PersistenceTestResult(
            isHealthy: issues.isEmpty,
            testedItemsCount: testedCount,
            issuesFound: issues.count,
            details: issues,
            summary: generateSummary(healthy: issues.isEmpty, tested: testedCount, issues: issues.count)
        )
    }
    
    /// 清理测试数据
    func cleanupTestData() async throws {
        // 注意：在实际应用中谨慎使用此方法
        // 仅清理测试相关的数据
        // 清理功能在生产环境中已禁用，确保数据安全
        
        // 在测试环境中可以实现具体的清理逻辑
        // 例如：删除用户名包含"测试"的用户
    }
    
    // MARK: - 辅助方法
    
    private func generateSummary(healthy: Bool, tested: Int, issues: Int) -> String {
        return """
        数据持久化测试结果：
        - 测试项目: \(tested)个
        - 发现问题: \(issues)个
        - 整体状态: \(healthy ? "✅ 健康" : "❌ 异常")
        """
    }
}

// MARK: - 测试结果结构

struct PersistenceTestResult {
    var isHealthy: Bool
    var testedItemsCount: Int
    var issuesFound: Int
    var details: [String]
    var summary: String
}

// MARK: - 测试错误

enum TestError: Error, LocalizedError {
    case userCreationFailed
    case authInfoMissing
    case loginValidationFailed
    case habitCreationFailed
    case dataIntegrityError
    case userNotSaved
    
    var errorDescription: String? {
        switch self {
        case .userCreationFailed:
            return "用户创建失败"
        case .authInfoMissing:
            return "认证信息缺失"
        case .loginValidationFailed:
            return "登录验证失败"
        case .habitCreationFailed:
            return "习惯创建失败"
        case .dataIntegrityError:
            return "数据完整性错误"
        case .userNotSaved:
            return "用户未保存"
        }
    }
} 