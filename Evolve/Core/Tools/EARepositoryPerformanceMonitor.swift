//
//  EARepositoryPerformanceMonitor.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-06.
//

import Foundation
import SwiftData
import Combine

// MARK: - 性能监控器

/// Repository性能监控器
/// 用于监控数据访问性能，收集性能指标，优化缓存策略
@MainActor
class EARepositoryPerformanceMonitor: ObservableObject {
    
    // MARK: - 性能指标
    
    /// 性能指标结构
    struct PerformanceMetrics {
        let operationType: String
        let duration: TimeInterval
        let timestamp: Date
        let success: Bool
        let recordCount: Int?
        
        init(operationType: String, duration: TimeInterval, success: Bool, recordCount: Int? = nil) {
            self.operationType = operationType
            self.duration = duration
            self.timestamp = Date()
            self.success = success
            self.recordCount = recordCount
        }
    }
    
    // MARK: - 私有属性
    
    /// 性能指标历史记录（最多保留1000条）
    private var metricsHistory: [PerformanceMetrics] = []
    
    /// 性能阈值配置
    private let performanceThresholds: [String: TimeInterval] = [
        "fetch": 0.5,      // 查询操作：500ms
        "insert": 0.2,     // 插入操作：200ms
        "update": 0.3,     // 更新操作：300ms
        "delete": 0.2,     // 删除操作：200ms
        "complex": 1.0     // 复杂操作：1000ms
    ]
    
    /// 缓存命中率统计
    private var cacheHits: Int = 0
    private var cacheMisses: Int = 0
    
    // MARK: - 公开属性
    
    /// 当前平均响应时间
    @Published var averageResponseTime: TimeInterval = 0.0
    
    /// 缓存命中率
    @Published var cacheHitRate: Double = 0.0
    
    /// 慢查询警告数量
    @Published var slowQueryCount: Int = 0
    
    // MARK: - 单例实例
    
    // 移除单例模式
    // static let shared = EARepositoryPerformanceMonitor()
    
    // 改为公开初始化器，支持依赖注入
    init() {
        // 启动定期清理任务
        startPeriodicCleanup()
    }
    
    // MARK: - 性能监控方法
    
    /// 开始监控操作
    /// - Parameter operationType: 操作类型
    /// - Returns: 监控令牌
    func startMonitoring(operationType: String) -> PerformanceToken {
        return PerformanceToken(operationType: operationType, startTime: CFAbsoluteTimeGetCurrent())
    }
    
    /// 结束监控操作
    /// - Parameters:
    ///   - token: 监控令牌
    ///   - success: 操作是否成功
    ///   - recordCount: 处理的记录数量
    func endMonitoring(token: PerformanceToken, success: Bool, recordCount: Int? = nil) {
        let duration = CFAbsoluteTimeGetCurrent() - token.startTime
        let metrics = PerformanceMetrics(
            operationType: token.operationType,
            duration: duration,
            success: success,
            recordCount: recordCount
        )
        
        // 记录性能指标
        recordMetrics(metrics)
        
        // 检查性能阈值
        checkPerformanceThreshold(metrics)
    }
    
    /// 记录缓存命中
    func recordCacheHit() {
        cacheHits += 1
        updateCacheHitRate()
    }
    
    /// 记录缓存未命中
    func recordCacheMiss() {
        cacheMisses += 1
        updateCacheHitRate()
    }
    
    // MARK: - 性能分析方法
    
    /// 获取操作类型的平均响应时间
    /// - Parameter operationType: 操作类型
    /// - Returns: 平均响应时间
    func getAverageResponseTime(for operationType: String) -> TimeInterval {
        let relevantMetrics = metricsHistory.filter { $0.operationType == operationType && $0.success }
        guard !relevantMetrics.isEmpty else { return 0.0 }
        
        let totalDuration = relevantMetrics.reduce(0.0) { $0 + $1.duration }
        return totalDuration / Double(relevantMetrics.count)
    }
    
    /// 获取慢查询列表
    /// - Returns: 慢查询指标列表
    func getSlowQueries() -> [PerformanceMetrics] {
        return metricsHistory.filter { metrics in
            guard let threshold = performanceThresholds[metrics.operationType] else { return false }
            return metrics.duration > threshold
        }
    }
    
    /// 获取性能报告
    /// - Returns: 性能报告字符串
    func getPerformanceReport() -> String {
        let totalOperations = metricsHistory.count
        let successfulOperations = metricsHistory.filter { $0.success }.count
        let successRate = totalOperations > 0 ? Double(successfulOperations) / Double(totalOperations) * 100 : 0
        
        return """
        📊 Repository性能报告
        ==================
        总操作数: \(totalOperations)
        成功率: \(String(format: "%.1f", successRate))%
        平均响应时间: \(String(format: "%.3f", averageResponseTime))s
        缓存命中率: \(String(format: "%.1f", cacheHitRate))%
        慢查询数量: \(slowQueryCount)
        """
    }
    
    // MARK: - 私有方法
    
    /// 记录性能指标
    private func recordMetrics(_ metrics: PerformanceMetrics) {
        metricsHistory.append(metrics)
        
        // 限制历史记录数量
        if metricsHistory.count > 1000 {
            metricsHistory.removeFirst(100) // 移除最旧的100条记录
        }
        
        // 更新平均响应时间
        updateAverageResponseTime()
    }
    
    /// 检查性能阈值
    private func checkPerformanceThreshold(_ metrics: PerformanceMetrics) {
        guard let threshold = performanceThresholds[metrics.operationType] else { return }
        
        if metrics.duration > threshold {
            slowQueryCount += 1
            
            #if DEBUG
            print("⚠️ 慢查询警告: \(metrics.operationType) 耗时 \(String(format: "%.3f", metrics.duration))s (阈值: \(threshold)s)")
            #endif
        }
    }
    
    /// 更新平均响应时间
    private func updateAverageResponseTime() {
        let successfulMetrics = metricsHistory.filter { $0.success }
        guard !successfulMetrics.isEmpty else {
            averageResponseTime = 0.0
            return
        }
        
        let totalDuration = successfulMetrics.reduce(0.0) { $0 + $1.duration }
        averageResponseTime = totalDuration / Double(successfulMetrics.count)
    }
    
    /// 更新缓存命中率
    private func updateCacheHitRate() {
        let totalCacheOperations = cacheHits + cacheMisses
        cacheHitRate = totalCacheOperations > 0 ? Double(cacheHits) / Double(totalCacheOperations) * 100 : 0.0
    }
    
    /// 启动定期清理任务
    private func startPeriodicCleanup() {
        // 每小时清理一次过期的性能数据
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.cleanupOldMetrics()
            }
        }
    }
    
    /// 清理过期的性能指标
    private func cleanupOldMetrics() {
        let oneHourAgo = Date().addingTimeInterval(-3600)
        metricsHistory.removeAll { $0.timestamp < oneHourAgo }
        
        // 重置计数器
        if metricsHistory.isEmpty {
            cacheHits = 0
            cacheMisses = 0
            slowQueryCount = 0
            updateCacheHitRate()
            updateAverageResponseTime()
        }
    }
}

// MARK: - 性能监控令牌

/// 性能监控令牌
struct PerformanceToken {
    let operationType: String
    let startTime: CFAbsoluteTime
}

// MARK: - Repository扩展：性能监控

extension EARepositoryPerformanceMonitor {
    
    /// 便捷方法：监控异步操作
    /// - Parameters:
    ///   - operationType: 操作类型
    ///   - operation: 要监控的异步操作
    /// - Returns: 操作结果
    func monitor<T>(
        operationType: String,
        operation: () async throws -> T
    ) async rethrows -> T {
        let token = startMonitoring(operationType: operationType)
        
        do {
            let result = try await operation()
            endMonitoring(token: token, success: true)
            return result
        } catch {
            endMonitoring(token: token, success: false)
            throw error
        }
    }
    
    /// 便捷方法：监控同步操作
    /// - Parameters:
    ///   - operationType: 操作类型
    ///   - operation: 要监控的同步操作
    /// - Returns: 操作结果
    func monitor<T>(
        operationType: String,
        operation: () throws -> T
    ) rethrows -> T {
        let token = startMonitoring(operationType: operationType)
        
        do {
            let result = try operation()
            endMonitoring(token: token, success: true)
            return result
        } catch {
            endMonitoring(token: token, success: false)
            throw error
        }
    }
} 