import SwiftUI
import SwiftData

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EAAtlasSheetType: Identifiable {
    case habitCreation
    case habitDetail(EAHabit)
    case habitActionMenu(EAHabit)
    case habitEdit(EAHabit)  // 新增：编辑习惯页面
    
    var id: String {
        switch self {
        case .habitCreation: return "habitCreation"
        case .habitDetail(let habit): return "habitDetail_\(habit.id)"
        case .habitActionMenu(let habit): return "habitActionMenu_\(habit.id)"
        case .habitEdit(let habit): return "habitEdit_\(habit.id)"  // 新增
        }
    }
}

/// Atlas页面 - 计划管理和创建
/// ✅ 原始设计：导航标题+发布按钮、习惯总览组件、我的习惯列表组件
struct EAAtlasView: View {
    // MARK: - 环境依赖
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    // MARK: - ViewModel
    @StateObject private var viewModel: EAAtlasViewModel
    
    // MARK: - Sheet状态管理
    @State private var activeSheet: EAAtlasSheetType?
    
    // MARK: - 删除确认状态
    @State private var showDeleteAlert = false
    @State private var habitToDelete: EAHabit?
    
    // MARK: - 初始化
    init() {
        _viewModel = StateObject(wrappedValue: EAAtlasViewModel())
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景延伸到整个视口（包括安全区域）
                EABackgroundView()
                    .ignoresSafeArea(.all)
                
                // 主内容区域 - 精确控制，确保内容永远不会进入安全区域
                VStack(spacing: 0) {
                    // 顶部安全区域占位 - 完全透明但占据空间，实现遮挡效果
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: calculateSafeTopMaskHeight(geometry: geometry))
                        .allowsHitTesting(false) // 不接收触摸事件
                    
                    // 可滚动内容区域 - 严格限制在安全区域下方
                    ScrollView {
                        VStack(spacing: 24) {
                            // 📊 习惯总览组件
                            habitOverviewSection
                            
                            // 📝 我的习惯列表组件
                            habitsListSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 16) // 减少顶部间距，因为已经有占位区域
                        .padding(.bottom, 100) // 为底部Tab导航留出空间
                    }
                    .frame(
                        maxWidth: .infinity,
                        maxHeight: calculateContentHeight(geometry: geometry)
                    )
                    .contentMargins(.top, 0, for: .scrollContent) // iOS 17+ 特性：确保内容不会超出顶部
                    .clipped() // 严格裁剪，确保内容不会超出边界
                }
            }
        }
        // ✅ Sheet管理
        .sheet(item: $activeSheet) { sheetType in
            sheetContent(for: sheetType)
        }
        // ✅ 删除确认Alert
        .alert("删除计划", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let habit = habitToDelete {
                    Task {
                        await deleteHabit(habit)
                    }
                }
            }
        } message: {
            if let habit = habitToDelete {
                Text("确定要删除计划「\(habit.name)」吗？此操作无法撤销。")
            }
        }
        .onAppear {
            setupViewModel()
        }
        .onChange(of: sessionManager.currentUser?.id) {
            setupViewModel()
        }
    }
    
    // MARK: - Sheet内容管理
    @ViewBuilder
    private func sheetContent(for sheetType: EAAtlasSheetType) -> some View {
        switch sheetType {
        case .habitCreation:
            EAHabitCreationView()
        case .habitDetail(let habit):
            EAHabitDetailView(habit: habit)
        case .habitEdit(let habit):  // 新增：编辑页面
            EAHabitCreationView(editingHabit: habit)
        case .habitActionMenu(let habit):
            EAHabitActionMenu(
                habit: habit,
                onEdit: {
                    // 编辑功能 - 关闭当前菜单并打开编辑页面
                    activeSheet = nil
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        activeSheet = .habitEdit(habit)
                    }
                },
                onDelete: {
                    // 删除功能 - 关闭当前菜单并显示删除确认
                    activeSheet = nil
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        habitToDelete = habit
                        showDeleteAlert = true
                    }
                }
            )
            .presentationDetents([.height(280)])
            .presentationDragIndicator(.visible)
        }
    }
    
    // MARK: - 删除习惯功能
    private func deleteHabit(_ habit: EAHabit) async {
        await viewModel.deleteHabit(habit)
    }
    
    // MARK: - 设置ViewModel
    private func setupViewModel() {
        if let container = repositoryContainer {
            viewModel.setRepositoryContainer(container)
        }
        viewModel.setSessionManager(sessionManager)
    }
    
    // MARK: - 📊 习惯总览统计组件
    private var habitOverviewSection: some View {
        EAHabitOverviewCard(
            totalHabits: viewModel.totalHabits,
            activeHabits: viewModel.activeHabits,
            averageCompletionRate: viewModel.averageCompletionRate,
            onCreateHabit: {
                activeSheet = .habitCreation
            }
        )
    }
    
    // MARK: - 📝 我的习惯列表组件
    private var habitsListSection: some View {
        VStack(spacing: 16) {
            // 列表标题
            HStack {
                Text("我的计划")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                if !viewModel.habits.isEmpty {
                    Text("\(viewModel.habits.count)个")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .padding(.horizontal, 4)
            
            // 习惯列表内容
            if viewModel.isLoading {
                habitLoadingView
            } else if viewModel.habits.isEmpty {
                emptyStateView
            } else {
                habitListContent
            }
        }
    }
    
    // MARK: - 加载状态视图
    private var habitLoadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .tint(.white)
                .scaleEffect(1.2)
            
            Text("加载计划中...")
                .font(.system(size: 16))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(height: 120)
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        EAAtlasEmptyStateView {
            activeSheet = .habitCreation
        }
    }
    
    // MARK: - 习惯列表内容
    private var habitListContent: some View {
        LazyVStack(spacing: 12) {
            ForEach(viewModel.habits) { habit in
                EAEcoHabitCard(
                    habit: habit,
                    onTap: {
                        activeSheet = .habitDetail(habit)
                    },
                    onDeleteHabit: {
                        habitToDelete = habit
                        showDeleteAlert = true
                    }
                )
                .transition(.asymmetric(
                    insertion: .move(edge: .trailing).combined(with: .opacity),
                    removal: .move(edge: .leading).combined(with: .opacity)
                ))
            }
        }
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: viewModel.habits)
    }
    
    // MARK: - 计算安全区域高度
    private func calculateSafeTopMaskHeight(geometry: GeometryProxy) -> CGFloat {
        let topSafeArea = geometry.safeAreaInsets.top
        
        // 🔑 标准iOS设备安全区域高度计算
        // iOS设备安全区域高度参考：
        // - iPhone 15 Pro/Pro Max (Dynamic Island): 59pt
        // - iPhone 14 Pro/Pro Max (Dynamic Island): 59pt  
        // - iPhone 13/14/15 (Notch): 47pt
        // - iPhone 12 mini/13 mini: 50pt
        // - iPhone SE (无刘海): 20pt (状态栏高度)
        
        // ✅ 精确iOS规范：在系统安全区域基础上增加适量间距，确保与灵动岛/刘海有足够视觉分离
        let calculatedHeight: CGFloat
        if topSafeArea > 20 {
            // 有刘海/灵动岛设备：系统安全区域 + 额外视觉间距
            let extraSpacing: CGFloat
            if topSafeArea >= 55 {
                // 灵动岛设备 (iPhone 14 Pro+)：增加10pt额外间距
                extraSpacing = 10
            } else {
                // 刘海设备 (iPhone X-13)：增加8pt额外间距
                extraSpacing = 8
            }
            calculatedHeight = topSafeArea + extraSpacing
        } else {
            // 无刘海设备：使用状态栏高度，无需额外间距
            calculatedHeight = 20
        }
        
        // 🎯 内容区域额外间距：在安全区域基础上再增加内容缓冲区
        let additionalContentSpacing: CGFloat = 12 // 额外的内容区域间距
        
        return calculatedHeight + additionalContentSpacing
    }
    
    // MARK: - 计算内容区域高度
    private func calculateContentHeight(geometry: GeometryProxy) -> CGFloat {
        let screenHeight = geometry.size.height
        let topMaskHeight = calculateSafeTopMaskHeight(geometry: geometry)
        let bottomSafeArea = geometry.safeAreaInsets.bottom
        let tabBarHeight: CGFloat = 49 // iOS标准Tab栏高度
        
        // 可用内容高度 = 屏幕高度 - 顶部遮挡高度 - 底部安全区域 - Tab栏高度
        let availableHeight = screenHeight - topMaskHeight - bottomSafeArea - tabBarHeight
        
        return max(availableHeight, 300) // 确保最小高度
    }
}

#Preview("Atlas页面") {
    EAAtlasView()
        .modelContainer(PreviewData.container)
        .environmentObject(EASessionManager())
} 