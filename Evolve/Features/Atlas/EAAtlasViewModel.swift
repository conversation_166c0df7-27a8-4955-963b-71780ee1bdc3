import Foundation
import SwiftUI
import SwiftData
import Observation

// MARK: - Atlas视图模型
@MainActor
final class EAAtlasViewModel: ObservableObject {
    
    // MARK: - 状态属性
    @Published var habits: [EAHabit] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingHabitCreation = false
    @Published var selectedHabit: EAHabit?
    @Published var showingHabitDetail = false
    
    // MARK: - 统计属性（适配EAAtlasComponents需求）
    @Published var totalHabits: Int = 0
    @Published var activeHabits: Int = 0
    @Published var averageCompletionRate: Double = 0.0
    
    // MARK: - 依赖注入
    private var repositoryContainer: EARepositoryContainer?
    private var sessionManager: EASessionManager?
    
    // 🔑 新增：通知观察者管理
    private var notificationObservers: [NSObjectProtocol] = []
    
    // MARK: - 初始化
    init() {
        // 🔑 关键修复：设置数据变化通知监听
        setupNotificationObservers()
    }
    
    // 🔑 新增：析构函数，清理通知观察者
    deinit {
        // 清理通知观察者，防止内存泄漏
        notificationObservers.forEach { observer in
            NotificationCenter.default.removeObserver(observer)
        }
        notificationObservers.removeAll()
    }
    
    // MARK: - 公共方法
    
    /// 设置Repository容器并加载数据
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        Task {
            await loadHabits()
        }
    }
    
    /// 🔑 关键修复：设置SessionManager依赖
    func setSessionManager(_ sessionManager: EASessionManager) {
        self.sessionManager = sessionManager
        Task {
            await loadHabits()
        }
    }
    
    /// 🔑 关键修复：获取当前用户ID
    private func getCurrentUserId() -> UUID? {
        return sessionManager?.currentUser?.id
    }
    
    /// 加载习惯数据 - 通过Repository
    func loadHabits() async {
        guard let repositoryContainer = repositoryContainer else { return }
        
        // 🔑 关键修复：获取真实的当前用户ID
        guard let currentUserId = getCurrentUserId() else {
            // 如果没有当前用户，清空习惯列表
            habits = []
            await updateStatistics()
            return
        }
        
        isLoading = true
        
        do {
            // ✅ 修复：使用真实的用户ID获取习惯数据
            let allHabits = try await repositoryContainer.habitRepository.fetchActiveHabits(for: currentUserId)
            habits = allHabits
            
            // 更新统计数据
            await updateStatistics()
            
        } catch {
            errorMessage = "加载习惯失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 更新统计数据
    private func updateStatistics() async {
        totalHabits = habits.count
        activeHabits = habits.filter { $0.isActive }.count
        
        // 计算平均完成率（简化版本）
        if !habits.isEmpty {
            let completionRates = habits.map { habit in
                // 简化计算：基于完成记录数量
                let completionCount = habit.completions.count
                return completionCount > 0 ? 0.8 : 0.0 // 模拟完成率
            }
            averageCompletionRate = completionRates.reduce(0, +) / Double(completionRates.count)
        } else {
            averageCompletionRate = 0.0
        }
    }
    
    /// 创建新习惯 - 通过Repository
    func createHabit(
        name: String,
        iconName: String,
        targetFrequency: Int,
        frequencyType: String,
        category: String,
        difficulty: String = "简单"
    ) async {
        guard let repositoryContainer = repositoryContainer else { return }
        
        // 🔑 关键修复：获取真实的当前用户ID
        guard let currentUserId = getCurrentUserId() else {
            errorMessage = "用户未登录，无法创建习惯"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let _ = try await repositoryContainer.habitRepository.createHabit(
                name: name,
                iconName: iconName,
                targetFrequency: targetFrequency,
                frequencyType: frequencyType,
                category: category,
                difficulty: difficulty,
                for: currentUserId
            )
            await loadHabits()
        } catch {
            errorMessage = "创建习惯失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 删除习惯 - 通过Repository
    func deleteHabit(_ habit: EAHabit) async {
        guard let repositoryContainer = repositoryContainer else { return }
        
        do {
            try await repositoryContainer.habitRepository.deleteHabit(habit)
            await loadHabits()
        } catch {
            errorMessage = "删除习惯失败: \(error.localizedDescription)"
        }
    }
    
    /// 更新习惯 - 通过Repository
    func updateHabit(_ habit: EAHabit) async {
        guard let repositoryContainer = repositoryContainer else { return }
        
        do {
            try await repositoryContainer.habitRepository.saveHabit(habit)
            await loadHabits()
        } catch {
            errorMessage = "更新习惯失败: \(error.localizedDescription)"
        }
    }
    
    // 🔑 新增：设置数据变化通知监听
    private func setupNotificationObservers() {
        // 监听习惯数据变化通知
        let habitDataChangedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.loadHabits()
            }
        }
        
        // 监听习惯删除通知
        let habitDeletedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(EAAppConstants.Today.Notifications.habitDeleted),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.loadHabits()
            }
        }
        
        // 监听习惯编辑通知
        let habitEditedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(EAAppConstants.Today.Notifications.habitEdited),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.loadHabits()
            }
        }
        
        // 监听数据库重置通知
        let databaseResetObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(EAAppConstants.Today.Notifications.databaseReset),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.loadHabits()
            }
        }
        
        // 🔑 新增：监听习惯创建通知（确保图鉴页面实时更新）
        let habitCreatedObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EAHabitCreated"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.loadHabits()
            }
        }
        
        // 保存观察者引用，用于清理
        notificationObservers = [
            habitDataChangedObserver,
            habitDeletedObserver, 
            habitEditedObserver,
            databaseResetObserver,
            habitCreatedObserver
        ]
    }
} 