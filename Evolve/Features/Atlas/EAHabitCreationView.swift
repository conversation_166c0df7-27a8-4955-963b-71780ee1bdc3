import SwiftUI
import SwiftData

/// 计划创建页面
/// 提供完整的计划创建流程，包括基本信息设置、时间安排、AI引导等
struct EAHabitCreationView: View {
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var sessionManager: EASessionManager
    
    let editingHabit: EAHabit?
    @StateObject private var viewModel: EAHabitCreationViewModel
    
    init(editingHabit: EAHabit? = nil) {
        self.editingHabit = editingHabit
        // ✅ 修复：延迟初始化ViewModel，在onAppear中设置依赖
        self._viewModel = StateObject(wrappedValue: EAHabitCreationViewModel(
            sessionManager: EASessionManager(), // 临时占位，在onAppear中重新设置
            editingHabit: editingHabit
        ))
    }
    
    var body: some View {
        ZStack {
            // ✅ 页面整体背景渐变 - 修复：作为最底层背景
            ZStack {
                // 主背景色
                Color.hexColor("002b20")
                
                // 渐变叠加
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("0A2F51"),
                        Color.hexColor("005A4B"), 
                        Color.hexColor("002b20")
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // 微光粒子效果层
                ZStack {
                    // 椭圆光晕效果
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("008080").opacity(0.35),
                            Color.clear
                        ]),
                        center: .topLeading,
                        startRadius: 50,
                        endRadius: 200
                    )
                    
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("38EF7D").opacity(0.18),
                            Color.clear
                        ]),
                        center: .bottomTrailing,
                        startRadius: 30,
                        endRadius: 150
                    )
                    
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("0D47A1").opacity(0.25),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 80,
                        endRadius: 250
                    )
                }
            }
            .ignoresSafeArea(.all)
            
            // ✅ 前景内容层 - 修复：分离导航栏和内容，确保导航栏透明效果
            VStack(spacing: 0) {
                // ✅ 紧凑导航栏 - 修复：在前景层，确保透明效果
                compactNavigationBar
                
                // ✅ 主要滚动内容
                ScrollView {
                    VStack(spacing: 24) {
                        // ✅ 内容顶部间距
                        Color.clear.frame(height: 16)
                        
                        // 计划名称
                        habitNameSection
                        
                        // 图标选择
                        iconSection
                        
                        // 频率选择
                        frequencySection
                        
                        // AI建议
                        if viewModel.showAISuggestion {
                            aiSuggestionSection
                        }
                        
                        // 创建按钮
                        createButton
                            .padding(.horizontal, 16)
                        
                        // 底部间距 - 适配Sheet底部安全区域
                        Color.clear.frame(height: 40)
                    }
                    .padding(.bottom, 20) // Sheet底部额外间距
                }
            }
        }
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage)
        }
        .onAppear {
            // ✅ 修复：设置正确的sessionManager和repositoryContainer
            viewModel.updateSessionManager(sessionManager)
            if let repositoryContainer = sessionManager.repositoryContainer {
                viewModel.setRepositoryContainer(repositoryContainer)
            }
            
            if let editingHabit = editingHabit {
                viewModel.loadHabitData(editingHabit)
            }
        }
        .onDisappear {
            // 🔑 优化：页面消失时重置表单，为下次使用做准备，避免在创建成功时重置
            if editingHabit == nil {
                viewModel.resetFormData()
            }
        }
    }
    
    // MARK: - 紧凑导航栏 - ✅ 参考习惯详情页面设计，解决间距和颜色一致性问题
    private var compactNavigationBar: some View {
        HStack {
            // 取消按钮 - 左对齐
            Button(action: { dismiss() }) {
                Text("取消")
                    .font(.system(size: 17, weight: .regular))
                    .foregroundColor(Color.hexColor("40E0D0"))
                    .frame(width: 44, height: 32) // ✅ 标准触控区域
            }
            
            Spacer()
            
            // 标题 - 居中对齐
            Text(editingHabit != nil ? "编辑计划" : "创建新计划")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            Spacer()
            
            // ✅ 右侧占位空间，保持标题完全居中
            Color.clear
                .frame(width: 44, height: 32)
        }
        .padding(.horizontal, 20) // ✅ 与详情页面保持一致的边距
        .padding(.top, 8) // ✅ 调整顶部间距，确保与状态栏适当分离
        .padding(.bottom, 8) // ✅ 调整底部间距，确保与内容适当分离
        .background(
            // ✅ 修复：与习惯详情页面完全一致的半透明渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black.opacity(0.3),
                    Color.clear
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - Plan Name Section
    private var habitNameSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("计划名称")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white) // 🔑 优化：深色背景使用白色文字
            
            TextField("输入计划名称", text: $viewModel.habitName)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white) // 🔑 优化：深色背景使用白色文字
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.05)) // 🔑 优化：深色背景使用半透明白色
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                )
                .keyboardType(.default)
                .textInputAutocapitalization(.never)
                .autocorrectionDisabled(false)
                .submitLabel(.done)
                .dynamicTypeSize(.large ... .accessibility2)
                .accessibilityLabel("计划名称输入框")
                .accessibilityHint("请输入您想要制定的计划名称")
                .onChange(of: viewModel.habitName) { _, newValue in
                    if !newValue.isEmpty && newValue.count >= 2 {
                        viewModel.generateAISuggestion()
                    }
                }
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Icon Section
    private var iconSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            EAIconCategorySelector(selectedIcon: $viewModel.selectedIcon)
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Frequency Section
    private var frequencySection: some View {
        VStack {
            EAFrequencySelector(
                selectedFrequencyType: $viewModel.selectedFrequencyType,
                selectedWeekdays: $viewModel.selectedWeekdays,
                dailyTarget: $viewModel.dailyTarget,
                monthlyTarget: $viewModel.monthlyTarget,
                selectedMonthlyDates: $viewModel.selectedMonthlyDates,
                monthlyMode: $viewModel.monthlyMode
            )
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - AI Suggestion Section
    private var aiSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "sparkles")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("FFD700"))
                
                Text("AI建议")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white) // 🔑 优化：深色背景使用白色文字
                
                Spacer()
            }
            
            Text(viewModel.aiSuggestion)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white) // 🔑 优化：深色背景使用白色文字
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.hexColor("FFD700").opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.hexColor("FFD700").opacity(0.3), lineWidth: 1)
                        )
                )
            
            Button(action: {
                viewModel.adoptAISuggestion(viewModel.aiSuggestion)
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 14, weight: .medium))
                    Text("采纳")
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.hexColor("40E0D0"),
                                    Color.hexColor("33FFDD")
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: Color.hexColor("40E0D0").opacity(0.3),
                            radius: 4,
                            x: 0,
                            y: 2
                        )
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05)) // 🔑 优化：深色背景使用半透明白色
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - Create Button
    private var createButton: some View {
        Button(action: {
            Task {
                if editingHabit != nil {
                    await viewModel.updateHabit()
                } else {
                    await viewModel.createHabit()
                }
                // ✅ iOS 18.2修复：确保在主线程安全关闭Sheet
                await MainActor.run {
                    if !viewModel.showError {
                        dismiss()
                    }
                }
            }
        }) {
            HStack {
                if viewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: editingHabit != nil ? "checkmark.circle.fill" : "plus.circle.fill")
                        .font(.system(size: 18, weight: .medium))
                    
                    Text(editingHabit != nil ? "保存修改" : "创建计划")
                        .font(.system(size: 16, weight: .medium))
                }
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.hexColor("40E0D0"),
                                Color.hexColor("33FFDD")
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.3),
                                        Color.clear
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: Color.hexColor("40E0D0").opacity(0.3),
                        radius: 10,
                        x: 0,
                        y: 5
                    )
            )
        }
        .disabled(viewModel.habitName.isEmpty || viewModel.isLoading)
        .opacity(viewModel.habitName.isEmpty || viewModel.isLoading ? 0.6 : 1.0)
        .scaleEffect(viewModel.habitName.isEmpty || viewModel.isLoading ? 0.95 : 1.0)
        // 🔑 优化：使用更平滑的动画，避免弹动效果
        .animation(.easeInOut(duration: 0.2), value: viewModel.habitName.isEmpty)
        .animation(.easeInOut(duration: 0.2), value: viewModel.isLoading)
    }
    
    // MARK: - Computed Properties
    private var canCreateHabit: Bool {
        !viewModel.habitName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // 预览区图标渲染
    private var iconPreview: some View {
                    ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(LinearGradient(
                        colors: [Color.blue, Color.purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)
            if isMulticolorSymbol(viewModel.selectedIcon) {
                Image(systemName: viewModel.selectedIcon)
                    .font(.system(size: 32, weight: .medium))
                    .symbolRenderingMode(.multicolor)
            } else {
                Image(systemName: viewModel.selectedIcon)
                    .font(.system(size: 32, weight: .medium))
                    .symbolRenderingMode(.palette)
                    .foregroundStyle(
                        Color.white,
                        Color.white.opacity(0.8),
                        Color.white.opacity(0.6)
                    )
            }
        }
    }
    
    // 工具方法：判断是否为多色符号
    private func isMulticolorSymbol(_ name: String) -> Bool {
        let multicolorSymbols: Set<String> = [
            "soccerball", "basketball.fill", "tennisball.fill", "baseball.fill", "volleyball.fill", "football.fill", "cricket.ball.fill", "hockey.puck.fill", "pills.fill", "flame.fill", "star.fill", "trophy.fill", "medal.fill", "crown.fill", "sparkles", "wand.and.stars", "flag.checkered", "rosette", "diamond.fill", "gem", "hand.thumbsup.fill", "hand.raised.fill", "checkmark.seal.fill", "lightbulb.max.fill", "paintbrush.fill", "photo.fill", "music.note.list", "gamecontroller.fill", "camera.fill", "headphones", "guitars.fill", "dice.fill", "film.fill", "tv.fill", "radio.fill", "speaker.wave.3.fill", "microphone.fill", "ticket.fill", "popcorn.fill", "party.popper.fill", "balloon.fill", "gift.fill", "person.2.wave.2.fill", "birthday.cake.fill", "heart.2.fill", "handshake.fill", "person.3.fill", "bubble.left.and.bubble.right.fill", "megaphone.fill", "bell.fill", "mail.fill", "phone.bubble.left.fill", "video.fill", "shareplay"
        ]
        return multicolorSymbols.contains(name)
    }
}

// MARK: - 预览
#Preview {
    NavigationView {
        EAHabitCreationView()
            .environmentObject(EASessionManager())
            .environment(\.repositoryContainer, EARepositoryContainerImpl.preview())
    }
    .modelContainer(PreviewData.container)
} 