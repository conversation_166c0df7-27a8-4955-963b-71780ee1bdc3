import SwiftUI
import SwiftData
import UserNotifications

/// 计划创建页面的ViewModel
/// 负责管理计划创建流程中的所有状态和业务逻辑
@MainActor
class EAHabitCreationViewModel: ObservableObject {
    // MARK: - Properties
    private var sessionManager: EASessionManager
    private var repositoryContainer: EARepositoryContainer?
    let notificationService: EANotificationService
    
    // MARK: - Published Properties
    @Published var habitName: String = ""
    @Published var selectedIcon: String = "star.fill"
    
    // 🔑 优化：新的频率设置 - 移除默认选择，让用户自己选择
    @Published var selectedFrequencyType: FrequencyType = .weekly
    @Published var selectedWeekdays: Set<Int> = [] // 🔑 优化：不预设默认选择，让用户自己选择
    @Published var dailyTarget: Int = 0 // 🔑 优化：不预设默认选择，让用户自己选择
    @Published var monthlyTarget: Int = 0 // 🔑 优化：不预设默认选择，让用户自己选择
    @Published var monthlyMode: MonthlyMode = .target // 每月执行模式
    @Published var selectedMonthlyDates: Set<Int> = [] // 🔑 优化：不预设默认选择，让用户自己选择
    
    // 向后兼容的频率设置
    @Published var targetFrequency: Int = 3
    
    @Published var selectedTimeSlot: String = "morning"
    @Published var selectedCategory: String = "健康"
    @Published var selectedDifficulty: String = "简单"
    @Published var habitTags: [String] = []
    @Published var aiCoachingStyle: String = "温柔鼓励型"
    
    // 提醒时间设置
    @Published var reminderTimes: [Date] = []
    
    // AI相关属性
    @Published var showAISuggestion: Bool = false
    @Published var aiSuggestion: String = ""
    @Published var aiSuggested: Bool = false
    
    // 错误处理
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    
    // UI状态
    @Published var isLoading: Bool = false
    @Published var showingAlert: Bool = false
    @Published var alertMessage: String = ""
    @Published var currentStep: Int = 1
    
    // 编辑模式
    var editingHabit: EAHabit?
    
    // 计算属性：是否为编辑模式
    var isEditMode: Bool {
        return editingHabit != nil
    }
    
    // 🔑 新增：计算属性 - 是否可以创建习惯
    var canCreateHabit: Bool {
        return !habitName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // 常量
    let totalSteps = 4
    
    // MARK: - 初始化
    
    /// ✅ 修复：移除默认初始化方法，强制要求依赖注入
    /// 初始化方法（强制依赖注入）
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer? = nil, editingHabit: EAHabit? = nil) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        self.editingHabit = editingHabit
        self.notificationService = EANotificationService(sessionManager: sessionManager)
        
        // 如果是编辑模式，加载现有数据
        if let habit = editingHabit {
            loadHabitData(habit)
        }
    }

    
    // MARK: - 公共方法
    
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
    }
    
    /// ✅ 修复：更新SessionManager（用于Environment注入后的更新）
    func updateSessionManager(_ newSessionManager: EASessionManager) {
        self.sessionManager = newSessionManager
    }
    
    /// 生成AI建议
    func generateAISuggestion() {
        Task {
            isLoading = true
            
            // 这里应该调用AI服务生成建议
            // 暂时使用模拟数据
            let suggestion = await generateMockAISuggestion()
            
            // 由于ViewModel已标记@MainActor，直接更新UI状态
            self.aiSuggestion = suggestion
            self.showAISuggestion = true
            self.isLoading = false
        }
    }
    
    /// 创建新习惯（iOS 18.2安全版本）
    /// 
    /// 使用Repository的安全创建方法，确保所有SwiftData操作在同一Context中进行
    func createHabit() async {
        // 获取当前用户ID
        guard let userId = await getCurrentUserId() else { return }
        
        // 验证表单数据
        guard let validatedFrequency = validateFormData() else { return }
        
        isLoading = true
        errorMessage = ""
        
        do {
            guard let container = repositoryContainer else {
                throw NSError(domain: "EAHabitCreationViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "Repository容器未初始化"])
            }
            
            // 准备提醒时间数据
            let reminderTimeStrings: [String]
            if !reminderTimes.isEmpty {
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "HH:mm"
                reminderTimeStrings = reminderTimes.map { dateFormatter.string(from: $0) }
            } else {
                reminderTimeStrings = []
            }
            
            // ✅ iOS 18.2修复：使用Repository的安全创建方法
            let newHabit = try await container.habitRepository.createHabitSafely(
                name: habitName,
                iconName: selectedIcon,
                targetFrequency: validatedFrequency,
                frequencyType: selectedFrequencyType.rawValue,
                selectedWeekdays: Array(selectedWeekdays),
                dailyTarget: dailyTarget,
                monthlyTarget: monthlyTarget,
                monthlyMode: monthlyMode.rawValue,
                selectedMonthlyDates: Array(selectedMonthlyDates),
                category: selectedCategory,
                difficulty: selectedDifficulty,
                reminderTimes: reminderTimeStrings,
                reminderEnabled: !reminderTimes.isEmpty,
                preferredTimeSlot: localizedTimeSlot(selectedTimeSlot),
                for: userId
            )
            
            // 发送通知
            notifyHabitCreated(habitId: newHabit.id)
            
            // ✅ iOS 18.2修复：成功创建后重置状态
            await MainActor.run {
                isLoading = false
                // 重置表单状态，避免状态残留
                resetFormState()
            }
        } catch {
            await MainActor.run {
                handleCreateHabitError(error)
            }
        }
    }
    
    /// 更新习惯
    func updateHabit() async {
        guard let habit = editingHabit else {
            showError = true
            errorMessage = "无法找到要编辑的习惯"
            return
        }
        
        // 由于ViewModel已标记@MainActor，直接更新UI状态
        isLoading = true
        showError = false
        
        // 更新习惯属性（确保关系完整性）
        habit.name = habitName
        habit.iconName = selectedIcon
        habit.frequencyType = selectedFrequencyType.rawValue
        habit.targetFrequency = selectedFrequencyType == .weekly ? selectedWeekdays.count : 
                               (selectedFrequencyType == .daily ? dailyTarget : monthlyTarget)
        habit.selectedWeekdays = Array(selectedWeekdays)
        habit.dailyTarget = dailyTarget
        habit.monthlyTarget = monthlyTarget
        habit.monthlyMode = monthlyMode.rawValue
        habit.selectedMonthlyDates = Array(selectedMonthlyDates)
        habit.category = selectedCategory
        habit.difficulty = selectedDifficulty
        
        // 🔑 新增：保存提醒时间设置
        if !reminderTimes.isEmpty {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "HH:mm"
            
            habit.reminderTimes = reminderTimes.map { date in
                dateFormatter.string(from: date)
            }
            habit.reminderEnabled = true
        } else {
            habit.reminderTimes = []
            habit.reminderEnabled = false
        }
        
        // 🔑 新增：保存时间段偏好（本地化处理）
        habit.preferredTimeSlot = localizedTimeSlot(selectedTimeSlot)
        
        // 保存到数据库
        do {
            guard let container = repositoryContainer else {
                showError = true
                errorMessage = "Repository容器未初始化"
                isLoading = false
                return
            }
            try await container.habitRepository.saveHabit(habit)
        } catch {
            showError = true
            
            // 提供用户友好的错误信息
            let errorDescription = error.localizedDescription.lowercased()
            if errorDescription.contains("incompatible") && 
               errorDescription.contains("nsmanagedobjectmodel") {
                errorMessage = "数据模型需要更新，请重启应用后重试"
            } else {
                errorMessage = "更新习惯失败，请重试"
            }
            
            isLoading = false
            return
        }
        
        // 更新成功
        isLoading = false
        // 发送习惯编辑通知
        NotificationCenter.default.post(name: NSNotification.Name("HabitEdited"), object: habit)
        // 发送习惯数据变化通知
        NotificationCenter.default.post(name: NSNotification.Name("HabitDataChanged"), object: nil)
        // 通知父视图关闭
        NotificationCenter.default.post(name: NSNotification.Name("HabitUpdated"), object: habit)
    }
    
    /// 采纳AI建议
    func adoptAISuggestion(_ suggestion: String) {
        habitName = suggestion
        aiSuggested = true
        showAISuggestion = false
    }
    
    /// 重置表单（公共方法，供外部调用）
    func resetFormData() {
        resetForm()
    }
    
    /// 重置表单状态（iOS 18.2修复：防止状态残留）
    private func resetFormState() {
        errorMessage = ""
        showError = false
        aiSuggested = false
        showAISuggestion = false
    }
    
    // MARK: - 私有方法
    
    /// 加载习惯数据（用于编辑模式）
    func loadHabitData(_ habit: EAHabit) {
        // 设置编辑模式
        editingHabit = habit
        
        // 🔑 数据验证：确保关键字段存在
        guard !habit.name.isEmpty && !habit.iconName.isEmpty else {
            // 数据不完整，使用安全默认值
            habitName = habit.name.isEmpty ? "新习惯" : habit.name
            selectedIcon = habit.iconName.isEmpty ? "star.fill" : habit.iconName
            return
        }
        
        // 加载习惯基本信息
        habitName = habit.name
        selectedIcon = habit.iconName
        selectedCategory = habit.category.isEmpty ? "健康" : habit.category
        selectedDifficulty = habit.difficulty.isEmpty ? "简单" : habit.difficulty
        
        // 🔑 加载提醒时间设置
        if !habit.reminderTimes.isEmpty {
            // 将字符串时间转换为Date对象
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "HH:mm"
            
            reminderTimes = habit.reminderTimes.compactMap { timeString in
                return dateFormatter.date(from: timeString)
            }
        }
        
        // 🔑 加载时间段偏好
        if let timeSlot = habit.preferredTimeSlot, !timeSlot.isEmpty {
            selectedTimeSlot = timeSlot
        }
        
        // 🔑 优化：加载频率设置（确保完整性和数据验证）
        // 1. 先加载频率类型
        if let frequencyType = FrequencyType(rawValue: habit.frequencyType) {
            selectedFrequencyType = frequencyType
        } else {
            // 默认频率类型
            selectedFrequencyType = .daily
        }
        
        // 2. 验证并加载所有频率相关数据
        selectedWeekdays = habit.selectedWeekdays.isEmpty ? [1, 3, 5] : Set(habit.selectedWeekdays)
        dailyTarget = max(1, habit.dailyTarget) // 确保至少为1
        monthlyTarget = max(1, habit.monthlyTarget) // 确保至少为1
        
        // 3. 🔑 关键修复：确保月度模式正确加载并验证
        if let mode = MonthlyMode(rawValue: habit.monthlyMode) {
            monthlyMode = mode
        } else {
            // 如果没有保存的模式或无效，根据数据推断
            monthlyMode = habit.selectedMonthlyDates.isEmpty ? .target : .dates
        }
        
        // 4. 验证并加载月度自定义日期
        let validMonthlyDates = habit.selectedMonthlyDates.filter { $0 >= 1 && $0 <= 31 }
        selectedMonthlyDates = validMonthlyDates.isEmpty ? [1, 15] : Set(validMonthlyDates)
        
        // 5. 🔑 确保向后兼容的频率数值正确（增加验证）
        let calculatedFrequency = {
            switch selectedFrequencyType {
            case .weekly:
                return selectedWeekdays.count
            case .daily:
                return dailyTarget
            case .monthly:
                return monthlyMode == .target ? monthlyTarget : selectedMonthlyDates.count
            }
        }()
        
        targetFrequency = max(1, habit.targetFrequency > 0 ? habit.targetFrequency : calculatedFrequency)
    }
    
    private func generateMockAISuggestion() async -> String {
        // 模拟AI建议生成
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        
        let suggestions = [
            "建议从每周3次开始，逐步增加频率。选择固定的时间点有助于养成习惯。",
            "可以考虑将习惯与现有的日常活动结合，比如刷牙后或睡前。",
            "设置小而具体的目标，比如每次10分钟，比设置过大的目标更容易坚持。"
        ]
        
        return suggestions.randomElement() ?? suggestions[0]
    }
    
    private func resetForm() {
        habitName = ""
        selectedIcon = "⭐"
        selectedFrequencyType = .weekly
        selectedWeekdays = [] // 🔑 优化：重置时不设置默认选择
        dailyTarget = 0 // 🔑 优化：重置时不设置默认选择
        monthlyTarget = 0 // 🔑 优化：重置时不设置默认选择
        monthlyMode = .target
        selectedMonthlyDates = [] // 🔑 优化：重置时不设置默认选择
        targetFrequency = 3
        selectedCategory = "健康"
        selectedDifficulty = "简单"
        habitTags = []
        aiCoachingStyle = "温柔鼓励型"

        aiSuggested = false
    }
    
    // MARK: - 辅助方法
    private func showError(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    /// 🔑 新增：本地化时间段显示
    private func localizedTimeSlot(_ timeSlot: String) -> String {
        switch timeSlot.lowercased() {
        case "morning", "早晨":
            return "早晨"
        case "afternoon", "下午":
            return "下午"
        case "evening", "晚上":
            return "晚上"
        case "night", "深夜":
            return "深夜"
        default:
            return timeSlot // 如果已经是中文或其他值，直接返回
        }
    }
    
    func nextStep() {
        if currentStep < totalSteps {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentStep += 1
            }
        }
    }
    
    func previousStep() {
        if currentStep > 1 {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentStep -= 1
            }
        }
    }
    
    func addTag(_ tag: String) {
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedTag.isEmpty && !habitTags.contains(trimmedTag) {
            habitTags.append(trimmedTag)
        }
    }
    
    func removeTag(_ tag: String) {
        habitTags.removeAll { $0 == tag }
    }
    
    // MARK: - 私有辅助方法
    
    /// 获取当前用户ID（iOS 18.2安全版本）
    /// - Returns: 当前用户ID，失败时返回nil并设置错误信息
    private func getCurrentUserId() async -> UUID? {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        return await MainActor.run {
            guard let userId = sessionManager.currentUser?.id else {
                errorMessage = "请先登录后再创建习惯"
                showError = true
                return nil
            }
            return userId
        }
    }
    
    /// 获取当前用户（保留用于其他用途）
    /// - Returns: 当前用户对象，失败时返回nil并设置错误信息
    private func getCurrentUser() async -> EAUser? {
        guard let container = repositoryContainer else {
            errorMessage = "Repository容器未初始化"
            return nil
        }
        
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let userId = sessionManager.currentUser?.id else {
            errorMessage = "请先登录后再创建习惯"
            return nil
        }
        
        guard let user = await container.userRepository.fetchUser(by: userId) else {
            errorMessage = "无法获取当前用户信息，请重试"
            return nil
        }
        return user
    }
    
    /// 验证表单数据
    /// - Returns: 验证后的目标频率，验证失败时返回nil并设置错误信息
    private func validateFormData() -> Int? {
        let validatedTargetFrequency: Int
        
        switch selectedFrequencyType {
        case .weekly:
            if selectedWeekdays.isEmpty {
                errorMessage = "请选择至少一天执行习惯"
                showError = true
                return nil
            }
            validatedTargetFrequency = selectedWeekdays.count
            
        case .daily:
            if dailyTarget <= 0 {
                errorMessage = "请选择每日目标次数"
                showError = true
                return nil
            }
            validatedTargetFrequency = dailyTarget
            
        case .monthly:
            if monthlyMode == .target {
                if monthlyTarget <= 0 {
                    errorMessage = "请选择每月目标次数"
                    showError = true
                    return nil
                }
                validatedTargetFrequency = monthlyTarget
            } else {
                if selectedMonthlyDates.isEmpty {
                    errorMessage = "请选择至少一个执行日期"
                    showError = true
                    return nil
                }
                validatedTargetFrequency = selectedMonthlyDates.count
            }
        }
        
        return validatedTargetFrequency
    }
    
    /// 创建习惯对象
    /// - Parameter validatedFrequency: 验证后的目标频率
    /// - Returns: 配置完整的习惯对象
    private func createHabitObject(validatedFrequency: Int) -> EAHabit {
        let newHabit = EAHabit(
            name: habitName,
            iconName: selectedIcon,
            targetFrequency: validatedFrequency,
            preferredTimeSlot: localizedTimeSlot(selectedTimeSlot)
        )
        
        // 设置详细的频率配置
        configureHabitFrequency(habit: newHabit)
        
        // 设置提醒配置
        configureHabitReminders(habit: newHabit)
        
        // 设置其他属性
        newHabit.category = selectedCategory
        newHabit.difficulty = selectedDifficulty
        
        return newHabit
    }
    
    /// 配置习惯频率设置
    /// - Parameter habit: 要配置的习惯对象
    private func configureHabitFrequency(habit: EAHabit) {
        habit.frequencyType = selectedFrequencyType.rawValue
        habit.selectedWeekdays = Array(selectedWeekdays)
        habit.dailyTarget = dailyTarget
        habit.monthlyTarget = monthlyTarget
        habit.monthlyMode = monthlyMode.rawValue
        habit.selectedMonthlyDates = Array(selectedMonthlyDates)
    }
    
    /// 配置习惯提醒设置
    /// - Parameter habit: 要配置的习惯对象
    private func configureHabitReminders(habit: EAHabit) {
        if !reminderTimes.isEmpty {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "HH:mm"
            let reminderTimeStrings = reminderTimes.map { dateFormatter.string(from: $0) }
            habit.reminderTimes = reminderTimeStrings
            habit.reminderEnabled = true
        } else {
            habit.reminderTimes = []
            habit.reminderEnabled = false
        }
    }
    
    /// 保存习惯到数据库（遵循iOS 18+关系赋值顺序）
    /// - Parameters:
    ///   - habit: 要保存的习惯对象
    ///   - user: 关联的用户对象
    private func saveHabitToDatabase(habit: EAHabit, user: EAUser) async throws {
        guard let container = repositoryContainer else {
            throw NSError(domain: "EAHabitCreationViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "Repository容器未初始化"])
        }
        
        // 设置关系
        habit.user = user
        
        // 通过Repository保存
        try await container.habitRepository.saveHabit(habit)
    }
    
    /// 发送习惯创建通知
    /// - Parameter habitId: 创建的习惯ID
    private func notifyHabitCreated(habitId: UUID) {
        NotificationCenter.default.post(
            name: NSNotification.Name("HabitDataChanged"),
            object: habitId
        )
        NotificationCenter.default.post(
            name: NSNotification.Name("HabitCreated"),
            object: habitId
        )
    }
    
    /// 处理创建习惯的错误
    /// - Parameter error: 发生的错误
    private func handleCreateHabitError(_ error: Error) {
        isLoading = false
        errorMessage = "创建习惯失败: \\(error.localizedDescription)"
        showError = true
    }
}

// MARK: - Supporting Types

struct TimeSlot {
    let id: String
    let name: String
    let description: String
}

// MARK: - 功能管理器集成示例

extension EAHabitCreationViewModel {
    
    /// 安全创建习惯（带功能检查）
    func createHabitSafely() async {
        let featureManager = EAFeatureManager()
        
        // 检查AI功能是否可用
        if featureManager.checkAIFeatures() {
            // AI功能可用，可以使用AI建议和分析
            await createHabitWithAISupport()
        } else {
            // AI功能不可用，使用基础创建流程
            await createHabitBasic()
            
            // 显示功能状态信息
            let statusMessage = featureManager.getAIStatusMessage()
            if !featureManager.isAIFeaturesAvailable {
                showUpgradePrompt(message: statusMessage)
            }
        }
    }
    
    /// 带AI支持的习惯创建
    private func createHabitWithAISupport() async {
        // 完整的AI功能实现
        await createHabit()
    }
    
    /// 基础习惯创建（无AI功能）
    private func createHabitBasic() async {
        // 基础创建流程，不依赖AI模型
        await createHabit()
    }
    
    /// 显示升级提示
    private func showUpgradePrompt(message: String) {
        // 显示用户友好的升级提示
        errorMessage = message
        showError = true
    }
} 
