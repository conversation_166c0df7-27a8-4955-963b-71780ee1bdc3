import SwiftUI
import SwiftData
import Foundation

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EAHabitDetailSheetType: Identifiable {
    case editView
    
    var id: String {
        switch self {
        case .editView:
            return "editView"
        }
    }
}

/// 计划详情页面 - 严格遵循MVVM架构
/// 🎯 解决用户反馈的问题：去除大框套小框设计，显示详细执行日期，修复图标显示
/// 🔧 架构修复：正确使用EAHabitDetailViewModel，符合项目MVVM规范
@MainActor
struct EAHabitDetailView: View {
    let habit: EAHabit
    
    // MARK: - 环境依赖
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var sessionManager: EASessionManager
    
    // MARK: - ViewModel（符合MVVM架构）
    @StateObject private var viewModel = EAHabitDetailViewModel()
    
    // MARK: - UI状态管理（仅UI相关）
    @State private var activeSheet: EAHabitDetailSheetType?
    @State private var showDeleteAlert = false
    @State private var showEditSheet = false
    
    @Query private var completions: [EACompletion]
    
    init(habit: EAHabit) {
        self.habit = habit
        // 初始化查询，只获取当前习惯的完成记录
        let habitId = habit.id
        self._completions = Query(
            filter: #Predicate<EACompletion> { completion in
                completion.habit?.id == habitId
            },
            sort: \EACompletion.date,
            order: .reverse
        )
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 紧凑导航栏
            compactNavigationBar
            
            // 主要内容
            ScrollView {
                VStack(spacing: 20) {
                    // 基本信息卡片
                    basicInfoCard
                    
                    // 执行安排信息卡片
                    executionScheduleCard
                    
                    // 能量统计卡片
                    energyStatsCard
                    
                    // AI观察笔记
                    aiInsightCard
                    
                    // 底部间距，为按钮区域留出空间
                    Spacer(minLength: 120)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
        }
        .background(
            // 使用项目标准背景渐变系统
            ZStack {
                // 主背景色
                Color.hexColor("002b20")
                
                // 渐变叠加
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("0A2F51"),
                        Color.hexColor("005A4B"), 
                        Color.hexColor("002b20")
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                
                // 微光粒子效果层
                ZStack {
                    // 椭圆光晕效果
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("008080").opacity(0.35),
                            Color.clear
                        ]),
                        center: .topLeading,
                        startRadius: 50,
                        endRadius: 200
                    )
                    
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("38EF7D").opacity(0.18),
                            Color.clear
                        ]),
                        center: .bottomTrailing,
                        startRadius: 30,
                        endRadius: 150
                    )
                    
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("0D47A1").opacity(0.25),
                            Color.clear
                        ]),
                        center: .center,
                        startRadius: 80,
                        endRadius: 250
                    )
                }
            }
            .ignoresSafeArea()
        )
        .overlay(
            // 底部按钮区域
            bottomButtonArea,
            alignment: .bottom
        )
        .alert("确认删除", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                Task {
                    await deleteHabit()
                }
            }
        } message: {
            Text("删除后无法恢复，确定要删除这个计划吗？")
        }
        .sheet(isPresented: $showEditSheet) {
            EAHabitCreationView(editingHabit: habit)
        }
        .onAppear {
            setupViewModel()
        }
    }
    
    // MARK: - 设置ViewModel
    private func setupViewModel() {
        if let container = repositoryContainer {
            viewModel.setRepositoryContainer(container)
        }
    }
    
    // MARK: - 紧凑导航栏
    private var compactNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 32, height: 32)
                    .background(Color.white.opacity(0.1))
                    .clipShape(Circle())
            }
            
            Spacer()
            
            // 习惯名称
            Text(habit.name)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            Spacer()
            
            // 占位空间，保持标题居中
            Color.clear
                .frame(width: 32, height: 32)
        }
        .padding(.horizontal, 20)
        .padding(.top, 4)
        .padding(.bottom, 4)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black.opacity(0.3),
                    Color.clear
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - 底部按钮区域
    private var bottomButtonArea: some View {
        VStack(spacing: 12) {
            // 编辑按钮
            EAButton(
                title: "编辑计划",
                style: .secondary,
                size: .medium,
                icon: "pencil",
                action: {
                    showEditSheet = true
                }
            )
            
            // 删除按钮
            EAButton(
                title: "彻底移除此计划",
                style: .destructive,
                size: .medium,
                icon: "trash",
                action: {
                    showDeleteAlert = true
                }
            )
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 34) // 安全区域底部间距
        .padding(.top, 16)
        .background(
            // 渐变背景，与页面背景融合
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.hexColor("002b20").opacity(0.8),
                    Color.hexColor("002b20")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    // MARK: - 基本信息卡片
    private var basicInfoCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                // 习惯图标
                EAColorfulIcon(habit.iconName, size: 32)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(habit.name)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Text(targetText)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                // 活跃状态指示器
                VStack(spacing: 4) {
                    Circle()
                        .fill(habit.isActive ? Color.hexColor("40E0D0") : Color.gray)
                        .frame(width: 8, height: 8)
                    
                    Text(habit.isActive ? "活跃" : "暂停")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(habit.isActive ? Color.hexColor("40E0D0") : Color.gray)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 执行安排信息卡片
    private var executionScheduleCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "calendar")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text("执行安排")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 12) {
                // 频率信息
                HStack {
                    Text("执行频率:")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                    
                    Spacer()
                    
                    Text(frequencyText)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                // 执行日期信息
                if !executionDaysText.isEmpty {
                    HStack {
                        Text("执行日期:")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                        
                        Spacer()
                        
                        Text(executionDaysText)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(Color.hexColor("40E0D0"))
                            .multilineTextAlignment(.trailing)
                    }
                }
                
                // 提醒时间
                if !habit.reminderTimes.isEmpty {
                    HStack {
                        Text("提醒时间:")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                        
                        Spacer()
                        
                        Text(habit.reminderTimes.joined(separator: ", "))
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(Color.hexColor("40E0D0"))
                            .multilineTextAlignment(.trailing)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 能量统计卡片
    private var energyStatsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text("能量统计")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "总完成次数",
                    value: "\(viewModel.totalCompletions)",
                    color: Color.hexColor("40E0D0")
                )
                
                StatCard(
                    title: "活跃天数",
                    value: "\(viewModel.activeDays)",
                    color: Color.hexColor("67e8f9")
                )
                
                StatCard(
                    title: "最长连续",
                    value: "\(viewModel.longestStreak)",
                    color: Color.hexColor("FF7F50")
                )
                
                StatCard(
                    title: "平均完成率",
                    value: "\(Int(viewModel.completionRate * 100))%",
                    color: Color.hexColor("98FB98")
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - AI观察笔记
    private var aiInsightCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                
                Text("Aura的观察笔记")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text(viewModel.aiObservation)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
                .lineSpacing(4)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.08))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 计算属性
    
    private var targetText: String {
        switch habit.frequencyType {
        case "weekly":
            return "每周 \(habit.targetFrequency) 次"
        case "daily":
            return "每天 1 次"
        case "monthly":
            if habit.monthlyMode == "target" {
                return "每月 \(habit.monthlyTarget) 次"
            } else {
                return "每月，指定的日期"
            }
        default:
            return "每周 \(habit.targetFrequency) 次"
        }
    }
    
    private var frequencyText: String {
        switch habit.frequencyType {
        case "weekly":
            return "每周 \(habit.targetFrequency) 次"
        case "daily":
            return "每天 1 次"
        case "monthly":
            if habit.monthlyMode == "target" {
                return "每月 \(habit.monthlyTarget) 次"
            } else {
                return "每月，指定的日期"
            }
        default:
            return "每周 \(habit.targetFrequency) 次"
        }
    }
    
    private var executionDaysText: String {
        switch habit.frequencyType {
        case "weekly":
            let weekdayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
            let selectedDays = habit.selectedWeekdays.compactMap { weekday in
                weekday >= 0 && weekday < weekdayNames.count ? weekdayNames[weekday] : nil
            }
            return selectedDays.joined(separator: ", ")
            
        case "monthly":
            if habit.monthlyMode == "dates" {
                let selectedDates = habit.selectedMonthlyDates.map { "每月\($0)日" }
                return selectedDates.joined(separator: ", ")
            } else {
                return ""
            }
            
        case "daily":
            return "每天"
            
        default:
            return ""
        }
    }
    
    // MARK: - 删除习惯操作
    private func deleteHabit() async {
        // ✅ 修复：检查删除结果，只有成功才关闭页面
        let success = await viewModel.deleteHabit(habit)
        
        await MainActor.run {
            if success {
                // 删除成功，关闭页面
                dismiss()
            } else {
                // 删除失败，保持页面打开，错误信息已经在ViewModel中设置
                // 删除失败，错误已通过errorMessage显示
            }
        }
    }
}

// MARK: - 统计卡片组件
struct StatCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(color)
            
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
}

// MARK: - 预览
#Preview {
    // 简化Preview配置，避免崩溃
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: EAHabit.self, EACompletion.self, configurations: config)
    
    let sampleHabit = EAHabit(
        name: "晨间正念计划",
        iconName: "leaf.fill",
        targetFrequency: 5
    )
    
    return EAHabitDetailView(habit: sampleHabit)
        .modelContainer(container)
        .preferredColorScheme(.dark)
} 