import SwiftUI
import SwiftData
import Foundation

/// 计划详情页面ViewModel - 管理计划详情展示和统计分析
/// 严格遵循项目MVVM架构规范，使用@MainActor确保UI更新在主线程
@MainActor
final class EAHabitDetailViewModel: ObservableObject {
    
    // MARK: - 发布状态属性
    
    /// 总完成次数
    @Published var totalCompletions: Int = 0
    
    /// 活跃天数（有完成记录的不重复天数）
    @Published var activeDays: Int = 0
    
    /// 最长连续天数
    @Published var longestStreak: Int = 0
    
    /// 完成率（0.0-1.0）
    @Published var completionRate: Double = 0.0
    
    /// 日历数据（30天）
    @Published var calendarDays: [CalendarDay] = []
    
    /// AI观察笔记
    @Published var aiObservation: String = ""
    
    /// AI建议
    @Published var aiSuggestion: String = ""
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    // MARK: - 私有属性
    
    /// Repository容器（强制依赖注入）
    private var repositoryContainer: EARepositoryContainer?
    
    // MARK: - 初始化
    
    init() {
        // 初始化时不执行数据加载，等待外部设置Repository
    }
    
    // MARK: - 公共方法
    
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
    }
    
    /// 加载计划详情数据
    func loadData(for habit: EAHabit) async {
        isLoading = true
        errorMessage = nil
        
        // 强制使用Repository模式
        if let container = repositoryContainer {
            await loadDataWithRepository(for: habit, container: container)
        } else {
            await provideFallbackExperience(for: habit)
        }
        
        await MainActor.run {
            self.isLoading = false
        }
    }
    
    /// 使用Repository模式加载数据
    private func loadDataWithRepository(for habit: EAHabit, container: EARepositoryContainer) async {
        do {
            // 使用正确的Repository获取完成记录
            let completions = try await container.completionRepository.fetchCompletions(for: habit.id)
            
            // 计算统计数据
            await calculateStatisticsWithCompletions(for: habit, completions: completions)
            
            // 生成日历数据
            await generateCalendarDaysWithCompletions(for: habit, completions: completions)
            
            // 生成AI观察和建议
            await generateAIObservation(for: habit)
            
        } catch {
            await MainActor.run {
                self.errorMessage = "加载数据失败：\(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - 🔑 新增：降级体验提供
    private func provideFallbackExperience(for habit: EAHabit) async {
        await MainActor.run {
            // 提供基本的静态体验，避免完全黑屏
            self.totalCompletions = habit.completions.count
            self.activeDays = max(1, habit.completions.count)
            self.longestStreak = habit.currentStreak
            self.completionRate = 0.8 // 提供一个合理的默认值
            
            // 提供静态AI观察
            self.aiObservation = "正在分析您的习惯数据，基本信息已可查看。"
            self.aiSuggestion = "建议稍后重新进入页面获取完整分析。"
            
            // 提供基本的日历数据
            self.calendarDays = generateFallbackCalendarDays()
        }
    }
    
    // MARK: - 🔑 新增：降级日历数据生成
    private func generateFallbackCalendarDays() -> [CalendarDay] {
        let calendar = Calendar.current
        let today = Date()
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -29, to: today) ?? today
        
        var days: [CalendarDay] = []
        
        // 生成基本的30天日历，不含完成状态
        for dayOffset in 0..<30 {
            if let date = calendar.date(byAdding: .day, value: dayOffset, to: thirtyDaysAgo) {
                let dayNumber = calendar.component(.day, from: date)
                let isToday = calendar.isDate(date, inSameDayAs: today)
                
                let calendarDay = CalendarDay(
                    date: date,
                    dayNumber: dayNumber,
                    isToday: isToday,
                    isCompleted: false, // 降级模式不显示完成状态
                    isMissed: false
                )
                
                days.append(calendarDay)
            }
        }
        
        return days
    }
    
    // MARK: - 数据加载
    private func loadCompletionData(for habit: EAHabit) async {
        // 获取习惯的完成记录 - 使用关系查询
        let completions = habit.completions
        self.totalCompletions = completions.count
    }
    
    // MARK: - 生成日历数据（使用完成记录数组）
    private func generateCalendarDaysWithCompletions(for habit: EAHabit, completions: [EACompletion]) async {
        let calendar = Calendar.current
        let today = Date()
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -29, to: today) ?? today
        
        var days: [CalendarDay] = []
        
        // 获取完成记录日期集合
        let completionDates = completions.map { $0.date }
        
        // 生成30天的日历数据
        for dayOffset in 0..<30 {
            if let date = calendar.date(byAdding: .day, value: dayOffset, to: thirtyDaysAgo) {
                let dayNumber = calendar.component(.day, from: date)
                let isToday = calendar.isDate(date, inSameDayAs: today)
                let isCompleted = completionDates.contains { calendar.isDate($0, inSameDayAs: date) }
                let isMissed = !isCompleted && date < today && habit.isActive
                
                let calendarDay = CalendarDay(
                    date: date,
                    dayNumber: dayNumber,
                    isToday: isToday,
                    isCompleted: isCompleted,
                    isMissed: isMissed
                )
                
                days.append(calendarDay)
            }
        }
        
        await MainActor.run {
        self.calendarDays = days
    }
    }
    
    // MARK: - 统计计算（使用完成记录数组）
    private func calculateStatisticsWithCompletions(for habit: EAHabit, completions: [EACompletion]) async {
        let calendar = Calendar.current
        let today = Date()
        let creationDate = habit.creationDate
        
        // 计算活跃天数（实际有完成记录的不重复天数）
        let completionDates = completions.map { $0.date }
        let uniqueCompletionDays = Set(completionDates.map { calendar.startOfDay(for: $0) })
        let activeDaysCount = uniqueCompletionDays.count
        
        // 计算最长连续天数
        let longestStreakCount = calculateLongestStreakWithDates(completionDates)
        
        // 计算完成率（基于从创建到现在的总天数）
        let daysSinceCreation = calendar.dateComponents([.day], from: creationDate, to: today).day ?? 0
        let expectedDays = max(1, daysSinceCreation) // 避免除零
        let rate = Double(activeDaysCount) / Double(expectedDays)
        
        await MainActor.run {
        self.activeDays = activeDaysCount
        self.longestStreak = longestStreakCount
        self.completionRate = min(1.0, rate) // 确保不超过100%
        }
    }
    
    private func calculateLongestStreakWithDates(_ completionDates: [Date]) -> Int {
        guard !completionDates.isEmpty else { return 0 }
        
        let calendar = Calendar.current
        let sortedDates = completionDates.sorted()
        
        var maxStreak = 1
        var currentStreak = 1
        
        for i in 1..<sortedDates.count {
            let previousDate = sortedDates[i-1]
            let currentDate = sortedDates[i]
            
            if calendar.dateComponents([.day], from: previousDate, to: currentDate).day == 1 {
                currentStreak += 1
                maxStreak = max(maxStreak, currentStreak)
            } else {
                currentStreak = 1
            }
        }
        
        return maxStreak
    }
    
    // MARK: - AI观察笔记生成
    private func generateAIObservation(for habit: EAHabit) async {
        // 基于统计数据生成AI观察笔记
        let observation = generateObservationText()
        let suggestion = generateSuggestionText()
        
        self.aiObservation = observation
        self.aiSuggestion = suggestion
    }
    
    private func generateObservationText() -> String {
        let rate = completionRate
        let streak = longestStreak
        
        if rate >= 0.8 {
            return "你在这个习惯上表现得非常出色！\(Int(rate * 100))%的完成率显示了你强大的自律能力。最长连续\(streak)天的记录更是令人印象深刻。"
        } else if rate >= 0.6 {
            return "你在培育这个习惯方面做得不错，\(Int(rate * 100))%的完成率是一个很好的开始。虽然偶有波动，但整体趋势是积极的。"
        } else if rate >= 0.4 {
            return "看起来你在这个习惯上遇到了一些挑战。\(Int(rate * 100))%的完成率表明还有改进的空间，但不要气馁，每一次尝试都是进步。"
        } else {
            return "这个习惯对你来说似乎比较困难，但这很正常。建立新习惯需要时间和耐心，重要的是不要放弃。"
        }
    }
    
    private func generateSuggestionText() -> String {
        let rate = completionRate
        
        // 分析最近7天的完成情况
        let recentDays = calendarDays.suffix(7)
        let recentCompletions = recentDays.filter { $0.isCompleted }.count
        let recentRate = Double(recentCompletions) / 7.0
        
        if rate < 0.5 {
            return "建议将习惯分解成更小的步骤，比如从每天5分钟开始。小步骤更容易坚持，成功的体验会激励你继续前进。"
        } else if recentRate < rate - 0.2 {
            return "最近一周的完成率有所下降，可能是遇到了新的挑战。试着回想一下最初的动机，或者调整一下执行时间。"
        } else if rate >= 0.8 {
            return "你已经很好地掌握了这个习惯！可以考虑适当增加难度，或者开始培育一个新的相关习惯。"
        } else {
            return "保持现在的节奏很好！如果想提高完成率，可以尝试设置更明确的提醒时间，或者找一个习惯伙伴互相督促。"
        }
    }
    
    // MARK: - 删除习惯操作
    /// 删除习惯
    func deleteHabit(_ habit: EAHabit) async -> Bool {
        guard let container = repositoryContainer else {
            await MainActor.run {
                self.errorMessage = "无法执行删除操作：Repository未初始化"
            }
            return false
        }
        
        do {
            // 通过Repository删除习惯
            try await container.habitRepository.deleteHabit(habit)
            
            // ✅ 删除成功后发送通知，通知其他页面更新
            await MainActor.run {
                NotificationCenter.default.post(
                    name: NSNotification.Name("EAHabitDeleted"),
                    object: habit.id
                )
                
                // 发送数据变化通知
                NotificationCenter.default.post(
                    name: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
                    object: habit.id
                )
            }
            
            return true
        } catch {
            await MainActor.run {
                self.errorMessage = "删除失败：\(error.localizedDescription)"
            }
            return false
        }
    }
}

// MARK: - 日历日期数据模型
struct CalendarDay {
    let date: Date
    let dayNumber: Int
    let isToday: Bool
    let isCompleted: Bool
    let isMissed: Bool
} 