import SwiftUI
import SwiftData

// MARK: - 计划提醒设置页面
struct EAHabitReminderSettingsView: View {
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss
    
    let habit: EAHabit
    
    @State private var reminderTimes: [Date] = []
    @State private var isReminderEnabled: Bool = true
    @State private var isPushNotificationEnabled: Bool = true
    @State private var currentTotalReminders: Int = 0
    @State private var isLoading: Bool = false
    @State private var showingTimePicker = false
    @State private var newReminderTime = Date()
    @State private var showingLimitAlert = false
    
    @EnvironmentObject var sessionManager: EASessionManager
    private let maxTotalReminders: Int = 40
    
    // 使用计算属性获取notificationService
    private var notificationService: EANotificationService {
        EANotificationService(sessionManager: sessionManager)
    }
    
    var body: some View {
        ZStack {
            // 使用与其他页面一致的背景设计
            EABackgroundView(style: .authentication, showParticles: false)
                .ignoresSafeArea(.all)
            
            VStack(spacing: 0) {
                // 自定义导航栏
                customNavigationBar
                
                // 主要内容区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 习惯信息卡片
                        habitInfoCard
                            .padding(.top, 16)
                        
                        // 全局提醒统计卡片
                        globalReminderStatsCard
                        
                        // 提醒设置主区域
                        reminderSettingsCard
                        
                        // 底部间距
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 16)
                }
            }
            
            // 加载指示器
            if isLoading {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: Color.hexColor("40E0D0")))
                    .scaleEffect(1.2)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            loadCurrentSettings()
        }
        .sheet(isPresented: $showingTimePicker) {
            timePickerSheet
        }
        .alert("提醒数量已达上限", isPresented: $showingLimitAlert) {
            Button("我知道了", role: .cancel) { }
        } message: {
            Text("您已设置了\(maxTotalReminders)个提醒，这是系统允许的最大数量。请删除一些现有提醒后再添加新的。")
        }
    }
    
    // MARK: - 自定义导航栏
    private var customNavigationBar: some View {
        HStack {
            // 取消按钮
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            
            Spacer()
            
            // 标题
            Text("提醒设置")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            // 保存按钮
            Button(action: {
                Task {
                    await saveReminderSettings()
                }
            }) {
                Text("保存")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(isLoading ? Color.white.opacity(0.5) : Color.hexColor("40E0D0"))
            }
            .disabled(isLoading)
        }
        .padding(.horizontal, 16)
        .padding(.top, 8)
        .padding(.bottom, 8)
    }
    
    // MARK: - 习惯信息卡片
    private var habitInfoCard: some View {
        HStack(spacing: 16) {
            // 🎨 使用统一的彩色图标组件，确保与选择器、今日页面、图鉴页面显示完全一致
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.hexColor("40E0D0").opacity(0.3),
                                Color.hexColor("40E0D0").opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 56, height: 56)
                
                EAColorfulIcon(habit.iconName, size: 24)
            }
            
            // 习惯信息
            VStack(alignment: .leading, spacing: 4) {
                Text(habit.name)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("设置智能提醒，让Aura在最佳时机提醒您")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 全局提醒统计卡片
    private var globalReminderStatsCard: some View {
        HStack {
            // 图标
            Image(systemName: "bell.badge.fill")
                .font(.system(size: 20))
                .foregroundColor(Color.hexColor("FF7F50"))
            
            VStack(alignment: .leading, spacing: 2) {
                Text("全局提醒统计")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Text("所有习惯的提醒总数")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
            }
            
            Spacer()
            
            // 统计数字
            Text("\(currentTotalReminders)/\(maxTotalReminders)")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(currentTotalReminders >= maxTotalReminders ? Color.red : Color.hexColor("40E0D0"))
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            currentTotalReminders >= maxTotalReminders ? 
                            Color.red.opacity(0.3) : Color.white.opacity(0.1), 
                            lineWidth: 1
                        )
                )
        )
    }
    
    // MARK: - 提醒设置主卡片
    private var reminderSettingsCard: some View {
        VStack(spacing: 20) {
            // 提醒开关
            reminderToggleSection
            
            // 提醒内容（开启时显示）
            if isReminderEnabled {
                VStack(spacing: 16) {
                    // 推送通知开关
                    pushNotificationToggle
                    
                    // 分割线
                    Rectangle()
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 1)
                    
                    // 时间管理区域
                    timeManagementSection
                }
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.3), value: isReminderEnabled)
    }
    
    // MARK: - 提醒开关区域
    private var reminderToggleSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("启用提醒")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Text(isReminderEnabled ? "已设置 \(reminderTimes.count) 个提醒" : "关闭所有提醒")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
            }
            
            Spacer()
            
            Toggle("", isOn: $isReminderEnabled)
                .toggleStyle(SwitchToggleStyle(tint: Color.hexColor("40E0D0")))
                .onChange(of: isReminderEnabled) { _, newValue in
                    if !newValue {
                        reminderTimes.removeAll()
                        updateTotalReminderCount()
                    }
                }
        }
    }
    
    // MARK: - 推送通知开关
    private var pushNotificationToggle: some View {
        HStack {
            HStack(spacing: 12) {
                Image(systemName: "app.badge")
                    .font(.system(size: 16))
                    .foregroundColor(Color.hexColor("FF7F50"))
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("系统推送")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text("即使应用未打开也能收到提醒")
                        .font(.system(size: 11, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.6))
                }
            }
            
            Spacer()
            
            Toggle("", isOn: $isPushNotificationEnabled)
                .toggleStyle(SwitchToggleStyle(tint: Color.hexColor("FF7F50")))
        }
    }
    
    // MARK: - 时间管理区域
    private var timeManagementSection: some View {
        VStack(spacing: 16) {
            // 标题和添加按钮
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "clock.fill")
                        .font(.system(size: 14))
                        .foregroundColor(Color.hexColor("40E0D0"))
                    
                    Text("提醒时间")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // 添加按钮
                Button(action: addNewReminder) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 14))
                        Text("添加")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(canAddMoreReminders ? Color.hexColor("40E0D0") : Color.white.opacity(0.3))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(canAddMoreReminders ? Color.hexColor("40E0D0").opacity(0.5) : Color.white.opacity(0.2), lineWidth: 1)
                    )
                }
                .disabled(!canAddMoreReminders)
            }
            
            // 时间列表
            if reminderTimes.isEmpty {
                // 空状态
                VStack(spacing: 8) {
                    Image(systemName: "clock.badge.plus")
                        .font(.system(size: 24))
                        .foregroundColor(Color.white.opacity(0.3))
                    
                    Text("还没有设置提醒时间")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.5))
                }
                .frame(height: 60)
            } else {
                // 时间列表
                VStack(spacing: 8) {
                    ForEach(Array(reminderTimes.enumerated()), id: \.offset) { index, time in
                        reminderTimeRow(time: time, index: index)
                    }
                }
            }
        }
    }
    
    // MARK: - 单个提醒时间行
    private func reminderTimeRow(time: Date, index: Int) -> some View {
        HStack {
            // 时间显示
            Text(timeFormatter.string(from: time))
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            // 删除按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    reminderTimes.remove(at: index)
                    updateTotalReminderCount()
                }
            }) {
                Image(systemName: "minus.circle.fill")
                    .foregroundColor(.red.opacity(0.8))
                    .font(.system(size: 18))
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.2))
        )
    }
    
    // MARK: - 时间选择器弹窗
    private var timePickerSheet: some View {
        NavigationView {
            ZStack {
                // 使用与项目一致的背景设计
                EABackgroundView(style: .authentication, showParticles: false)
                    .ignoresSafeArea(.all)
                
                VStack(spacing: 0) {
                    // 标题区域
                    VStack(spacing: 8) {
                        Text("选择提醒时间")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Text("设置您希望收到提醒的时间")
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(Color.white.opacity(0.7))
                    }
                    .padding(.top, 30)
                    .padding(.bottom, 20)
                    
                    // 时间选择器容器
                    DatePicker(
                        "选择时间",
                        selection: $newReminderTime,
                        displayedComponents: .hourAndMinute
                    )
                    .datePickerStyle(WheelDatePickerStyle())
                    .labelsHidden()
                    .colorScheme(.dark) // 确保使用深色主题
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
                    .padding(.horizontal, 20)
                    
                    Spacer()
                    
                    // 底部按钮区域
                    HStack(spacing: 16) {
                        // 取消按钮
                        Button(action: {
                            showingTimePicker = false
                        }) {
                            Text("取消")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.white.opacity(0.8))
                                .frame(maxWidth: .infinity)
                                .frame(height: 48)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.white.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                        )
                                )
                        }
                        
                        // 确定按钮
                        Button(action: {
                            addReminderTime()
                            showingTimePicker = false
                        }) {
                            Text("确定")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 48)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(
                                            LinearGradient(
                                                colors: [
                                                    Color.hexColor("40E0D0"),
                                                    Color.hexColor("40E0D0").opacity(0.8)
                                                ],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                }
            }
            .navigationBarHidden(true)
        }
        .presentationDetents([.height(350)])
        .presentationDragIndicator(.visible)
    }
    
    // MARK: - 计算属性
    private var canAddMoreReminders: Bool {
        currentTotalReminders < maxTotalReminders
    }
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }
    
    // MARK: - 方法
    private func addNewReminder() {
        guard canAddMoreReminders else { return }
        
        // 设置默认时间
        let calendar = Calendar.current
        if let lastTime = reminderTimes.last {
            newReminderTime = calendar.date(byAdding: .hour, value: 1, to: lastTime) ?? Date()
        } else {
            newReminderTime = Date()
        }
        
        showingTimePicker = true
    }
    
    private func addReminderTime() {
        // 🔑 修复：检查40个提醒限制
        if currentTotalReminders >= maxTotalReminders {
            showingLimitAlert = true
            return
        }
        
        // 检查时间是否已存在
        let timeString = timeFormatter.string(from: newReminderTime)
        let existingTimeStrings = reminderTimes.map { timeFormatter.string(from: $0) }
        
        if !existingTimeStrings.contains(timeString) {
            reminderTimes.append(newReminderTime)
            reminderTimes.sort()
            updateTotalReminderCount()
        }
    }
    
    private func loadCurrentSettings() {
        // 加载习惯的提醒时间
        reminderTimes = habit.reminderTimes.compactMap { timeString in
            DateFormatter.shortTime.date(from: timeString)
        }
        
        // 加载全局提醒统计
        Task {
            // ✅ 修复：使用不需要ModelContext参数的方法
            currentTotalReminders = await notificationService.getTotalReminderCount()
        }
    }
    
    private func updateTotalReminderCount() {
        Task {
            // 🔑 修复：使用新的查询方法，确保统计准确性
            currentTotalReminders = await notificationService.getTotalReminderCount()
        }
    }
    
    @MainActor
    private func saveReminderSettings() async {
        isLoading = true
        
        do {
            // ✅ 修复：安全访问Repository容器
            guard let repositoryContainer = repositoryContainer else {
                throw EARepositoryError.dataNotFound
            }
            
            // ✅ 修复：通过Repository获取并更新习惯
            guard let updatedHabit = try await repositoryContainer.habitRepository.fetchHabit(id: habit.id) else {
                throw EARepositoryError.habitNotFound
            }
            
            // 更新习惯的提醒设置
            let timeStrings = reminderTimes.map { timeFormatter.string(from: $0) }
            updatedHabit.reminderTimes = timeStrings
            updatedHabit.reminderEnabled = isPushNotificationEnabled
            
            // 保存更新
            try await repositoryContainer.habitRepository.saveHabit(updatedHabit)
            
            // 更新通知调度
            if isPushNotificationEnabled && !reminderTimes.isEmpty {
                await notificationService.scheduleMultipleHabitReminders(
                    for: updatedHabit,
                    times: reminderTimes
                )
            } else {
                // 移除该习惯的所有通知
                await notificationService.removeHabitReminders(habitId: updatedHabit.id)
                
                // 如果关闭了推送，启用AI应用内提醒
                if !reminderTimes.isEmpty {
                    await notificationService.scheduleAIInAppReminders(
                        for: updatedHabit,
                        times: reminderTimes
                    )
                }
            }
            
            // 发送数据变更通知
            NotificationCenter.default.post(
                name: NSNotification.Name("HabitDataChanged"),
                object: updatedHabit.id
            )
            
            isLoading = false
            
            // 直接关闭页面，不显示成功提示
            dismiss()
            
        } catch {
            isLoading = false
            // 错误处理：显示用户友好的错误信息
            // TODO: 实现错误提示UI组件
        }
    }
}

#Preview("计划提醒设置") {
    EAHabitReminderSettingsView(habit: PreviewData.sampleHabit)
        .modelContainer(PreviewData.container)
} 