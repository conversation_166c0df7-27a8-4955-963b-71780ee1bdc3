import SwiftUI
import SwiftData

/// AuraSpace主视图 - AI对话界面
/// 提供与AI教练Aura的深度对话体验，包含聊天记录、输入区域和快速回复
struct EAAuraSpaceView: View {
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    @StateObject private var viewModel: EAAuraSpaceViewModel
    
    init() {
        // ✅ 修复：使用临时SessionManager初始化，在onAppear中更新
        self._viewModel = StateObject(wrappedValue: EAAuraSpaceViewModel(
            sessionManager: EASessionManager()
        ))
    }
    
    @State private var messageText = ""
    @State private var isInputFocused = false
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                EABackgroundView(style: .auraSpace)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 顶部导航区域 - 增加更多顶部间距
                    headerView
                        .padding(.horizontal, EAAppConstants.Dimensions.standardPadding + 4)
                        .padding(.top, max(geometry.safeAreaInsets.top + EAAppConstants.Dimensions.standardPadding, 30))
                    
                    // 聊天内容区域 - 动态计算高度，确保为输入框和Tab栏预留空间
                    chatContentView
                        .frame(
                            maxWidth: .infinity,
                            maxHeight: calculateChatContentHeight(geometry: geometry)
                        )
                    
                    // 底部输入区域 - 固定位置，确保在Tab栏上方
                    inputAreaView
                        .padding(.horizontal, EAAppConstants.Dimensions.standardPadding + 4)
                        .padding(.bottom, calculateBottomPadding(geometry: geometry))
                        .background(
                            // 输入区域背景，确保与Tab栏有明确分界
                            Rectangle()
                                .fill(Color.black.opacity(EAAppConstants.AuraSpace.inputBackgroundOpacity))
                                .blur(radius: EAAppConstants.AuraSpace.inputBackgroundBlurRadius)
                                .ignoresSafeArea(.all, edges: .bottom)
                        )
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $viewModel.showingContentLibrary) {
            EAContentLibraryView()
        }
        .onAppear {
            // ✅ 修复：设置正确的sessionManager和Repository容器
            viewModel.updateSessionManager(sessionManager)
            if let repositoryContainer = repositoryContainer {
                viewModel.setRepositoryContainer(repositoryContainer)
            }
            Task {
                await viewModel.loadConversationHistory()
            }
        }
        .onTapGesture {
            // 点击空白区域收起键盘
            isTextFieldFocused = false
        }
    }
    
    // MARK: - 顶部导航区域
    
    private var headerView: some View {
        HStack {
            // AI头像和状态
            HStack(spacing: EAAppConstants.Dimensions.baseSpacing + 4) {
                EAStatefulAIAvatarView(
                    size: EAAppConstants.AuraSpace.avatarSizeMedium,
                    state: viewModel.aiState
                )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Aura")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Text(viewModel.aiStatusText)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color("TextSecondary"))
                }
            }
            
            Spacer()
            
            // 功能按钮
            HStack(spacing: EAAppConstants.Dimensions.standardPadding) {
                // 智慧宝库按钮
                Button(action: {
                    viewModel.navigateToWisdomTreasury()
                }) {
                    Image(systemName: "book.fill")
                        .font(.system(size: 20))
                        .foregroundColor(Color("PrimaryTurquoise"))
                }
                
                // 清除对话按钮
                Button(action: {
                    viewModel.clearConversation()
                }) {
                    Image(systemName: "trash")
                        .font(.system(size: 18))
                        .foregroundColor(Color("TextSecondary"))
                }
            }
        }
    }
    
    // MARK: - 聊天内容区域
    
    private var chatContentView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: EAAppConstants.AuraSpace.chatBubbleSpacing) {
                    // 欢迎消息（如果没有历史记录）
                    if viewModel.messages.isEmpty {
                        welcomeMessageView
                            .padding(.top, EAAppConstants.AuraSpace.welcomeMessageTopPadding)
                    }
                    
                    // 聊天消息列表
                    ForEach(viewModel.messages, id: \.id) { message in
                        EAChatBubble(
                            message: message.content,
                            isFromUser: message.isFromUser,
                            timestamp: message.timestamp
                        )
                        .id(message.id)
                    }
                    
                    // AI正在输入指示器
                    if viewModel.isAITyping {
                        aiTypingIndicator
                    }
                    
                    // 快速回复按钮组
                    if !viewModel.quickReplies.isEmpty && !viewModel.isAITyping {
                        quickRepliesView
                            .padding(.top, EAAppConstants.AuraSpace.quickReplyTopPadding)
                    }
                    
                    // 底部间距
                    Color.clear
                        .frame(height: EAAppConstants.AuraSpace.chatContentBottomSpacing)
                }
                .padding(.horizontal, EAAppConstants.Dimensions.standardPadding + 4)
            }
            .onChange(of: viewModel.messages.count) { _, _ in
                // 自动滚动到最新消息
                if let lastMessage = viewModel.messages.last {
                    withAnimation(.easeOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }
    
    // MARK: - 欢迎消息
    
    private var welcomeMessageView: some View {
        VStack(spacing: 20) {
            // AI头像
            EAAIAvatarView(size: EAAppConstants.AuraSpace.avatarSizeLarge, isActive: true)
            
            // 欢迎文字
            VStack(spacing: 8) {
                Text("你好，我是Aura")
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("你的专属AI计划教练")
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(Color("TextSecondary"))
                
                Text("我会陪伴你一起成长，帮助你建立更好的计划")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(Color("TextSecondary").opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, EAAppConstants.Dimensions.standardPadding + 4)
            }
            
            // 开始对话建议
            VStack(spacing: 12) {
                Text("你可以这样开始：")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color("TextSecondary"))
                
                EAQuickReplyButtonGroup(
                    replies: [
                        "我想创建新计划",
                        "查看我的进度",
                        "需要一些鼓励",
                        "随便聊聊"
                    ]
                ) { reply in
                    sendQuickReply(reply)
                }
            }
            .padding(.top, 10)
        }
    }
    
    // MARK: - AI正在输入指示器
    
    private var aiTypingIndicator: some View {
        HStack {
            HStack(spacing: 8) {
                EAAIAvatarView(size: EAAppConstants.AuraSpace.avatarSizeSmall, isActive: true)
                
                HStack(spacing: 4) {
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .fill(Color("TextSecondary"))
                            .frame(width: 6, height: 6)
                            .scaleEffect(viewModel.typingAnimationScale)
                            .animation(
                                .easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                                value: viewModel.typingAnimationScale
                            )
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white.opacity(0.1))
                )
            }
            
            Spacer()
        }
    }
    
    // MARK: - 快速回复区域
    
    private var quickRepliesView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("快速回复")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(Color("TextSecondary").opacity(0.7))
                .padding(.leading, 4)
            
            EAQuickReplyButtonGroup(replies: viewModel.quickReplies) { reply in
                sendQuickReply(reply)
            }
        }
    }
    
    // MARK: - 底部输入区域
    
    private var inputAreaView: some View {
        VStack(spacing: 12) {
            // 输入框
            HStack(spacing: 12) {
                // 文本输入框
                TextField("输入消息...", text: $messageText, axis: .vertical)
                    .font(.system(size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.white.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(
                                        isTextFieldFocused ? Color("PrimaryTurquoise") : Color.white.opacity(0.2),
                                        lineWidth: 1
                                    )
                            )
                    )
                    .focused($isTextFieldFocused)
                    .lineLimit(1...4)
                    .onSubmit {
                        sendMessage()
                    }
                
                // 发送按钮
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.system(size: 32))
                        .foregroundColor(
                            messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?
                            Color("TextSecondary") : Color("PrimaryTurquoise")
                        )
                }
                .disabled(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || viewModel.isAITyping)
            }
            
            // 错误提示
            if let error = viewModel.error {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    
                    Text(error.localizedDescription)
                        .font(.system(size: 14))
                        .foregroundColor(.red)
                    
                    Spacer()
                    
                    Button("重试") {
                        viewModel.clearError()
                    }
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color("PrimaryTurquoise"))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.red.opacity(0.1))
                )
            }
        }
    }
    
    // MARK: - 布局计算方法
    
    /// 计算聊天内容区域的最大高度
    /// 确保为输入框和Tab栏预留足够空间，适配不同设备尺寸
    private func calculateChatContentHeight(geometry: GeometryProxy) -> CGFloat {
        let screenHeight = geometry.size.height
        
        // 计算顶部占用空间：安全区域 + 导航栏高度
        let topPadding = max(geometry.safeAreaInsets.top + EAAppConstants.Dimensions.standardPadding, 30) + 70
        
        // 计算底部占用空间：输入区域 + Tab栏 + 安全区域 + 额外间距
        let inputAreaHeight = EAAppConstants.AuraSpace.inputMaxHeight
        let tabBarHeight = EAAppConstants.Dimensions.tabBarHeight
        let bottomSafeArea = geometry.safeAreaInsets.bottom
        let extraPadding = EAAppConstants.AuraSpace.bottomSafeAreaPadding
        
        // 计算可用高度
        let availableHeight = screenHeight - topPadding - inputAreaHeight - tabBarHeight - bottomSafeArea - extraPadding
        
        // 确保最小高度，避免在小屏设备上内容区域过小
        return max(availableHeight, 200)
    }
    
    /// 计算底部输入区域的padding
    /// 确保输入框始终在Tab栏上方，有明确的视觉分离
    private func calculateBottomPadding(geometry: GeometryProxy) -> CGFloat {
        // iOS标准Tab栏高度
        let tabBarHeight = EAAppConstants.Dimensions.tabBarHeight
        
        // 设备底部安全区域（如iPhone X系列的Home Indicator区域）
        let bottomSafeArea = geometry.safeAreaInsets.bottom
        
        // 与Tab栏的视觉间隙，确保输入框不会紧贴Tab栏
        let clearanceSpace = EAAppConstants.Dimensions.standardPadding
        
        return tabBarHeight + bottomSafeArea + clearanceSpace
    }
    
    // MARK: - 私有方法
    
    /// 发送消息
    /// 处理用户输入的消息发送，包括输入验证和状态管理
    private func sendMessage() {
        // 清理用户输入，移除首尾空白字符
        let text = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 验证输入：消息不能为空，且AI不能正在输入
        guard !text.isEmpty && !viewModel.isAITyping else { return }
        
        // 清空输入框并收起键盘
        messageText = ""
        isTextFieldFocused = false
        
        // 异步发送消息到AI
        Task {
            await viewModel.sendMessage(text)
        }
    }
    
    /// 发送快速回复
    /// 处理用户点击快速回复按钮的消息发送
    private func sendQuickReply(_ reply: String) {
        // 确保AI不在输入状态时才能发送快速回复
        guard !viewModel.isAITyping else { return }
        
        // 异步发送快速回复消息
        Task {
            await viewModel.sendMessage(reply)
        }
    }
}

// MARK: - 预览

#Preview {
    NavigationView {
        EAAuraSpaceView()
    }
    .modelContainer(PreviewData.container)
}