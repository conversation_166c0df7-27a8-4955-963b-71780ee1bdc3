import Foundation
import SwiftData
import SwiftUI

/// AuraSpace主视图ViewModel
/// 管理AI对话的状态和业务逻辑
@MainActor
class EAAuraSpaceViewModel: ObservableObject {
    
    // MARK: - 数据模型
    
    /// 消息模型
    struct Message: Identifiable {
        let id = UUID()
        let content: String
        let isFromUser: Bool
        let timestamp: Date
        
        init(content: String, isFromUser: Bool, timestamp: Date = Date()) {
            self.content = content
            self.isFromUser = isFromUser
            self.timestamp = timestamp
        }
    }
    
    // MARK: - 发布属性
    
    @Published var messages: [Message] = []
    @Published var quickReplies: [String] = []
    @Published var isAITyping = false
    @Published var aiState: EAAIAvatarState = .idle
    @Published var error: Error?
    @Published var typingAnimationScale: CGFloat = 1.0
    @Published var showingContentLibrary = false
    @Published var currentUser: EAUser?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - 私有属性
    
    /// Repository容器（强制依赖注入）
    private var repositoryContainer: EARepositoryContainer?
    
    /// AI服务
    private let aiService: EAAIService
    
    /// 会话管理器
    private var sessionManager: EASessionManager
    
    private var contentService: EAContentService?
    private var userProfile: EAUser?
    private var typingAnimationTimer: Timer?
    
    // MARK: - 初始化
    
    /// ✅ 修复：添加依赖注入
    init(sessionManager: EASessionManager, aiService: EAAIService? = nil) {
        self.sessionManager = sessionManager
        self.aiService = aiService ?? EAAIService()
        setupInitialQuickReplies()
        startTypingAnimation()
    }
    
    // MARK: - 公共方法
    
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        // 初始化contentService
        self.contentService = EAContentService(repositoryContainer: container)
    }
    
    /// ✅ 修复：更新SessionManager（用于Environment注入后的更新）
    func updateSessionManager(_ newSessionManager: EASessionManager) {
        self.sessionManager = newSessionManager
    }
    
    /// 加载对话历史
    func loadConversationHistory() async {
        isLoading = true
        errorMessage = nil
        
        do {
            if let container = repositoryContainer {
                // ✅ 修复：使用依赖注入的sessionManager替代单例
                guard let currentUser = sessionManager.currentUser else {
                    await provideFallbackExperience()
                    return
                }
                self.currentUser = currentUser
                
                let conversations = try await container.aiMessageRepository.fetchRecentMessages(for: currentUser.id)
            
            // 转换为消息格式
            var loadedMessages: [Message] = []
                for conversation in conversations {
                loadedMessages.append(Message(
                    content: conversation.userMessage,
                        isFromUser: true
                ))
                loadedMessages.append(Message(
                    content: conversation.aiResponse,
                        isFromUser: false
                ))
            }
            
                await MainActor.run {
            self.messages = loadedMessages
                    self.isLoading = false
                }
            } else {
                await provideFallbackExperience()
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "加载对话历史失败：\(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }
    
    /// 发送消息
    func sendMessage(_ text: String) async {
        // ✅ 修复：强制使用Repository容器
        if let repositoryContainer = repositoryContainer {
            await sendMessageWithRepository(text, container: repositoryContainer)
        } else {
            errorMessage = "Repository容器未设置，无法发送消息"
        }
    }
    
    /// 使用Repository容器发送消息
    private func sendMessageWithRepository(_ text: String, container: EARepositoryContainer) async {
        guard let user = currentUser else { return }
        
        // 添加用户消息到UI
        let userMessage = Message(content: text, isFromUser: true)
        messages.append(userMessage)
        
        // 这里应该调用AI服务，暂时使用模拟响应
        let aiResponse = "这是一个模拟的AI回复：\(text)"
        
        // 添加AI回复到UI
        let aiMessage = Message(content: aiResponse, isFromUser: false)
        messages.append(aiMessage)
        
        // 通过Repository保存消息
        do {
            let _ = try await container.aiMessageRepository.createMessage(
                userID: user.id,
                userMessage: text,
                aiResponse: aiResponse,
                type: "conversation"
            )
        } catch {
            errorMessage = "发送消息失败: \(error.localizedDescription)"
        }
    }
    
    /// 清除对话
    func clearConversation() {
        messages.removeAll()
        quickReplies.removeAll()
        setupInitialQuickReplies()
        aiState = .idle
        clearError()
        
        // 清除AI服务的对话历史
        aiService.clearConversationHistory()
    }
    
    /// 导航到智慧宝库
    func navigateToWisdomTreasury() {
        showingContentLibrary = true
    }
    
    /// 清除错误
    func clearError() {
        error = nil
    }
    
    /// AI状态文本
    var aiStatusText: String {
        switch aiState {
        case .idle:
            return "在线"
        case .thinking:
            return "思考中..."
        case .speaking:
            return "回复中..."
        case .listening:
            return "聆听中..."
        }
    }
    
    // MARK: - 私有方法
    
    /// 构建上下文信息
    /// 为AI服务提供用户的习惯数据和偏好设置，用于生成个性化回复
    private func buildContext() -> [String: Any] {
        var context: [String: Any] = [:]
        
        // 如果用户已登录，添加个性化上下文信息
        if let userProfile = userProfile {
            context["habitCount"] = getActiveHabitCount()           // 活跃习惯数量
            context["completionRate"] = getRecentCompletionRate()   // 最近完成率
            context["isPro"] = userProfile.isPro                    // Pro会员状态
            
            // 从用户设置中获取教练风格偏好
            if let coachStyle = getUserCoachStyle() {
                context["coachStyle"] = coachStyle
            }
        }
        
        return context
    }
    
    /// 获取用户教练风格偏好
    /// 从用户设置中获取偏好的教练风格，用于AI个性化回复
    private func getUserCoachStyle() -> String? {
        guard let currentUser = currentUser else { return nil }
        
        // 直接通过关系访问，避免async调用复杂性
        return currentUser.settings?.preferredCoachStyle
    }
    
    /// 获取活跃习惯数量
    private func getActiveHabitCount() -> Int {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = sessionManager.currentUser else { return 0 }
        
        // 直接通过关系访问，避免async调用复杂性
        return currentUser.habits.filter { $0.isActive }.count
    }
    
    /// 获取最近完成率
    /// 计算用户在过去7天内的习惯完成率，用于AI个性化回复
    private func getRecentCompletionRate() -> Double {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = sessionManager.currentUser else { return 0.0 }
        
        let calendar = Calendar.current
        let sevenDaysAgo = calendar.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        
        // 直接通过关系访问，避免async调用复杂性
        let activeHabits = currentUser.habits.filter { $0.isActive }
        if activeHabits.isEmpty { return 0.0 }
        
        let recentCompletions = activeHabits.flatMap { habit in
            habit.completions.filter { $0.date >= sevenDaysAgo }
        }
        
        let expectedCompletions = activeHabits.count * 7
        let actualCompletions = recentCompletions.count
        
        return min(1.0, Double(actualCompletions) / Double(expectedCompletions))
    }
    
    /// 设置初始快速回复
    private func setupInitialQuickReplies() {
        quickReplies = EAAIService.getDefaultQuickReplies()
    }
    
    /// 更新快速回复
    private func updateQuickReplies(basedOnLastMessage message: String) {
        quickReplies = aiService.generateQuickReplies(for: message)
    }
    
    /// 开始输入动画
    private func startTypingAnimation() {
        // 先停止现有的Timer
        stopTypingAnimation()
        
        typingAnimationTimer = Timer.scheduledTimer(withTimeInterval: EAAppConstants.Animation.slowDuration + 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor [weak self] in
                guard let self = self else { return }
                withAnimation(.easeInOut(duration: EAAppConstants.Animation.slowDuration + 0.1)) {
                    self.typingAnimationScale = self.typingAnimationScale == 1.0 ? 1.4 : 1.0
                }
            }
        }
    }
    
    /// 停止输入动画
    private func stopTypingAnimation() {
        typingAnimationTimer?.invalidate()
        typingAnimationTimer = nil
    }
    
    /// 清理资源
    deinit {
        // 直接清理Timer资源，Timer.invalidate()是线程安全的
        typingAnimationTimer?.invalidate()
        typingAnimationTimer = nil
    }
    
    /// ✅ 修复：加载消息 - 强制Repository模式
    func loadMessages() {
        guard let repositoryContainer = repositoryContainer,
              let user = currentUser else { 
            Task { await provideFallbackExperience() }
            return 
        }
        
        isLoading = true
        
        Task {
            do {
                let userMessages = try await repositoryContainer.aiMessageRepository.fetchRecentMessages(for: user.id)
            
            // 转换为Message格式
            var convertedMessages: [Message] = []
            for aiMessage in userMessages.reversed() {
                convertedMessages.append(Message(
                    content: aiMessage.userMessage,
                    isFromUser: true,
                    timestamp: aiMessage.timestamp
                ))
                convertedMessages.append(Message(
                    content: aiMessage.aiResponse,
                    isFromUser: false,
                    timestamp: aiMessage.timestamp.addingTimeInterval(1)
                ))
            }
            
                await MainActor.run {
                    self.messages = convertedMessages
                    self.isLoading = false
                }
        } catch {
            await MainActor.run {
                    self.errorMessage = "加载消息失败: \(error.localizedDescription)"
                self.isLoading = false
                }
            }
        }
    }
    
    /// ✅ 修复：移除ModelContext依赖，已由Repository模式替代
    
    /// 提供降级体验
    private func provideFallbackExperience() async {
        await MainActor.run {
            // 静默处理数据库错误，不显示给用户
            self.messages = []
            self.currentUser = nil
            self.isLoading = false
            setupInitialQuickReplies()
        }
    }
}

// MARK: - 扩展：错误处理

extension EAAuraSpaceViewModel {
    /// 处理AI服务错误
    private func handleAIError(_ error: Error) {
        if let aiError = error as? EAAIService.EAAIError {
            switch aiError {
            case .rateLimitExceeded:
                self.error = NSError(
                    domain: "EAAuraSpace",
                    code: 1001,
                    userInfo: [NSLocalizedDescriptionKey: "请求过于频繁，请稍后再试"]
                )
            case .serviceUnavailable:
                self.error = NSError(
                    domain: "EAAuraSpace",
                    code: 1002,
                    userInfo: [NSLocalizedDescriptionKey: "AI服务暂时不可用，请稍后再试"]
                )
            default:
                self.error = error
            }
        } else {
            self.error = error
        }
    }
}

// MARK: - 扩展：统计和分析

extension EAAuraSpaceViewModel {
    /// 记录用户交互事件
    private func trackUserInteraction(_ eventType: String, data: [String: Any] = [:]) {
        // TODO: 集成分析服务
        // ✅ 修复：移除调试代码，符合.cursorrules规范
        // 将来可以集成正式的分析服务
    }
    
    /// 分析对话模式
    func analyzeConversationPatterns() -> [String: Any] {
        let totalMessages = messages.count
        let userMessages = messages.filter { $0.isFromUser }.count
        let aiMessages = totalMessages - userMessages
        
        return [
            "totalMessages": totalMessages,
            "userMessages": userMessages,
            "aiMessages": aiMessages,
            "averageResponseTime": calculateAverageResponseTime()
        ]
    }
    
    /// 计算平均响应时间
    private func calculateAverageResponseTime() -> TimeInterval {
        var responseTimes: [TimeInterval] = []
        
        for i in 0..<messages.count - 1 {
            let currentMessage = messages[i]
            let nextMessage = messages[i + 1]
            
            if currentMessage.isFromUser && !nextMessage.isFromUser {
                let responseTime = nextMessage.timestamp.timeIntervalSince(currentMessage.timestamp)
                responseTimes.append(responseTime)
            }
        }
        
        guard !responseTimes.isEmpty else { return 0 }
        return responseTimes.reduce(0, +) / Double(responseTimes.count)
    }
}