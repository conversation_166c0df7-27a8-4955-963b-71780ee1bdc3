import SwiftUI
import Foundation
import SwiftData
import Combine

// MARK: - 认证错误枚举

enum EAAuthError: LocalizedError {
    case invalidCredentials
    case userNotFound
    case phoneAlreadyExists
    case emailAlreadyExists
    case weakPassword
    case networkError
    case serverError
    case phoneNotVerified
    case emailNotVerified
    case invalidPhoneNumber
    case passwordMismatch
    case termsNotAgreed
    case unknown
    case dataContextUnavailable
    
    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "手机号或密码错误"
        case .userNotFound:
            return "用户不存在"
        case .phoneAlreadyExists:
            return "该手机号已被注册"
        case .emailAlreadyExists:
            return "该邮箱已被注册"
        case .weakPassword:
            return "密码强度不够，请设置更复杂的密码"
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError:
            return "服务器繁忙，请稍后重试"
        case .phoneNotVerified:
            return "手机号尚未验证，请查看短信"
        case .emailNotVerified:
            return "邮箱尚未验证，请查看邮件"
        case .invalidPhoneNumber:
            return "请输入有效的手机号"
        case .passwordMismatch:
            return "两次输入的密码不一致"
        case .termsNotAgreed:
            return "请阅读并同意用户协议和隐私政策"
        case .unknown:
            return "发生未知错误，请重试"
        case .dataContextUnavailable:
            return "数据上下文不可用"
        }
    }
}

// MARK: - 认证视图模型

@MainActor
class EAAuthViewModel: ObservableObject {
    // MARK: - Published Properties
    
    // 用户状态
    @Published var isAuthenticated = false
    @Published var currentUser: EAUser?
    
    // UI 状态
    @Published var isLoading = false
    @Published var errorMessage: String? = nil
    @Published var successMessage: String? = nil
    
    // 登录表单
    @Published var loginPhoneNumber: String = "" {
        didSet {
            if loginPhoneError != nil {
                loginPhoneError = nil
            }
        }
    }
    @Published var loginPassword: String = "" {
        didSet {
            if loginPasswordError != nil {
                loginPasswordError = nil
            }
        }
    }
    @Published var loginPhoneError: String? = nil
    @Published var loginPasswordError: String? = nil
    
    // 注册表单
    @Published var registrationPhoneNumber: String = "" {
        didSet {
            if registrationPhoneError != nil {
                registrationPhoneError = nil
            }
        }
    }
    @Published var registrationPassword: String = "" {
        didSet {
            if registrationPasswordError != nil {
                registrationPasswordError = nil
            }
        }
    }
    @Published var registrationConfirmPassword: String = "" {
        didSet {
            if registrationConfirmPasswordError != nil {
                registrationConfirmPasswordError = nil
            }
        }
    }
    @Published var registrationAgreedToTerms: Bool = false {
        didSet {
            if registrationTermsError != nil {
                registrationTermsError = nil
            }
        }
    }
    @Published var registrationPhoneError: String? = nil
    @Published var registrationPasswordError: String? = nil
    @Published var registrationConfirmPasswordError: String? = nil
    @Published var registrationTermsError: String? = nil
    
    // 忘记密码表单
    @Published var resetPhoneNumber: String = "" {
        didSet {
            if resetPhoneError != nil {
                resetPhoneError = nil
            }
        }
    }
    @Published var resetPhoneError: String? = nil
    @Published var isResetEmailSent = false
    
    // MARK: - Private Properties
    
    private let authService: EAAuthServiceProtocol
    private var sessionManager: EASessionManager?
    
    // ✅ 修复：强制Repository模式，移除ModelContext依赖
    private var repositoryContainer: EARepositoryContainer?
    
    // MARK: - Initialization
    
    /// 无参数初始化方法（用于@StateObject）
    init() {
        // 创建临时的SessionManager和AuthService，稍后通过setSessionManager更新
        let tempSessionManager = EASessionManager()
        self.authService = EAAuthService(sessionManager: tempSessionManager)
        self.sessionManager = nil
    }
    
    init(
        authService: EAAuthServiceProtocol? = nil,
        sessionManager: EASessionManager
    ) {
        // ✅ 修复：在MainActor上下文中创建EAAuthService
        if let authService = authService {
        self.authService = authService
        } else {
            self.authService = EAAuthService(sessionManager: sessionManager)
        }
        
        self.sessionManager = sessionManager
        
        self.isAuthenticated = sessionManager.isLoggedIn
        self.currentUser = sessionManager.currentUser
        
        setupSessionObserver()
    }
    
    // MARK: - 依赖注入方法
    
    /// 设置SessionManager（用于延迟依赖注入）
    func setSessionManager(_ manager: EASessionManager) {
        self.sessionManager = manager
        self.isAuthenticated = manager.isLoggedIn
        self.currentUser = manager.currentUser
        setupSessionObserver()
    }
    
    // MARK: - 会话观察
    
    private func setupSessionObserver() {
        guard let sessionManager = sessionManager else { return }
        
        sessionManager.$isLoggedIn
            .receive(on: DispatchQueue.main)
            .assign(to: &$isAuthenticated)
        
        sessionManager.$currentUser
            .receive(on: DispatchQueue.main)
            .assign(to: &$currentUser)
    }
    
    // MARK: - Public Methods
    
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        
        // 🔑 关键修复：同时设置AuthService的Repository容器
        Task { @MainActor in
            await authService.setRepositoryContainer(container)
        }
    }
    
    /// 登录
    func login() async {
        clearErrors()
        guard validateLoginInputs() else {
            return
        }
        
        isLoading = true
        
        do {
            let response = try await authService.login(
                phoneNumber: loginPhoneNumber,
                password: loginPassword
            )
            
            if response.success, let _ = response.token {
                isAuthenticated = true
                successMessage = "登录成功，欢迎回来！"
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.successMessage = nil
                }
                if let sessionManager = sessionManager, !sessionManager.isLoggedIn {
                    try await Task.sleep(nanoseconds: 50_000_000) // 0.05秒
                }
            } else {
                errorMessage = response.message
            }
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// 注册
    func register() async {
        clearErrors()
        guard validateRegistrationInputs() else { return }
        
        isLoading = true
        
        do {
            let response = try await authService.register(
                phoneNumber: registrationPhoneNumber,
                password: registrationPassword,
                agreesToTerms: registrationAgreedToTerms
            )
            
            if response.success, let _ = response.token {
                // 🔑 关键修复：EAAuthService已经自动处理了用户登录和会话保存
                // 无需重复调用SessionManager.login，只需等待状态更新
                
                // 等待SessionManager状态更新
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                
                // 验证登录状态
                if let sessionManager = sessionManager, sessionManager.isLoggedIn {
                    isAuthenticated = true
                    successMessage = "注册成功，欢迎使用Evolve！"
                    
                    // 清除成功消息
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        self.successMessage = nil
                    }
                } else {
                    // 如果状态未更新，说明可能有问题
                    errorMessage = "注册成功但登录状态异常，请尝试手动登录"
                }
            } else {
                errorMessage = response.message
            }
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// 社交登录
    func socialLogin(provider: EASocialButton.SocialProvider) async {
        clearErrors()
        isLoading = true
        
        do {
            let token = "mock_\(provider.rawValue.lowercased())_token"
            
            let response = try await authService.socialLogin(provider: provider.rawValue, token: token)
            
            if response.success, let _ = response.token {
                // 社交登录成功，创建用户数据
                let userData: [String: Any] = [
                    "id": UUID().uuidString,
                    "username": "\(provider.rawValue)用户",
                    "phoneNumber": "",
                    "isPro": false,
                    "preferredCoachStyle": "温柔鼓励型",
                    "creationDate": ISO8601DateFormatter().string(from: Date())
                ]
                
                await createUserProfile(from: userData)
                
                // 使用SessionManager的login方法
                if let sessionManager = sessionManager {
                    try await sessionManager.login(username: "\(provider.rawValue)用户")
                }
                
                isAuthenticated = true
                successMessage = response.message
                // 第三方登录成功
            } else {
                errorMessage = response.message
                // 第三方登录失败，错误信息已设置
            }
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    /// 忘记密码
    func sendPasswordReset() async -> Bool {
        clearErrors()
        
        if resetPhoneNumber.isEmpty {
            resetPhoneError = "请输入手机号"
            return false
        } else if !EATextField.phoneValidator(resetPhoneNumber) {
            resetPhoneError = "请输入有效的手机号"
            return false
        }
        
        isLoading = true
        
        do {
            let response = try await authService.sendPasswordResetCode(phoneNumber: resetPhoneNumber)
            
            if response.success && response.verificationCodeSent {
                successMessage = response.message
                isResetEmailSent = true
                isLoading = false
                return true
            } else {
                resetPhoneError = response.message
                isLoading = false
                return false
            }
            
        } catch {
            handleError(error)
            isLoading = false
            return false
        }
    }
    
    /// 登出
    func logout() async {
        isLoading = true
        
        do {
            let success = try await authService.logout()
            
            if success {
                if let sessionManager = sessionManager {
                    sessionManager.clearSession()
                }
                
                Task {
                    let networkService = EANetworkService()
                    networkService.clearAuthToken()
                }
                
                clearAllForms()
                
                successMessage = "已安全退出"
            } else {
                errorMessage = "退出失败，请重试"
            }
            
        } catch {
            if let sessionManager = sessionManager {
                sessionManager.clearSession()
            }
            Task {
                let networkService = EANetworkService()
                networkService.clearAuthToken()
            }
            clearAllForms()
            
            // 登出网络请求失败，但已清除本地会话
        }
        
        isLoading = false
    }
    
    // MARK: - 用户档案管理（已简化为新架构）
    
    // 旧的方法已移除，使用新的createUserProfile(from: [String: Any])方法
    
    // MARK: - 表单验证
    
    private func validateLoginInputs() -> Bool {
        var isValid = true
        
        if loginPhoneNumber.isEmpty {
            loginPhoneError = "请输入手机号"
            isValid = false
        } else if !EATextField.phoneValidator(loginPhoneNumber) {
            loginPhoneError = "请输入有效的手机号"
            isValid = false
        }
        
        if loginPassword.isEmpty {
            loginPasswordError = "请输入密码"
            isValid = false
        }
        
        return isValid
    }
    
    private func validateRegistrationInputs() -> Bool {
        var isValid = true
        
        if registrationPhoneNumber.isEmpty {
            registrationPhoneError = "请输入手机号"
            isValid = false
        } else if !EATextField.phoneValidator(registrationPhoneNumber) {
            registrationPhoneError = "请输入有效的手机号"
            isValid = false
        }
        
        if registrationPassword.isEmpty {
            registrationPasswordError = "请设置密码"
            isValid = false
        } else if !EATextField.passwordValidator(registrationPassword) {
            registrationPasswordError = "密码至少6位，需包含字母和数字"
            isValid = false
        }
        
        if registrationConfirmPassword.isEmpty {
            registrationConfirmPasswordError = "请确认密码"
            isValid = false
        } else if registrationPassword != registrationConfirmPassword {
            registrationConfirmPasswordError = "两次输入的密码不一致"
            isValid = false
        }
        
        if !registrationAgreedToTerms {
            registrationTermsError = "请阅读并同意用户协议和隐私政策"
            isValid = false
        }
        
        return isValid
    }
    
    // MARK: - 错误处理
    
    private func handleError(_ error: Error) {
        if let authError = error as? EAAuthError {
            errorMessage = authError.localizedDescription
        } else if let networkError = error as? EANetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = "发生未知错误：\(error.localizedDescription)"
        }
        
        // 记录认证错误
    }
    
    func clearErrors() {
        errorMessage = nil
        successMessage = nil
        loginPhoneError = nil
        loginPasswordError = nil
        registrationPhoneError = nil
        registrationPasswordError = nil
        registrationConfirmPasswordError = nil
        registrationTermsError = nil
        resetPhoneError = nil
    }
    
    // MARK: - 表单清理
    
    private func clearLoginForm() {
        loginPhoneNumber = ""
        loginPassword = ""
    }
    
    private func clearRegistrationForm() {
        registrationPhoneNumber = ""
        registrationPassword = ""
        registrationConfirmPassword = ""
        registrationAgreedToTerms = false
    }
    
    private func clearAllForms() {
        clearLoginForm()
        clearRegistrationForm()
        resetPhoneNumber = ""
        isResetEmailSent = false
    }
    
    // MARK: - Repository容器支持（新架构迁移准备）
    
    /// 检查是否支持新架构
    private var supportsNewArchitecture: Bool {
        return repositoryContainer != nil
    }
    
    // MARK: - 用户档案创建（新架构适配）
    
    /// 创建用户档案（适配新架构）
    private func createUserProfile(from userData: [String: Any]) async {
        // 新架构中，用户创建逻辑已经移到SessionManager.login方法中
        // 这里不需要直接操作ModelContext，而是通过Repository层处理
        // 用户档案创建逻辑已集成
    }
}

// MARK: - 扩展：社交登录提供商

extension EASocialButton.SocialProvider {
    var rawValue: String {
        switch self {
        case .apple: return "Apple"
        case .wechat: return "WeChat"
        case .phone: return "Phone"
        }
    }
} 