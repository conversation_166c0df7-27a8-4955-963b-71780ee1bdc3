import SwiftUI
import StoreKit

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EAProMembershipSheetType: Identifiable {
    case subscriptionConfirmation(ProPlan)
    
    var id: String {
        switch self {
        case .subscriptionConfirmation: return "subscriptionConfirmation"
        }
    }
}

/// Alert类型枚举 - 统一管理所有Alert状态
enum EAProMembershipAlertType: Identifiable {
    case message(title: String, content: String)
    
    var id: String {
        switch self {
        case .message: return "message"
        }
    }
}

/// Pro会员中心页面
/// 展示会员权益、订阅管理和价格方案
struct EAProMembershipView: View {
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.dismiss) private var dismiss
    @Environment(\.repositoryContainer) private var repositories
    
    // 支付服务
    @StateObject private var paymentService: EAPaymentService
    
    @State private var selectedPlan: ProPlan = .yearly
    @State private var isProcessingPurchase = false
    
    // ✅ 修复：统一状态管理
    @State private var activeSheet: EAProMembershipSheetType?
    @State private var activeAlert: EAProMembershipAlertType?
    
    // MARK: - Initialization
    init() {
        self._paymentService = StateObject(wrappedValue: EAPaymentService(sessionManager: EASessionManager()))
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 背景
            EABackgroundView()
                .ignoresSafeArea(.all)
            
            // 主要内容
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部间距
                    Spacer(minLength: 20)
                    
                    // 会员状态卡片
                    membershipStatusCard
                    
                    // 会员权益展示
                    membershipBenefitsSection
                    
                    // 价格方案（仅对非Pro用户显示）
                    if !paymentService.isProMember {
                        pricingPlansSection
                        
                        // 订阅按钮
                        subscribeButton
                    }
                    
                    // 管理订阅（仅对Pro用户显示）
                    if paymentService.isProMember {
                        manageSubscriptionSection
                    }
                    
                    // 常见问题
                    faqSection
                    
                    // 底部间距
                    Spacer(minLength: 40)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationTitle("Pro会员中心")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("返回")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
            }
        }
        .onAppear {
            setupPaymentService()
        }
        .alert(item: $activeAlert) { alertType in
            switch alertType {
            case .message(let title, let content):
                return Alert(
                    title: Text(title),
                    message: Text(content),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
        .sheet(item: $activeSheet) { sheet in
            switch sheet {
            case .subscriptionConfirmation(let plan):
                EASubscriptionConfirmationView(plan: plan) {
                    activeSheet = nil
                }
            }
        }
    }
    
    // MARK: - Membership Status Card
    private var membershipStatusCard: some View {
        VStack(spacing: 16) {
            // 会员状态
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(Color.hexColor("FFD700"))
                        
                        Text(paymentService.isProMember ? "Pro会员" : "普通用户")
                            .font(.system(size: 22, weight: .bold))
                            .foregroundColor(.white)
                    }
                    
                    if paymentService.isProMember {
                        if let productID = paymentService.currentSubscriptionProductID {
                            let subscriptionText = getSubscriptionText(for: productID)
                            Text(subscriptionText)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(productID == "evolve_pro_lifetime" ? Color.hexColor("FFD700") : Color.white.opacity(0.8))
                        }
                    } else {
                        Text("升级Pro解锁更多功能")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.8))
                    }
                }
                
                Spacer()
                
                // 会员徽章
                ZStack {
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("FFD700").opacity(0.8),
                                    Color.hexColor("FFA500").opacity(0.6)
                                ]),
                                center: .center,
                                startRadius: 0,
                                endRadius: 30
                            )
                        )
                        .frame(width: 60, height: 60)
                        .shadow(
                            color: Color.hexColor("FFD700").opacity(0.5),
                            radius: 10,
                            x: 0,
                            y: 0
                        )
                    
                    Image(systemName: paymentService.isProMember ? "crown.fill" : "crown")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("FFD700").opacity(0.15),
                            Color.hexColor("FFA500").opacity(0.1)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.hexColor("FFD700").opacity(0.4), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Membership Benefits Section
    private var membershipBenefitsSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("Pro会员权益")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 权益列表
            VStack(spacing: 12) {
                benefitItem(
                    icon: "brain.head.profile",
                    title: "无限AI对话",
                    description: "与Aura进行无限次深度对话",
                    isUnlocked: paymentService.isProMember
                )
                
                benefitItem(
                    icon: "sparkles",
                    title: "专属AI功能",
                    description: "个性化建议、深度分析等高级功能",
                    isUnlocked: paymentService.isProMember
                )
                
                benefitItem(
                    icon: "book.closed",
                    title: "专属内容库",
                    description: "访问Pro专属的动力包和心理学练习",
                    isUnlocked: paymentService.isProMember
                )
                
                benefitItem(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "高级数据分析",
                    description: "详细的习惯分析报告和趋势预测",
                    isUnlocked: paymentService.isProMember
                )
                
                benefitItem(
                    icon: "bell.badge",
                    title: "智能提醒",
                    description: "基于AI的个性化提醒和干预",
                    isUnlocked: paymentService.isProMember
                )
                
                benefitItem(
                    icon: "icloud.and.arrow.up",
                    title: "云端同步",
                    description: "数据在多设备间无缝同步",
                    isUnlocked: paymentService.isProMember
                )
            }
        }
    }
    
    // MARK: - Pricing Plans Section
    private var pricingPlansSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("选择订阅方案")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 加载状态
            if paymentService.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                    
                    Text("加载产品信息...")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                .frame(height: 200)
            } else if paymentService.products.isEmpty {
                // 无产品状态
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.6))
                    
                    Text("暂时无法加载产品信息")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Button("重新加载") {
                        Task {
                            await paymentService.loadProducts()
                        }
                    }
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.hexColor("40E0D0"))
                }
                .frame(height: 200)
            } else {
                // 价格方案
                VStack(spacing: 12) {
                    ForEach(paymentService.products, id: \.id) { product in
                        if let plan = ProPlan(rawValue: product.id) {
                            pricingPlanCard(
                                plan: plan,
                                product: product,
                                isSelected: selectedPlan == plan
                            )
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Subscribe Button
    private var subscribeButton: some View {
        Button(action: {
            handleSubscribe()
        }) {
            HStack(spacing: 12) {
                if isProcessingPurchase {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Text(isProcessingPurchase ? "处理中..." : "立即订阅")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("FFD700"),
                                Color.hexColor("FFA500")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .shadow(
                        color: Color.hexColor("FFD700").opacity(0.4),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isProcessingPurchase || paymentService.products.isEmpty)
    }
    
    // MARK: - Manage Subscription Section
    private var manageSubscriptionSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("订阅管理")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 管理选项
            VStack(spacing: 12) {
                managementItem(
                    icon: "arrow.clockwise",
                    title: "恢复购买",
                    subtitle: "恢复之前的购买记录",
                    action: {
                        handleRestorePurchases()
                    }
                )
                
                managementItem(
                    icon: "gear",
                    title: "管理订阅",
                    subtitle: "在App Store中管理订阅",
                    action: {
                        openAppStoreSubscriptions()
                    }
                )
                
                managementItem(
                    icon: "questionmark.circle",
                    title: "联系客服",
                    subtitle: "遇到问题？联系我们",
                    action: {
                        showAlert(title: "联系客服", message: "请发送邮件至 <EMAIL> 或在设置中提交反馈。")
                    }
                )
            }
        }
    }
    
    // MARK: - FAQ Section
    private var faqSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("常见问题")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // FAQ列表
            VStack(spacing: 12) {
                faqItem(
                    question: "如何取消订阅？",
                    answer: "您可以在iPhone的设置 > Apple ID > 订阅中取消订阅。"
                )
                
                faqItem(
                    question: "订阅后立即生效吗？",
                    answer: "是的，订阅成功后Pro功能将立即解锁。"
                )
                
                faqItem(
                    question: "可以退款吗？",
                    answer: "根据Apple的退款政策，您可以在App Store申请退款。"
                )
                
                faqItem(
                    question: "终身会员是什么？",
                    answer: "终身会员是一次性付费，永久享受Pro功能，无需续费。"
                )
            }
        }
    }
    
    // MARK: - Helper Views
    private func benefitItem(icon: String, title: String, description: String, isUnlocked: Bool) -> some View {
        HStack(spacing: 16) {
            // 图标
            ZStack {
                Circle()
                    .fill(isUnlocked ? Color.hexColor("32CD32").opacity(0.2) : Color.white.opacity(0.1))
                    .frame(width: 44, height: 44)
                
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(isUnlocked ? Color.hexColor("32CD32") : Color.white.opacity(0.6))
            }
            
            // 文字信息
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
            }
            
            Spacer()
            
            // 状态指示
            if isUnlocked {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("32CD32"))
            } else {
                Image(systemName: "lock.fill")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.5))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isUnlocked ? Color.hexColor("32CD32").opacity(0.3) : Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    private func pricingPlanCard(plan: ProPlan, product: Product, isSelected: Bool) -> some View {
        Button(action: {
            selectedPlan = plan
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // 标题和徽章
                HStack {
                    Text(product.displayName)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    if let badge = product.recommendationTag {
                        Text(badge)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.hexColor("FF7F50"))
                            )
                    }
                }
                
                // 价格
                HStack(alignment: .bottom, spacing: 4) {
                    Text(paymentService.getFormattedPrice(for: product))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(Color.hexColor("FFD700"))
                    
                    Text(paymentService.getSubscriptionPeriodDescription(for: product))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.8))
                    
                    Spacer()
                }
                
                // 描述和节省信息
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(getProductDescription(for: product))
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(Color.white.opacity(0.7))
                        
                        // 显示节省百分比
                        if let monthlyProduct = paymentService.products.first(where: { $0.id == "evolve_pro_monthly" }) {
                            if let savings = product.getSavingsPercentage(comparedTo: monthlyProduct), savings > 0 {
                            Text("相比月度节省 \(savings)%")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(Color.hexColor("32CD32"))
                            }
                        }
                    }
                    
                    Spacer()
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(isSelected ? 0.1 : 0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? Color.hexColor("FFD700") : Color.white.opacity(0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func managementItem(icon: String, title: String, subtitle: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标
                ZStack {
                    Circle()
                        .fill(Color.hexColor("40E0D0").opacity(0.2))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
                
                // 文字信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.5))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func faqItem(question: String, answer: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(question)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
            
            Text(answer)
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(Color.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "yyyy年MM月dd日"
            return displayFormatter.string(from: date)
        }
        return dateString
    }
    
    private func formatDateFromDate(_ date: Date) -> String {
        let displayFormatter = DateFormatter()
        displayFormatter.dateFormat = "yyyy年MM月dd日"
        return displayFormatter.string(from: date)
    }
    
    private func handleSubscribe() {
        guard let selectedProduct = paymentService.products.first(where: { $0.id == selectedPlan.rawValue }) else {
            showAlert(title: "错误", message: "未找到选中的产品，请重新选择。")
            return
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        isProcessingPurchase = true
        
        Task {
            let result = await paymentService.purchase(selectedProduct)
            
            await MainActor.run {
                isProcessingPurchase = false
                
                switch result {
                case .success(let paymentRecord):
                    // 购买成功
                    showAlert(title: "购买成功", message: "恭喜您成为Pro会员！所有高级功能已解锁。")
                    
                    // 更新用户状态
                    if let user = sessionManager.currentUser {
                        user.isPro = true
                        if paymentRecord.expirationDate == nil {
                            // 终身会员
                            user.proExpirationDate = nil
                        } else {
                            user.proExpirationDate = paymentRecord.expirationDate
                        }
                    }
                case .failure(let error):
                    // 购买失败
                    showAlert(title: "购买失败", message: error.localizedDescription)
                case .userCancelled:
                    // 用户取消购买，不显示提示
                    break
                case .pending:
                    // 购买待处理
                    showAlert(title: "购买处理中", message: "您的购买正在处理中，请稍后查看。")
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func getSubscriptionText(for productID: String) -> String {
        switch productID {
        case "evolve_pro_monthly":
            return "月度会员 - 每月自动续费"
        case "evolve_pro_quarterly":
            return "季度会员 - 每3个月自动续费"
        case "evolve_pro_yearly":
            return "年度会员 - 每年自动续费"
        case "evolve_pro_lifetime":
            return "终身会员 - 永久享受Pro功能"
        default:
            return "Pro会员"
        }
    }
    
    private func getProductDescription(for product: Product) -> String {
        switch product.id {
        case "evolve_pro_monthly":
            return "按月付费，随时可取消"
        case "evolve_pro_quarterly":
            return "按季度付费，更优惠的选择"
        case "evolve_pro_yearly":
            return "按年付费，最划算的订阅方案"
        case "evolve_pro_lifetime":
            return "一次性付费，永久享受所有Pro功能"
        default:
            return product.description
        }
    }
    
    private func setupPaymentService() {
        // 设置模型上下文（如果支付服务需要的话）
        Task {
            await paymentService.loadProducts()
        }
    }
    
    private func handleRestorePurchases() {
        Task {
            await paymentService.restorePurchases()
            
            await MainActor.run {
                if paymentService.isProMember {
                    showAlert(title: "恢复成功", message: "您的Pro会员已恢复！")
                    
                    // 更新用户状态
                    if let user = sessionManager.currentUser {
                        user.isPro = true
                        if let productID = paymentService.currentSubscriptionProductID,
                           productID == "evolve_pro_lifetime" {
                            user.proExpirationDate = nil // 终身会员无过期时间
                        }
                    }
                } else {
                    showAlert(title: "恢复失败", message: "未找到可恢复的购买记录。")
                }
            }
        }
    }
    
    private func openAppStoreSubscriptions() {
        if let url = URL(string: "https://apps.apple.com/account/subscriptions") {
            UIApplication.shared.open(url)
        } else {
            showAlert(title: "提示", message: "请前往iPhone设置 > Apple ID > 订阅中管理您的订阅。")
        }
    }
    
    private func showAlert(title: String, message: String) {
        activeAlert = .message(title: title, content: message)
    }
}

// MARK: - Supporting Types
enum ProPlan: String, CaseIterable {
    case monthly = "evolve_pro_monthly"
    case quarterly = "evolve_pro_quarterly"
    case yearly = "evolve_pro_yearly"
    case lifetime = "evolve_pro_lifetime"
    
    var displayName: String {
        switch self {
        case .monthly: return "月度会员"
        case .quarterly: return "季度会员"
        case .yearly: return "年度会员"
        case .lifetime: return "终身会员"
        }
    }
}

// MARK: - Product Extensions
extension Product {
    var recommendationTag: String? {
        switch self.id {
        case "evolve_pro_yearly":
            return "推荐"
        case "evolve_pro_lifetime":
            return "最划算"
        default:
            return nil
        }
    }
    
    func getSavingsPercentage(comparedTo monthlyProduct: Product) -> Int? {
        // 简化实现：返回预设的节省百分比
        switch self.id {
        case "evolve_pro_yearly":
            return 30 // 年度会员节省30%
        case "evolve_pro_lifetime":
            return 60 // 终身会员节省60%
        default:
            return nil
        }
    }
}

// MARK: - Preview
#Preview("Pro会员中心 - 普通用户") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        
        return NavigationView {
            EAProMembershipView()
                .environmentObject(sessionManager)
                .task {
                    #if DEBUG
                    await sessionManager.simulateLogin()
                    #endif
                }
        }
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}

#Preview("Pro会员中心 - Pro用户") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        
        return NavigationView {
            EAProMembershipView()
                .environmentObject(sessionManager)
                .task {
                    #if DEBUG
                    await sessionManager.simulateLogin()
                    sessionManager.currentUser?.isPro = true
                    sessionManager.currentUser?.proExpirationDate = Calendar.current.date(byAdding: .year, value: 1, to: Date())
                    #endif
                }
        }
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
} 