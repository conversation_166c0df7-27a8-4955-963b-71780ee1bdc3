import SwiftUI

/// 订阅确认页面
/// 展示订阅详情和确认购买流程
struct EASubscriptionConfirmationView: View {
    let plan: ProPlan
    let onDismiss: () -> Void
    
    @EnvironmentObject var sessionManager: EASessionManager
    @State private var isProcessing = false
    @State private var showSuccessAlert = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                EABackgroundView()
                    .ignoresSafeArea(.all)
                
                // 主要内容
                ScrollView {
                    VStack(spacing: 24) {
                        // 顶部间距
                        Spacer(minLength: 20)
                        
                        // 订阅方案卡片
                        subscriptionPlanCard
                        
                        // 权益列表
                        benefitsSection
                        
                        // 价格详情
                        pricingDetailsSection
                        
                        // 条款说明
                        termsSection
                        
                        // 确认按钮
                        confirmButton
                        
                        // 底部间距
                        Spacer(minLength: 40)
                    }
                    .padding(.horizontal, 20)
                }
            }
            .navigationTitle("确认订阅")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        onDismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .medium))
                            Text("返回")
                                .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(Color.hexColor("40E0D0"))
                    }
                }
            }
            .alert("订阅成功", isPresented: $showSuccessAlert) {
                Button("完成") {
                    // 关闭订阅确认页面
                    onDismiss()
                }
                .font(.system(size: 17, weight: .semibold))
                .foregroundColor(Color("DeepTeal"))
            } message: {
                Text("恭喜您成为Pro会员！所有高级功能已为您解锁。")
            }
            .alert("订阅失败", isPresented: $showErrorAlert) {
                Button("好的") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - Subscription Plan Card
    private var subscriptionPlanCard: some View {
        VStack(spacing: 16) {
            // 方案标题
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(plan.displayName)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("立即升级为Pro会员")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.8))
                }
                
                Spacer()
                
                // Pro徽章
                ZStack {
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("FFD700").opacity(0.8),
                                    Color.hexColor("FFA500").opacity(0.6)
                                ]),
                                center: .center,
                                startRadius: 0,
                                endRadius: 25
                            )
                        )
                        .frame(width: 50, height: 50)
                        .shadow(
                            color: Color.hexColor("FFD700").opacity(0.5),
                            radius: 8,
                            x: 0,
                            y: 0
                        )
                    
                    Image(systemName: "crown.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            
            // 价格信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(alignment: .bottom, spacing: 4) {
                        Text(planPrice)
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(Color.hexColor("FFD700"))
                        
                        Text(planPeriod)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.8))
                    }
                    
                    if let savings = planSavings {
                        Text(savings)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color.hexColor("32CD32"))
                    }
                }
                
                Spacer()
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("FFD700").opacity(0.15),
                            Color.hexColor("FFA500").opacity(0.1)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.hexColor("FFD700").opacity(0.4), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Benefits Section
    private var benefitsSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("您将获得")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 权益列表
            VStack(spacing: 12) {
                benefitItem(
                    icon: "brain.head.profile",
                    title: "无限AI对话",
                    description: "与Aura进行无限次深度对话和咨询"
                )
                
                benefitItem(
                    icon: "sparkles",
                    title: "专属AI功能",
                    description: "个性化建议、深度分析等高级AI功能"
                )
                
                benefitItem(
                    icon: "book.closed",
                    title: "专属内容库",
                    description: "访问Pro专属的动力包和心理学练习"
                )
                
                benefitItem(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "高级数据分析",
                    description: "详细的计划分析报告和趋势预测"
                )
                
                benefitItem(
                    icon: "bell.badge",
                    title: "智能提醒",
                    description: "基于AI的个性化提醒和干预"
                )
            }
        }
    }
    
    // MARK: - Pricing Details Section
    private var pricingDetailsSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("价格详情")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 价格明细
            VStack(spacing: 12) {
                HStack {
                    Text(plan.displayName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text(planPrice)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color.hexColor("FFD700"))
                }
                
                if plan != .monthly {
                    HStack {
                        Text("相比月度订阅")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.7))
                        
                        Spacer()
                        
                        Text(planSavings ?? "")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.hexColor("32CD32"))
                    }
                }
                
                // 分割线
                Rectangle()
                    .fill(Color.white.opacity(0.1))
                    .frame(height: 1)
                
                HStack {
                    Text("总计")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text(planPrice)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(Color.hexColor("FFD700"))
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Terms Section
    private var termsSection: some View {
        VStack(spacing: 12) {
            Text("订阅说明")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("• 订阅将自动续费，您可以随时在设置中取消")
                Text("• 取消订阅后，您仍可使用Pro功能直到当前订阅期结束")
                Text("• 订阅费用将从您的Apple ID账户扣除")
                Text("• 点击确认即表示您同意我们的服务条款和隐私政策")
            }
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(Color.white.opacity(0.7))
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Confirm Button
    private var confirmButton: some View {
        Button(action: {
            handleConfirmSubscription()
        }) {
            HStack(spacing: 12) {
                if isProcessing {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Text(isProcessing ? "处理中..." : "确认订阅")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("FFD700"),
                                Color.hexColor("FFA500")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .shadow(
                        color: Color.hexColor("FFD700").opacity(0.4),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isProcessing)
    }
    
    // MARK: - Helper Views
    private func benefitItem(icon: String, title: String, description: String) -> some View {
        HStack(spacing: 16) {
            // 图标
            ZStack {
                Circle()
                    .fill(Color.hexColor("32CD32").opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("32CD32"))
            }
            
            // 文字信息
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.7))
            }
            
            Spacer()
            
            // 勾选图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.hexColor("32CD32"))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.hexColor("32CD32").opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Computed Properties
    private var planPrice: String {
        switch plan {
        case .monthly: return "¥19"
        case .quarterly: return "¥49"
        case .yearly: return "¥168"
        case .lifetime: return "¥498"
        }
    }
    
    private var planPeriod: String {
        switch plan {
        case .monthly: return "/月"
        case .quarterly: return "/季"
        case .yearly: return "/年"
        case .lifetime: return ""
        }
    }
    
    private var planSavings: String? {
        switch plan {
        case .monthly: return nil
        case .quarterly: return "节省14%"
        case .yearly: return "节省26%"
        case .lifetime: return "节省73%"
        }
    }
    
    // MARK: - Actions
    private func handleConfirmSubscription() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        isProcessing = true
        
        // 模拟订阅处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isProcessing = false
            
            // 模拟成功概率（实际应用中这里会调用StoreKit）
            let success = Bool.random()
            
            if success {
                // 更新用户Pro状态
                updateUserProStatus()
                showSuccessAlert = true
            } else {
                errorMessage = "订阅失败，请检查网络连接或稍后重试。"
                showErrorAlert = true
            }
        }
    }
    
    private func updateUserProStatus() {
        // 更新会话中的用户状态
        if let currentUser = sessionManager.currentUser {
            currentUser.isPro = true
            
            // 设置过期时间
            let calendar = Calendar.current
            let expirationDate: Date
            
            switch plan {
            case .monthly:
                expirationDate = calendar.date(byAdding: .month, value: 1, to: Date()) ?? Date()
            case .quarterly:
                expirationDate = calendar.date(byAdding: .month, value: 3, to: Date()) ?? Date()
            case .yearly:
                expirationDate = calendar.date(byAdding: .year, value: 1, to: Date()) ?? Date()
            case .lifetime:
                expirationDate = calendar.date(byAdding: .year, value: 100, to: Date()) ?? Date() // 设置为100年后，表示永久
            }
            
            currentUser.proExpirationDate = expirationDate
            
            // 保存用户状态
            // sessionManager.updateUserData(currentUser) // 暂时注释掉，等待实现
        }
    }
}

// MARK: - Preview
#Preview("订阅确认 - 月度") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        return EASubscriptionConfirmationView(plan: .monthly) {
            // 关闭预览页面
        }
        .environmentObject(sessionManager)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}

#Preview("订阅确认 - 年度") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        return EASubscriptionConfirmationView(plan: .yearly) {
            // 关闭预览页面
        }
        .environmentObject(sessionManager)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
} 