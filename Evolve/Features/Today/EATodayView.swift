import SwiftUI
import SwiftData

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EATodaySheetType: Identifiable {
    case habitCreation
    case habitDetail(EAHabit)
    
    var id: String {
        switch self {
        case .habitCreation: return "habitCreation"
        case .habitDetail(let habit): return "habitDetail_\(habit.id)"
        }
    }
}

// MARK: - Today页面主视图
/// ✅ 修复：遵循MVVM架构，通过ViewModel处理业务逻辑
struct EATodayView: View {
    // MARK: - Environment & ViewModel
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    // ✅ 关键修复：使用@StateObject正确观察ViewModel的@Published属性变化
    @StateObject private var viewModel: EATodayViewModel
    
    // ✅ 关键修复：简化@Query设计，只查询完成记录用于今日过滤
    @Query(
        sort: \EACompletion.date,
        order: .reverse
    ) private var allCompletions: [EACompletion]
    
    // Sheet状态管理
    @State private var activeSheet: EATodaySheetType?
    @State private var selectedDate: Date = Date()
    
    // MARK: - Animation State
    @State private var refreshRotation: Double = 0.0
    
    // ✅ 关键修复：自定义初始化器，支持依赖注入
    init(sessionManager: EASessionManager? = nil) {
        // 创建ViewModel实例，支持依赖注入
        let vm = EATodayViewModel(sessionManager: sessionManager ?? EASessionManager())
        self._viewModel = StateObject(wrappedValue: vm)
    }
    
    // MARK: - Computed Properties
    /// ✅ 修复：直接从ViewModel获取今日习惯，自动响应@Published变化
    private var todayHabits: [EAHabit] {
        return viewModel.todayHabits
    }
    
    /// ✅ 修复：通过ViewModel获取今日完成记录
    private var todayCompletions: [EACompletion] {
        return viewModel.filterTodayCompletions(from: allCompletions, for: selectedDate)
    }
    
    /// 今日已完成的习惯数量
    private var completedHabitsCount: Int {
        return viewModel.calculateCompletedCount(habits: todayHabits, completions: todayCompletions)
    }
    
    /// 今日总习惯数量
    private var totalHabitsCount: Int {
        todayHabits.count
    }
    
    /// 能量进度
    private var energyProgress: Double {
        guard totalHabitsCount > 0 else { return 0.0 }
        return Double(completedHabitsCount) / Double(totalHabitsCount)
    }
    
    /// 当前日期字符串
    private var currentDateString: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日，EEEE"
        return formatter.string(from: selectedDate)
    }
    
    /// 问候语
    private var greetingText: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "早上好，叶同学！"
        case 12..<18:
            return "下午好，叶同学！"
        default:
            return "晚上好，叶同学！"
        }
    }
    

    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // ✅ 还原：使用与"我的"页面相同的背景效果（深邃蓝绿渐变 + 丰富光源 + 多彩粒子）
                EABackgroundView(style: .authentication, showParticles: true)
                    .ignoresSafeArea(.all)
                
                // 主内容区域 - ✅ 采用"我的"页面的遮挡方式，精确控制内容显示范围
                VStack(spacing: 0) {
                    // 顶部安全区域占位 - 完全透明但占据空间，实现遮挡效果
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: calculateSafeTopMaskHeight(geometry: geometry))
                        .allowsHitTesting(false) // 不接收触摸事件
                    
                    // 可滚动内容区域 - 严格限制在安全区域下方
                    ScrollView {
                        VStack(spacing: 0) {
                            // ✅ 还原：顶部区域（日期、问候语、智慧核心）
                            topSection
                                .padding(.top, 16) // 减少顶部间距，因为已经有占位区域
                            
                            // ✅ 还原：今日能量进度区域
                            energyProgressSection
                                .padding(.top, 20)
                            
                            // ✅ 还原：习惯列表区域
                            habitsListSection
                                .padding(.top, 28)
                            
                            // ✅ 还原：每日洞察区域
                            dailyInsightSection
                                .padding(.top, 24)
                            
                            // 底部安全区域
                            Spacer()
                                .frame(height: 100)
                        }
                    }
                    .frame(
                        maxWidth: .infinity,
                        maxHeight: calculateContentHeight(geometry: geometry)
                    )
                    .contentMargins(.top, 0, for: .scrollContent) // iOS 17+ 特性：确保内容不会超出顶部
                    .clipped() // 严格裁剪，确保内容不会超出边界
                    .scrollIndicators(.hidden)
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(item: $activeSheet) { sheet in
            switch sheet {
            case .habitCreation:
                EAHabitCreationView()
            case .habitDetail(let habit):
                EAHabitDetailView(habit: habit)
            }
        }
        .onAppear {
            // ✅ 关键修复：设置Repository容器
            if let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
            }
            loadTodayData()
        }
        .onChange(of: selectedDate) { _, _ in
            loadTodayData()
        }
        .onChange(of: sessionManager.currentUser?.id) { _, _ in
            // ✅ 关键修复：用户变化时重新加载数据
            loadTodayData()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EAHabitCreated"))) { _ in
            // ✅ 关键修复：监听习惯创建通知，确保实时更新
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                // 短暂延迟确保SwiftData关系完全生效
                loadTodayData()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged))) { _ in
            // ✅ 修复：监听数据变化通知
            loadTodayData()
        }
    }
    
    // MARK: - Setup Methods
    
    // MARK: - 计算内容区域最大高度（与"我的"页面保持一致）
    private func calculateContentHeight(geometry: GeometryProxy) -> CGFloat {
        let screenHeight = geometry.size.height
        let topMaskHeight = calculateSafeTopMaskHeight(geometry: geometry)
        let bottomSafeArea = geometry.safeAreaInsets.bottom
        let tabBarHeight: CGFloat = 49 // iOS标准Tab栏高度
        
        // 可用内容高度 = 屏幕高度 - 顶部遮挡高度 - 底部安全区域 - Tab栏高度
        let availableHeight = screenHeight - topMaskHeight - bottomSafeArea - tabBarHeight
        
        return max(availableHeight, 300) // 确保最小高度
    }
    
    // MARK: - 计算安全的顶部遮挡高度（iOS规范）- 与"我的"页面完全一致
    private func calculateSafeTopMaskHeight(geometry: GeometryProxy) -> CGFloat {
        let topSafeArea = geometry.safeAreaInsets.top
        
        // 🔑 标准iOS设备安全区域高度计算
        // iOS设备安全区域高度参考：
        // - iPhone 15 Pro/Pro Max (Dynamic Island): 59pt
        // - iPhone 14 Pro/Pro Max (Dynamic Island): 59pt  
        // - iPhone 13/14/15 (Notch): 47pt
        // - iPhone 12 mini/13 mini: 50pt
        // - iPhone SE (无刘海): 20pt (状态栏高度)
        
        // ✅ 精确iOS规范：在系统安全区域基础上增加适量间距，确保与灵动岛/刘海有足够视觉分离
        let calculatedHeight: CGFloat
        if topSafeArea > 20 {
            // 有刘海/灵动岛设备：系统安全区域 + 额外视觉间距
            let extraSpacing: CGFloat
            if topSafeArea >= 55 {
                // 灵动岛设备 (iPhone 14 Pro+)：增加10pt额外间距
                extraSpacing = 10
            } else {
                // 刘海设备 (iPhone X-13)：增加8pt额外间距
                extraSpacing = 8
            }
            calculatedHeight = topSafeArea + extraSpacing
        } else {
            // 无刘海设备：使用状态栏高度，无需额外间距
            calculatedHeight = 20
        }
        
        // 🎯 内容区域额外间距：在安全区域基础上再增加内容缓冲区
        let additionalContentSpacing: CGFloat = 12 // 额外的内容区域间距
        
        return calculatedHeight + additionalContentSpacing
    }
    
    // MARK: - ✅ 还原：顶部区域组件
    private var topSection: some View {
        VStack(spacing: 16) {
            // 日期和问候语 - ✅ 已通过占位区域实现安全显示，无需额外上边距
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(currentDateString)
                        .font(.system(size: 14, weight: .light))
                        .foregroundColor(.white.opacity(0.7))
                        .tracking(0.5)
                    
                    Text(greetingText)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                        .tracking(0.3)
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            
            // 智慧核心 - ✅ 优化：减少上下间距，更新文字提示
            VStack(spacing: 6) {
                EABreathingWisdomCore(size: 70)
                    .onTapGesture {
                        // 点击智慧核心的交互逻辑
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            // 可以添加点击反馈或导航到AI对话页面
                        }
                    }
                
                Text("点击与Aura聊天")
                    .font(.system(size: 12, weight: .light))
                    .foregroundColor(.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - ✅ 还原：能量进度区域
    private var energyProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("今日能量")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("\(completedHabitsCount)/\(totalHabitsCount)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            // ✅ 还原：能量进度条
            EAEnergyMeter(
                progress: energyProgress,
                showPercentage: true
            )
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - ✅ 还原：习惯列表区域
    private var habitsListSection: some View {
        VStack(spacing: 16) {
            // 标题栏
            HStack {
                Text("今日计划")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                // 刷新按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        refreshRotation += 360
                    }
                    // ✅ 修复：刷新时重新加载数据
                    loadTodayData()
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16))
                        .foregroundColor(Color("PrimaryTurquoise"))
                        .rotationEffect(.degrees(refreshRotation))
                }
                
                // 添加习惯按钮
                Button(action: {
                    activeSheet = .habitCreation
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 16))
                        .foregroundColor(Color("PrimaryTurquoise"))
                }
            }
            .padding(.horizontal, 20)
            
            // 习惯列表内容
            if todayHabits.isEmpty {
                emptyStateView
            } else {
                habitsList
            }
        }
    }
    
    // MARK: - ✅ 还原：每日洞察区域
    private var dailyInsightSection: some View {
        EADailyInsight(
            insight: "今日专注于内心的平静，让每一个习惯都成为成长的种子。",
            style: .card
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - ✅ 还原：空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "sparkles")
                .font(.system(size: 40))
                .foregroundColor(Color("PrimaryTurquoise").opacity(0.6))
            
            Text("还没有计划")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            Text("点击右上角 + 号创建你的第一个计划")
                .font(.system(size: 14))
                .foregroundColor(.white.opacity(0.6))
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 40)
        .padding(.horizontal, 20)
    }
    
    // MARK: - ✅ 还原：习惯列表
    private var habitsList: some View {
        LazyVStack(spacing: 12) {
            ForEach(todayHabits, id: \.id) { habit in
                EAHabitCard(
                    habit: habit,
                    isCompleted: todayCompletions.contains(where: { $0.habit?.id == habit.id }),
                    onToggleCompletion: {
                        toggleHabitCompletion(habit)
                    }
                )
                .onTapGesture {
                    activeSheet = .habitDetail(habit)
                }
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 辅助方法
    private func loadTodayData() {
        viewModel.loadTodayData()
    }
    
    private func toggleHabitCompletion(_ habit: EAHabit) {
        viewModel.toggleHabitCompletion(habit)
    }
}

// MARK: - Today页面优化粒子效果 (复用图鉴页面的粒子系统)
struct TodayOptimizedParticles: View {
    @State private var particles: [TodayParticle] = []
    @State private var startTime = Date()
    
    var body: some View {
        GeometryReader { geometry in
            TimelineView(.animation(minimumInterval: EAAppConstants.Today.ParticleEffect.particleAnimationInterval)) { timeline in
                Canvas { context, size in
                    let timeElapsed = timeline.date.timeIntervalSince(startTime)
                    
                    // 绘制优化的粒子效果
                    for particle in particles {
                        drawOptimizedParticle(context: context, particle: particle, time: timeElapsed, size: size)
                    }
                }
                .blendMode(.plusLighter)
            }
        }
        .onAppear {
            initializeOptimizedParticles()
        }
        .allowsHitTesting(false)
    }
    
    /// 初始化优化的粒子系统
    private func initializeOptimizedParticles() {
        particles = [
            TodayParticle(
                id: 0,
                size: 4,
                startPosition: CGPoint(x: 0.2, y: 0.15),
                animationDuration: 8.0,
                delay: 0.0
            ),
            TodayParticle(
                id: 1,
                size: EAAppConstants.Today.ParticleEffect.maxParticleSize,
                startPosition: CGPoint(x: 0.75, y: 0.35),
                animationDuration: 10.0,
                delay: 1.5
            ),
            TodayParticle(
                id: 2,
                size: 5,
                startPosition: CGPoint(x: 0.15, y: 0.70),
                animationDuration: 9.0,
                delay: 0.8
            ),
            TodayParticle(
                id: 3,
                size: EAAppConstants.Today.ParticleEffect.minParticleSize,
                startPosition: CGPoint(x: 0.85, y: 0.20),
                animationDuration: 7.5,
                delay: 2.0
            )
        ]
    }
    
    /// 绘制优化的粒子
    /// - Parameters:
    ///   - context: 图形上下文
    ///   - particle: 粒子数据
    ///   - time: 当前时间
    ///   - size: 画布尺寸
    private func drawOptimizedParticle(context: GraphicsContext, particle: TodayParticle, time: Double, size: CGSize) {
        let adjustedTime = time + particle.delay
        let progress = (adjustedTime / particle.animationDuration).truncatingRemainder(dividingBy: 1.0)
        
        // 浮动动画
        let baseY = particle.startPosition.y * size.height
        let floatY = baseY + sin(progress * 2 * .pi) * (-EAAppConstants.Today.ParticleEffect.floatAmplitude)
        
        let baseX = particle.startPosition.x * size.width
        let floatX = baseX + sin(progress * 2 * .pi + .pi/3) * EAAppConstants.Today.ParticleEffect.horizontalFloatAmplitude
        
        // 缩放和透明度
        let scale = 1.0 + sin(progress * 2 * .pi) * EAAppConstants.Today.ParticleEffect.scaleVariation
        let opacity = EAAppConstants.Today.ParticleEffect.baseOpacity + sin(progress * 2 * .pi) * (-EAAppConstants.Today.ParticleEffect.opacityVariation)
        
        let currentSize = CGFloat(particle.size) * scale
        let rect = CGRect(
            x: floatX - currentSize/2,
            y: floatY - currentSize/2,
            width: currentSize,
            height: currentSize
        )
        
        let path = Path(ellipseIn: rect)
        let color = Color.hexColor("40E0D0").opacity(opacity * EAAppConstants.Today.ParticleEffect.particleOpacity)
        context.fill(path, with: .color(color))
    }
}

// MARK: - Today页面粒子数据模型
struct TodayParticle {
    let id: Int
    let size: Int
    let startPosition: CGPoint  // 相对位置 (0-1)
    let animationDuration: Double
    let delay: Double
}

// MARK: - Preview
#Preview("Today页面") {
    EATodayView(sessionManager: EASessionManager())
        .modelContainer(PreviewData.container)
        .environmentObject(EASessionManager())
} 