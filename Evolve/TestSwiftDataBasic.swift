import SwiftUI
import SwiftData
import Foundation

/// SwiftData基础功能测试
/// 🔧 开发工具：用于验证数据模型定义和基础CRUD操作
/// 📋 说明：这是一个测试视图，不是应用入口文件，与AppEntry.swift不冲突
/// ✅ 用途：验证SwiftData关系配置、调试数据库问题、测试模型功能
/// 🚫 注意：这不是@main入口点，仅供开发调试使用
struct TestSwiftDataBasic: View {
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Query private var users: [EAUser]
    @Query private var habits: [EAHabit]
    @Query private var completions: [EACompletion]
    
    @State private var testResults: [String] = []
    @State private var isTestRunning = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // 当前数据状态
                    dataStatusSection
                    
                    // 测试按钮
                    testButtonsSection
                    
                    // 测试结果
                    testResultsSection
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("SwiftData测试")
        }
    }
    
    // MARK: - 数据状态显示
    private var dataStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("数据状态")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("用户: \(users.count)")
                    Text("习惯: \(habits.count)")
                    Text("完成记录: \(completions.count)")
                }
                .font(.body)
                .foregroundColor(.secondary)
                
                Spacer()
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 测试按钮
    private var testButtonsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试操作")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                Button("创建测试用户") {
                    Task {
                        await createTestUser()
                    }
                }
                .buttonStyle(.borderedProminent)
                .disabled(isTestRunning)
                
                Button("创建测试习惯") {
                    Task {
                        await createTestHabit()
                }
                }
                .buttonStyle(.borderedProminent)
                .disabled(isTestRunning || users.isEmpty)
                
                Button("创建完成记录") {
                    Task {
                        await createTestCompletion()
                }
                }
                .buttonStyle(.borderedProminent)
                .disabled(isTestRunning || habits.isEmpty)
                
                Button("清空所有数据") {
                    Task {
                        await clearAllData()
                    }
                }
                .buttonStyle(.bordered)
                .disabled(isTestRunning)
            }
        }
    }
    
    // MARK: - 测试结果
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试结果")
                .font(.headline)
                .foregroundColor(.primary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(testResults, id: \.self) { result in
                        Text(result)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                    }
            .frame(height: 200)
        .padding()
            .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
        }
    }
    
    // MARK: - 测试方法
    
    /// 创建测试用户
    private func createTestUser() async {
        guard let repositoryContainer = repositoryContainer else {
            addTestResult("❌ Repository容器未初始化")
            return
        }
        
        isTestRunning = true
        
        do {
            let username = "测试用户\(Date().timeIntervalSince1970)"
            let user = try await repositoryContainer.userRepository.createUser(
                username: username,
                email: "\(username)@test.com"
            )
            
            addTestResult("✅ 用户创建成功: \(user.username)")
        } catch {
            addTestResult("❌ 用户创建失败: \(error.localizedDescription)")
        }
        
        isTestRunning = false
    }
    
    /// 创建测试习惯
    private func createTestHabit() async {
        guard let repositoryContainer = repositoryContainer else {
            addTestResult("❌ Repository容器未初始化")
            return
        }
        
        guard let user = users.first else {
            addTestResult("❌ 没有可用用户")
            return
        }
        
        isTestRunning = true
        
        do {
            let habitName = "测试习惯\(Date().timeIntervalSince1970)"
            let habit = try await repositoryContainer.habitRepository.createHabit(
                name: habitName,
                iconName: "star.fill",
                targetFrequency: 1,
                frequencyType: "daily",
                category: "健康",
                difficulty: "简单",
                for: user.id
            )
            
            addTestResult("✅ 习惯创建成功: \(habit.name)")
        } catch {
            addTestResult("❌ 习惯创建失败: \(error.localizedDescription)")
        }
        
        isTestRunning = false
    }
    
    /// 创建测试完成记录
    private func createTestCompletion() async {
        guard let repositoryContainer = repositoryContainer else {
            addTestResult("❌ Repository容器未初始化")
            return
        }
        
        guard let habit = habits.first else {
            addTestResult("❌ 没有可用习惯")
            return
        }
        
        isTestRunning = true
        
        do {
            let completion = try await repositoryContainer.completionRepository.createCompletion(
                for: habit.id,
                note: "测试完成记录",
                energyLevel: 8
            )
            
            addTestResult("✅ 完成记录创建成功")
            
            // 验证关系
            if completion.habit?.id == habit.id {
                addTestResult("✅ 关系验证成功: 完成记录正确关联到习惯")
            } else {
                addTestResult("❌ 关系验证失败: 完成记录未正确关联")
            }
        } catch {
            addTestResult("❌ 完成记录创建失败: \(error.localizedDescription)")
        }
        
        isTestRunning = false
    }
    
    /// 清空所有数据
    private func clearAllData() async {
        guard let repositoryContainer = repositoryContainer else {
            addTestResult("❌ Repository容器未初始化")
            return
        }
        
        isTestRunning = true
        
        do {
            // 删除所有完成记录
            for completion in completions {
                try await repositoryContainer.completionRepository.deleteCompletion(completion)
            }
            
            // 删除所有习惯
            for habit in habits {
                try await repositoryContainer.habitRepository.deleteHabit(habit)
            }
            
            // 删除所有用户
            for user in users {
                try await repositoryContainer.userRepository.deleteUser(user)
            }
            
            addTestResult("✅ 所有数据已清空")
        } catch {
            addTestResult("❌ 数据清空失败: \(error.localizedDescription)")
        }
        
        isTestRunning = false
    }
    
    /// 添加测试结果
    private func addTestResult(_ result: String) {
        DispatchQueue.main.async {
            testResults.append("[\(DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium))] \(result)")
        }
    }
}

// MARK: - 预览

#Preview("SwiftData测试") {
    TestSwiftDataBasic()
        .modelContainer(PreviewData.container)
} 