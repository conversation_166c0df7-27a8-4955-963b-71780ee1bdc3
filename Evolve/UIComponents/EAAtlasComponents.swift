import SwiftUI
import SwiftData

// MARK: - 统计卡片组件
struct EAStatCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(Color.hexColor("33FFDD"))
                .accessibilityHidden(true)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .dynamicTypeSize(.large ... .accessibility2)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .dynamicTypeSize(.large ... .accessibility2)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 80)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(value)")
        .accessibilityAddTraits(.isSummaryElement)
    }
}

// MARK: - 习惯概览卡片
struct EAHabitOverviewCard: View {
    let totalHabits: Int
    let activeHabits: Int
    let averageCompletionRate: Double
    let onCreateHabit: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // 顶部标题和发布按钮
            HStack {
                Text("📊 计划概览")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                // ✨ 发布按钮 - 生态隐喻风格
                Button(action: onCreateHabit) {
                    HStack(spacing: 6) {
                        // 生态图标 - 新芽成长的隐喻
                        Image(systemName: "leaf.fill")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Text("发布")
                            .font(.system(size: 13, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        ZStack {
                            // 主背景渐变 - 生态绿到青色的渐变
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.hexColor("00CC99"),
                                    Color.hexColor("40E0D0")
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                            
                            // 微光效果层
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.white.opacity(0.3),
                                    Color.clear,
                                    Color.white.opacity(0.2)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        }
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    // 轻微阴影效果
                    .shadow(
                        color: Color.hexColor("40E0D0").opacity(0.4),
                        radius: 6,
                        x: 0,
                        y: 3
                    )
                }
                .accessibilityLabel("创建新计划")
                .accessibilityHint("点击创建新的计划")
            }
            
            // 统计卡片网格
            HStack(spacing: 12) {
                // 总计划数
                EAStatCard(
                    title: "总计划",
                    value: "\(totalHabits)",
                    icon: "list.bullet.circle.fill"
                )
                
                // 活跃计划
                EAStatCard(
                    title: "活跃计划",
                    value: "\(activeHabits)",
                    icon: "checkmark.circle.fill"
                )
                
                // 平均完成率
                EAStatCard(
                    title: "平均完成率",
                    value: "\(Int(averageCompletionRate * 100))%",
                    icon: "chart.line.uptrend.xyaxis.circle.fill"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

// MARK: - 空状态视图
struct EAAtlasEmptyStateView: View {
    let onCreateHabit: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // 空状态图标
            ZStack {
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .containerRelativeFrame(.horizontal) { width, _ in
                        min(width * 0.2, 80)
                    }
                    .aspectRatio(1, contentMode: .fit)
                
                Image(systemName: "leaf.circle")
                    .font(.system(size: 40))
                    .foregroundColor(.white.opacity(0.6))
                    .accessibilityHidden(true)
            }
            
            VStack(spacing: 8) {
                Text("还没有计划")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .dynamicTypeSize(.large ... .accessibility2)
                
                Text("创建你的第一个计划，开始成长之旅")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .dynamicTypeSize(.large ... .accessibility2)
            }
            
            // 创建习惯按钮
            Button(action: onCreateHabit) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text("创建计划")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: 200)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.hexColor("40E0D0"),
                                    Color.hexColor("33FFDD")
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
            }
            .accessibilityLabel("创建第一个计划")
            .accessibilityHint("点击开始创建您的第一个计划")
            .scaleEffect(1.0)
            .animation(.bouncy(duration: 0.4, extraBounce: 0.1), value: true)
        }
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white.opacity(0.05))
        )
        .accessibilityElement(children: .contain)
        .accessibilityLabel("空状态页面")
        .accessibilityHint("当前没有计划，可以创建第一个计划")
    }
}

// MARK: - 习惯卡片组件 (完全重构版本，基于原型图设计)
struct EAEcoHabitCard: View {
    let habit: EAHabit
    let onTap: () -> Void
    let onDeleteHabit: () -> Void
    
    // MARK: - Properties
    @State private var isPressed = false
    @State private var isTodayCompleted = false
    
    // MARK: - iOS标准弹窗状态管理
    @State private var showActionDialog = false
    @State private var showReminderSettings = false
    @State private var showEditSheet = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                // 顶部区域：图标、名称、频率、操作按钮
                topSection
                
                // 底部区域：状态和类别
                bottomSection
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(cardBackground)
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onTapGesture {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0.1) {
            isPressed = true
        } onPressingChanged: { pressing in
            isPressed = pressing
        }
        // iOS标准弹窗：操作菜单
        .confirmationDialog("", isPresented: $showActionDialog, titleVisibility: .hidden) {
            Button("编辑计划") {
                showEditSheet = true
            }
            
            Button("提醒设置") {
                showReminderSettings = true
            }
            
            Button("删除计划", role: .destructive) {
                onDeleteHabit()
            }
            
            Button("取消", role: .cancel) { }
        } message: {
            Text("选择要执行的操作")
        }
        // 编辑Sheet
        .sheet(isPresented: $showEditSheet) {
            EAHabitCreationView(editingHabit: habit)
        }
        // 提醒设置Sheet
        .sheet(isPresented: $showReminderSettings) {
            EAHabitReminderSettingsView(habit: habit)
        }
    }
    
    // MARK: - 顶部区域：图标、名称、频率、操作按钮
    private var topSection: some View {
        HStack(spacing: 12) {
            // 习惯图标 - 优化：减少图标大小
            habitIcon
            
            // 左侧：习惯名称和类别（垂直排列）
            VStack(alignment: .leading, spacing: 2) {
                Text(habit.name)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                categoryBadge
            }
            
            Spacer()
            
            // 右侧：频率信息（与习惯名称平行）
            HStack(spacing: 4) {
                Image(systemName: "repeat")
                    .font(.system(size: 11))
                    .foregroundColor(Color.white.opacity(0.6))
                
                Text(frequencyText)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.9))
                    .lineLimit(1)
            }
            
            // ✅ 操作按钮 - 优化：减少按钮大小
            Button(action: {
                showActionDialog = true
            }) {
                Image(systemName: "ellipsis")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 习惯图标（生态化设计）- 优化尺寸
    private var habitIcon: some View {
        ZStack {
            // 背景渐变圆圈 - 减少尺寸
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("33FFDD").opacity(0.3),
                            Color.hexColor("33FFDD").opacity(0.1)
                        ]),
                        center: .center,
                        startRadius: 0,
                        endRadius: 25
                    )
                )
                .frame(width: 48, height: 48) // 从56减少到48
            
            // 图标 - 减少尺寸
            EAColorfulIcon(habit.iconName, size: 20) // 从24减少到20
        }
    }
    
    // MARK: - 底部区域：状态和类别
    private var bottomSection: some View {
        HStack {
            // 今日完成状态
            todayStatusBadge
            
            Spacer()
            
            // 连续天数（如果有）
            if consecutiveDays > 0 {
                consecutiveDaysBadge
            }
        }
    }
    
    // MARK: - 今日完成状态标识
    private var todayStatusBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: todayCompleted ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 11))
                .foregroundColor(todayCompleted ? Color.hexColor("33FFDD") : Color.white.opacity(0.6))
            
            Text(todayCompleted ? "已完成" : "待完成")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(todayCompleted ? Color.hexColor("33FFDD") : Color.white.opacity(0.8))
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            Capsule()
                .fill(todayCompleted ? Color.hexColor("33FFDD").opacity(0.15) : Color.white.opacity(0.08))
                .overlay(
                    Capsule()
                        .stroke(todayCompleted ? Color.hexColor("33FFDD").opacity(0.3) : Color.white.opacity(0.2), lineWidth: 0.5)
                )
        )
    }
    
    // MARK: - 连续天数标识
    private var consecutiveDaysBadge: some View {
        HStack(spacing: 3) {
            Image(systemName: "flame.fill")
                .font(.system(size: 9))
                .foregroundColor(Color.hexColor("FF6B35"))
            
            Text("\(consecutiveDays)天")
                .font(.system(size: 11, weight: .semibold))
                .foregroundColor(Color.hexColor("FF6B35"))
        }
        .padding(.horizontal, 5)
        .padding(.vertical, 2)
        .background(
            Capsule()
                .fill(Color.hexColor("FF6B35").opacity(0.15))
                .overlay(
                    Capsule()
                        .stroke(Color.hexColor("FF6B35").opacity(0.3), lineWidth: 0.5)
                )
        )
    }
    
    // MARK: - 习惯类别标识（简化设计）
    private var categoryBadge: some View {
        HStack(spacing: 3) {
            Image(systemName: categoryIcon)
                .font(.system(size: 9))
                .foregroundColor(Color.white.opacity(0.5))
            
            Text(habit.category)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(Color.white.opacity(0.6))
        }
    }
    
    // MARK: - 计算属性：今日完成状态（🔑 修复：安全的关系数据访问）
    private var todayCompleted: Bool {
        // 🔑 安全访问关系数据，避免SwiftData延迟加载问题
        guard !habit.completions.isEmpty else { return false }
        
        let today = Date()
        let calendar = Calendar.current
        
        // 使用安全的方式访问completions关系
        return habit.completions.contains { completion in
            calendar.isDate(completion.date, inSameDayAs: today)
        }
    }
    
    // MARK: - 计算属性：连续完成天数
    private var consecutiveDays: Int {
        guard !habit.completions.isEmpty else { return 0 }
        
        let calendar = Calendar.current
        let today = Date()
        
        // 获取最近的完成记录，按日期排序
        let sortedCompletions = habit.completions
            .map { $0.date }
            .sorted(by: >)
        
        guard !sortedCompletions.isEmpty else { return 0 }
        
        var consecutiveCount = 0
        var checkDate = today
        
        // 从今天开始往前检查连续天数
        for _ in 0..<30 { // 最多检查30天
            if sortedCompletions.contains(where: { calendar.isDate($0, inSameDayAs: checkDate) }) {
                consecutiveCount += 1
                checkDate = calendar.date(byAdding: .day, value: -1, to: checkDate) ?? checkDate
            } else {
                break
            }
        }
        
        return consecutiveCount
    }
    
    // MARK: - 计算属性：类别图标
    private var categoryIcon: String {
        switch habit.category {
        case "健康":
            return "heart.fill"
        case "学习":
            return "book.fill"
        case "运动":
            return "figure.walk"
        case "工作":
            return "briefcase.fill"
        case "生活":
            return "house.fill"
        default:
            return "tag.fill"
        }
    }
    
    // MARK: - 卡片背景 (基于原型图主题系统)
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 24)
            .fill(habitTheme.backgroundColor)
    }
    
    // MARK: - 频率文本
    private var frequencyText: String {
        switch habit.frequencyType {
        case "weekly":
            let weekdayCount = habit.selectedWeekdays.count
            if weekdayCount == 0 {
                return "未设置"
            } else if weekdayCount == 7 {
                return "每天"
            } else if weekdayCount <= 3 {
                let weekdayNames = ["一", "二", "三", "四", "五", "六", "日"]
                let selectedDays = habit.selectedWeekdays.sorted().compactMap { weekday -> String? in
                    guard weekday >= 1 && weekday <= 7 else { return nil }
                    return weekdayNames[weekday - 1]
                }
                return "周\(selectedDays.joined(separator: "、"))"
            } else {
                return "每周\(weekdayCount)天"
            }
            
        case "daily":
            if habit.dailyTarget <= 1 {
                return "每日"
            } else {
                return "每日\(habit.dailyTarget)次"
            }
            
        case "monthly":
            // 🔑 关键修复：根据monthlyMode区分显示，与详情页保持一致
            if habit.monthlyMode == "dates" {
                // 按指定日期模式
                let selectedDates = habit.selectedMonthlyDates.sorted()
                if selectedDates.isEmpty {
                    return "未设置日期"
                } else if selectedDates.count <= 2 {
                    return "每月\(selectedDates.map { "\($0)日" }.joined(separator: "、"))"
                } else {
                    return "每月\(selectedDates.count)个指定日期"
                }
            } else {
                // 按次数目标模式（target）
                if habit.monthlyTarget <= 0 {
                    return "未设置目标"
                } else {
                    return "每月\(habit.monthlyTarget)次"
                }
            }
            
        default:
            return "自定义频率"
        }
    }
    
    // MARK: - 主题颜色系统 (基于原型图)
    private var habitTheme: EAHabitCardTheme {
        // 根据习惯类型返回不同主题
        switch habit.iconName {
        case "leaf.fill", "star.fill", "sparkles":
            return .mindfulness
        case "figure.strengthtraining.traditional", "heart.fill", "flame.fill":
            return .fitness
        case "book.fill", "lightbulb.fill", "brain.head.profile":
            return .learning
        case "bed.double.fill", "moon.fill", "zzz":
            return .rest
        case "drop.fill", "carrot.fill", "apple.fill":
            return .nutrition
        default:
            return .mindfulness
        }
    }
}

// MARK: - 主题颜色定义 (基于原型图，避免与EAHabitCard冲突)
struct EAHabitCardTheme {
    let backgroundColor: Color
    let iconBackgroundColor: Color
    let iconColor: Color
    
    static let mindfulness = EAHabitCardTheme(
        backgroundColor: Color.hexColor("#14B8A6").opacity(0.25),
        iconBackgroundColor: Color.hexColor("#F59E0B").opacity(0.15),
        iconColor: Color.hexColor("#F59E0B")
    )
    
    static let fitness = EAHabitCardTheme(
        backgroundColor: Color.hexColor("#3B82F6").opacity(0.25),
        iconBackgroundColor: Color.hexColor("#60A5FA").opacity(0.15),
        iconColor: Color.hexColor("#60A5FA")
    )
    
    static let learning = EAHabitCardTheme(
        backgroundColor: Color.hexColor("#F59E0B").opacity(0.25),
        iconBackgroundColor: Color.hexColor("#FDE047").opacity(0.15),
        iconColor: Color.hexColor("#FDE047")
    )
    
    static let rest = EAHabitCardTheme(
        backgroundColor: Color.hexColor("#8B5CF6").opacity(0.25),
        iconBackgroundColor: Color.hexColor("#A78BFA").opacity(0.15),
        iconColor: Color.hexColor("#A78BFA")
    )
    
    static let nutrition = EAHabitCardTheme(
        backgroundColor: Color.hexColor("#10B981").opacity(0.25),
        iconBackgroundColor: Color.hexColor("#34D399").opacity(0.15),
        iconColor: Color.hexColor("#34D399")
    )
}

// MARK: - 习惯数据错误卡片
struct HabitDataErrorCard: View {
    let habitName: String
    
    var body: some View {
        HStack(spacing: 16) {
            // 错误图标区域
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.red.opacity(0.2))
                    .frame(width: 52, height: 52)
                
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.red.opacity(0.8))
            }
            
            // 错误信息区域
            VStack(alignment: .leading, spacing: 6) {
                Text(habitName)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Text("数据加载异常，请重新进入页面")
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            Spacer()
            
            // 重试按钮
            Button(action: {
                // 触发页面刷新
                NotificationCenter.default.post(
                    name: NSNotification.Name("RefreshAtlasData"),
                    object: nil
                )
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(Color.white.opacity(0.1))
                    .clipShape(Circle())
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color.red.opacity(0.15))
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - 数据错误处理视图（用于Sheet显示）
struct HabitDataErrorView: View {
    let title: String
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text(title)
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("确定") {
                onDismiss()
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 30)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.blue)
            )
        }
        .padding(30)
    }
}

struct EAAtlasComponents_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 统计卡片
    EAStatCard(
        title: "总计划",
        value: "5",
        icon: "list.bullet.circle.fill"
    )
    .padding()
    .background(Color.black)
            .previewDisplayName("统计卡片")

            // 计划概览卡片
    EAHabitOverviewCard(
        totalHabits: 5,
        activeHabits: 3,
        averageCompletionRate: 0.75,
        onCreateHabit: {}
    )
    .padding()
    .background(Color.black)
            .previewDisplayName("计划概览卡片")

            // 空状态视图
    EAAtlasEmptyStateView {
        // 创建计划回调 - 实际使用时会由父视图处理
    }
    .padding()
    .background(Color.black)
            .previewDisplayName("空状态视图")

            // 生态计划卡片
    EAEcoHabitCard(habit: PreviewData.sampleHabit, onTap: {}, onDeleteHabit: {})
    .padding()
    .background(Color.black)
    .modelContainer(PreviewData.container)
            .previewDisplayName("生态计划卡片")
        }
    }
} 