import SwiftUI

/// 头像显示组件
/// 用于在各个页面显示用户头像，支持系统头像和自定义头像
struct EAAvatarView: View {
    let avatarData: EAAvatarData?
    let size: CGFloat
    let showShadow: Bool
    
    init(avatarData: EAAvatarData?, size: CGFloat = 70, showShadow: Bool = true) {
        self.avatarData = avatarData
        self.size = size
        self.showShadow = showShadow
    }
    
    var body: some View {
        ZStack {
            avatarBackground
            avatarContent
        }
    }
    
    // MARK: - Avatar Background
    private var avatarBackground: some View {
        Circle()
            .fill(avatarGradient)
            .frame(width: size, height: size)
            .shadow(
                color: showShadow ? avatarColor.opacity(0.4) : .clear,
                radius: showShadow ? 12 : 0,
                x: 0,
                y: 0
            )
    }
    
    // MARK: - Avatar Content
    @ViewBuilder
    private var avatarContent: some View {
        if let avatarData = avatarData {
            if avatarData.type == .custom, let imageData = avatarData.customImageData, let uiImage = UIImage(data: imageData) {
                // 自定义头像
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: size - 10, height: size - 10)
                    .clipShape(Circle())
            } else {
                // 系统头像
                Image(systemName: avatarData.type.rawValue)
                    .font(.system(size: size * 0.45, weight: .medium))
                    .foregroundColor(.white)
            }
        } else {
            // 默认头像
            Image(systemName: "person.fill")
                .font(.system(size: size * 0.45, weight: .medium))
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Computed Properties
    private var avatarColor: Color {
        return avatarData?.type.avatarColor ?? .accentColor
    }
    
    private var avatarGradient: RadialGradient {
        RadialGradient(
            gradient: Gradient(colors: [
                avatarColor.opacity(0.8),
                avatarColor.opacity(0.7),
                avatarColor.opacity(0.5)
            ]),
            center: .center,
            startRadius: 0,
            endRadius: size / 2
        )
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        // 系统头像
        EAAvatarView(avatarData: EAAvatarData(type: .systemStar))
        
        // 默认头像
        EAAvatarView(avatarData: nil)
        
        // 不同尺寸
        HStack(spacing: 16) {
            EAAvatarView(avatarData: EAAvatarData(type: .systemHeart), size: 40)
            EAAvatarView(avatarData: EAAvatarData(type: .systemLeaf), size: 60)
            EAAvatarView(avatarData: EAAvatarData(type: .systemSun), size: 80)
        }
    }
    .padding()
    .background(Color.black)
}