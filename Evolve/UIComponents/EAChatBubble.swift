import SwiftUI

/// AI对话聊天气泡组件
/// 支持用户消息和AI回复两种样式，包含动画效果和自适应布局
struct EAChatBubble: View {
    let message: String
    let isFromUser: Bool
    let timestamp: Date
    
    @State private var isVisible = false
    
    var body: some View {
        HStack {
            if isFromUser {
                Spacer(minLength: 60)
            }
            
            VStack(alignment: isFromUser ? .trailing : .leading, spacing: 4) {
                // 消息气泡
                Text(message)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(isFromUser ? .white : Color("TextSecondary"))
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(isFromUser ? 
                                  AnyShapeStyle(LinearGradient(
                                    colors: [Color("PrimaryTurquoise"), Color("PrimaryTurquoise").opacity(0.8)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                  )) :
                                  AnyShapeStyle(Color.white.opacity(0.1))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(
                                        isFromUser ? Color.clear : Color.white.opacity(0.2),
                                        lineWidth: 1
                                    )
                            )
                    )
                
                // 时间戳
                Text(formatTimestamp(timestamp))
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color("TextSecondary").opacity(0.6))
                    .padding(.horizontal, 4)
            }
            
            if !isFromUser {
                Spacer(minLength: 60)
            }
        }
        .opacity(isVisible ? 1 : 0)
        .offset(y: isVisible ? 0 : 20)
        .onAppear {
            withAnimation(.easeOut(duration: 0.3)) {
                isVisible = true
            }
        }
    }
    
    // 格式化时间戳
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else {
            formatter.dateFormat = "MM/dd"
        }
        
        return formatter.string(from: date)
    }
}

#Preview {
    ZStack {
        Color("BackgroundDeepGreen")
            .ignoresSafeArea()
        
        VStack(spacing: 16) {
            EAChatBubble(
                message: "你好，我想开始培养一个新的计划",
                isFromUser: true,
                timestamp: Date()
            )
            
            EAChatBubble(
                message: "很高兴见到你！我是Aura，你的专属AI计划教练。告诉我，你想培养什么样的计划呢？我会根据你的情况为你制定个性化的方案。",
                isFromUser: false,
                timestamp: Date().addingTimeInterval(-60)
            )
        }
        .padding()
    }
}