import SwiftUI

/// 社区评论单元组件
/// 复用EACommunityPostCard设计模式，提供紧凑的评论展示
/// 支持评论内容显示、用户信息、点赞交互和回复功能
struct EACommentCell: View {
    
    // MARK: - Properties
    
    /// 评论数据
    let comment: EACommunityComment
    
    /// 是否已点赞
    @Binding var isLiked: Bool
    
    /// 点赞数量
    let likeCount: Int
    
    /// 评论样式
    let style: EACommentCellStyle
    
    /// 点赞回调
    let onLike: () -> Void
    
    /// 回复回调
    let onReply: (() -> Void)?
    
    /// 用户头像点击回调
    let onUserTap: (() -> Void)?
    
    // MARK: - Animation State
    
    @State private var cellScale: CGFloat = EAAppConstants.Community.PostCard.normalScale
    @State private var isPressed = false
    
    // MARK: - Initialization
    
    init(
        comment: EACommunityComment,
        isLiked: Binding<Bool>,
        likeCount: Int = 0,
        style: EACommentCellStyle = .standard,
        onLike: @escaping () -> Void,
        onReply: (() -> Void)? = nil,
        onUserTap: (() -> Void)? = nil
    ) {
        self.comment = comment
        self._isLiked = isLiked
        self.likeCount = likeCount
        self.style = style
        self.onLike = onLike
        self.onReply = onReply
        self.onUserTap = onUserTap
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: cellSpacing) {
            // 用户信息栏
            userInfoHeader
            
            // 评论内容
            commentContent
            
            // 交互操作栏
            actionBar
        }
        .padding(EAAppConstants.Community.Comment.cellPadding)
        .background(cellBackground)
        .clipShape(RoundedRectangle(cornerRadius: cellCornerRadius))
        .shadow(
            color: Color.black.opacity(shadowOpacity),
            radius: shadowRadius,
            x: 0,
            y: shadowOffset
        )
        .scaleEffect(cellScale)
        .onLongPressGesture(
            minimumDuration: EAAppConstants.Community.Timing.longPressDuration, 
            maximumDistance: .infinity
        ) { isPressing in
            withAnimation(.easeInOut(duration: EAAppConstants.Community.Timing.cardAnimationDuration)) {
                isPressed = isPressing
                cellScale = isPressing ? EAAppConstants.Community.PostCard.pressedScale : EAAppConstants.Community.PostCard.normalScale
            }
        } perform: {}
    }
    
    // MARK: - User Info Header
    
    private var userInfoHeader: some View {
        HStack(spacing: EAAppConstants.Community.Comment.contentSpacing) {
            // 用户头像 - 复用EAAvatarView
            userAvatar
            
            // 用户信息
            userInfoText
            
            Spacer()
            
            // 评论时间
            commentTimeText
        }
    }
    
    private var userAvatar: some View {
        Button(action: {
            onUserTap?()
        }) {
            // 复用EAAvatarView - 遵循开发规范要求
            EAAvatarView(
                avatarData: nil, // 暂时使用默认头像
                size: EAAppConstants.Community.Comment.avatarSize,
                showShadow: false
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("查看用户资料")
        .accessibilityHint("点击查看 \(comment.getAuthorUsername()) 的个人资料")
    }
    
    private var userInfoText: some View {
        VStack(alignment: .leading, spacing: 1) {
            // 用户名
            Text(comment.getAuthorUsername())
                .font(.system(size: userNameFontSize, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            // 回复标识（如果是回复评论）
            if comment.isReply, let replyTo = comment.replyToUsername {
                HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
                    Image(systemName: "arrowshape.turn.up.left.fill")
                        .font(.system(size: EAAppConstants.Community.Typography.energyIconFontSize))
                        .foregroundColor(Color.hexColor("40E0D0"))
                    
                    Text("回复 @\(replyTo)")
                        .font(.system(size: replyMetaFontSize, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                        .lineLimit(1)
                }
            }
        }
    }
    
    private var commentTimeText: some View {
        Text(formatCommentTime(comment.creationDate))
            .font(.system(size: timeFontSize, weight: .medium))
            .foregroundColor(Color.white.opacity(EAAppConstants.Community.ColorOpacity.tertiaryWhite))
    }
    
    // MARK: - Comment Content
    
    @ViewBuilder
    private var commentContent: some View {
        Text(comment.content)
            .font(.system(size: contentFontSize, weight: .regular))
            .foregroundColor(.white)
            .lineSpacing(EAAppConstants.Community.Typography.lineSpacing * 0.75)
            .fixedSize(horizontal: false, vertical: true)
    }
    
    // MARK: - Action Bar
    
    private var actionBar: some View {
        HStack(spacing: EAAppConstants.Community.ActionBar.spacing) {
            // 点赞按钮 - 复用EALikeButton但使用小尺寸
            EALikeButton(
                isLiked: $isLiked,
                likeCount: likeCount,
                size: .small,
                onToggle: onLike
            )
            
            // 回复按钮
            if let onReply = onReply {
                Button(action: {
                    handleReplyTap()
                    onReply()
                }) {
                    HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
                        Image(systemName: "arrowshape.turn.up.left")
                            .font(.system(size: EAAppConstants.Community.Comment.avatarSize * 0.375, weight: .medium))
                        
                        Text("回复")
                            .font(.system(size: EAAppConstants.Community.Comment.avatarSize * 0.375, weight: .medium))
                    }
                    .foregroundColor(Color.white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite))
                }
                .buttonStyle(PlainButtonStyle())
                .accessibilityLabel("回复评论")
                .accessibilityHint("点击回复这条评论")
            }
            
            Spacer()
        }
    }
    
    // MARK: - Cell Background
    
    private var cellBackground: some View {
        RoundedRectangle(cornerRadius: cellCornerRadius)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("2C5530").opacity(0.6),
                        Color.hexColor("1A3A1D").opacity(0.7)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cellCornerRadius)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0").opacity(0.2),
                                Color.white.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: EAAppConstants.Community.PostCard.borderWidth * 0.5
                    )
            )
    }
    
    // MARK: - Action Handlers
    
    /// 处理回复按钮点击
    private func handleReplyTap() {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    // MARK: - Helper Methods
    
    /// 格式化评论时间
    /// - Parameter date: 评论时间
    /// - Returns: 格式化后的时间字符串
    private func formatCommentTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    // MARK: - Computed Properties
    
    private var cellSpacing: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.Comment.metaSpacing
        case .compact: 
            return EAAppConstants.Community.Comment.metaSpacing * 0.75
        case .reply: 
            return EAAppConstants.Community.Comment.metaSpacing * 0.5
        }
    }
    
    private var cellCornerRadius: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.PostCard.cardCornerRadius * 0.75
        case .compact: 
            return EAAppConstants.Community.PostCard.cardCornerRadius * 0.6
        case .reply: 
            return EAAppConstants.Community.PostCard.cardCornerRadius * 0.5
        }
    }
    
    private var userNameFontSize: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.Typography.userNameFontSize * 0.875
        case .compact: 
            return EAAppConstants.Community.Typography.userNameFontSize * 0.8
        case .reply: 
            return EAAppConstants.Community.Typography.userNameFontSize * 0.75
        }
    }
    
    private var replyMetaFontSize: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.Typography.userMetaFontSize
        case .compact: 
            return EAAppConstants.Community.Typography.userMetaFontSize * 0.9
        case .reply: 
            return EAAppConstants.Community.Typography.userMetaFontSize * 0.85
        }
    }
    
    private var contentFontSize: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.Typography.contentFontSize * 0.93
        case .compact: 
            return EAAppConstants.Community.Typography.contentFontSize * 0.87
        case .reply: 
            return EAAppConstants.Community.Typography.contentFontSize * 0.8
        }
    }
    
    private var timeFontSize: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.Typography.timeFontSize
        case .compact: 
            return EAAppConstants.Community.Typography.timeFontSize * 0.9
        case .reply: 
            return EAAppConstants.Community.Typography.timeFontSize * 0.85
        }
    }
    
    private var shadowOpacity: Double {
        switch style {
        case .standard: 
            return EAAppConstants.Community.PostCard.shadowOpacity * 0.6
        case .compact: 
            return EAAppConstants.Community.PostCard.shadowOpacity * 0.4
        case .reply: 
            return EAAppConstants.Community.PostCard.shadowOpacity * 0.3
        }
    }
    
    private var shadowRadius: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.PostCard.shadowRadius * 0.5
        case .compact: 
            return EAAppConstants.Community.PostCard.shadowRadius * 0.375
        case .reply: 
            return EAAppConstants.Community.PostCard.shadowRadius * 0.25
        }
    }
    
    private var shadowOffset: CGFloat {
        switch style {
        case .standard: 
            return EAAppConstants.Community.PostCard.shadowOffset * 0.5
        case .compact: 
            return EAAppConstants.Community.PostCard.shadowOffset * 0.375
        case .reply: 
            return EAAppConstants.Community.PostCard.shadowOffset * 0.25
        }
    }
}

// MARK: - Comment Cell Style

/// 评论单元格样式
enum EACommentCellStyle {
    case standard  // 标准样式：用于主评论
    case compact   // 紧凑样式：用于列表展示
    case reply     // 回复样式：用于回复评论
}

// MARK: - Preview

struct EACommentCell_Previews: PreviewProvider {
    static var previews: some View {
    @Previewable @State var isLiked1 = false
    @Previewable @State var isLiked2 = true
    @Previewable @State var isLiked3 = false
    
    // 创建示例评论数据
    let standardComment = EACommunityComment(
        content: "这个习惯分享真的很有用！我也要试试这个方法，感谢分享～"
    )
    
    let replyComment = EACommunityComment(
        content: "确实很不错，我已经坚持了一周了，效果明显！",
        replyToUsername: "叶同学"
    )
    // 在创建后立即设置属性，避免在ViewBuilder中赋值
    let _ = { replyComment.isReply = true }()
    
    ScrollView {
        VStack(spacing: 20) {
            Text("评论组件预览")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.top, 20)
            
            VStack(spacing: 16) {
                // 标准样式评论
                VStack(alignment: .leading, spacing: 8) {
                    Text("标准样式")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    EACommentCell(
                        comment: standardComment,
                        isLiked: $isLiked1,
                        likeCount: 12,
                        style: .standard,
                        onLike: {
                            isLiked1.toggle()
                        },
                        onReply: {
                            // 处理回复
                        },
                        onUserTap: {
                            // 处理用户头像点击
                        }
                    )
                }
                
                // 紧凑样式评论
                VStack(alignment: .leading, spacing: 8) {
                    Text("紧凑样式")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    EACommentCell(
                        comment: standardComment,
                        isLiked: $isLiked2,
                        likeCount: 5,
                        style: .compact,
                        onLike: {
                            isLiked2.toggle()
                        }
                    )
                }
                
                // 回复样式评论
                VStack(alignment: .leading, spacing: 8) {
                    Text("回复样式")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    HStack {
                        Spacer()
                            .frame(width: EAAppConstants.Community.Comment.replyIndentation)
                        
                        EACommentCell(
                            comment: replyComment,
                            isLiked: $isLiked3,
                            likeCount: 3,
                            style: .reply,
                            onLike: {
                                isLiked3.toggle()
                            }
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
        }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(
        LinearGradient(
            gradient: Gradient(colors: [
                Color.hexColor("1A3A1D"),
                Color.hexColor("0F1E11")
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
    }
} 