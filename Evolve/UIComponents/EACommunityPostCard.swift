import SwiftUI

/// 数字宇宙社区帖子卡片 - 宇宙信标设计
/// 基于数字宇宙主题，将帖子呈现为星际探索者发布的宇宙信标
/// 支持星际能量显示、宇宙等级标识、能量流动动画等特效
/// 设计面向18-30岁年轻用户，具有强烈的科技感和未来感
struct EACommunityPostCard: View {
    
    // MARK: - Properties
    
    /// 帖子数据
    let post: EACommunityPost
    
    /// 是否已点赞
    @Binding var isLiked: Bool
    
    /// 点赞数量
    let likeCount: Int
    
    /// 卡片样式
    let style: EACommunityPostCardStyle
    
    /// 点赞回调
    let onLike: () -> Void
    
    /// 帖子点击回调
    let onPostTap: (() -> Void)?
    
    /// 用户头像点击回调
    let onUserTap: (() -> Void)?
    
    /// 图片点击回调
    let onImageTap: ((String, Int) -> Void)?
    
    // MARK: - Animation State
    
    @State private var cardScale: CGFloat = EAAppConstants.Community.PostCard.normalScale
    @State private var isPressed = false
    
    // MARK: - Digital Universe Animation State
    
    /// 能量流动动画进度
    @State private var energyFlowProgress: CGFloat = 0.0
    
    /// 宇宙信标脉冲动画
    @State private var beaconPulse: CGFloat = 1.0
    
    /// 星际能量粒子动画
    @State private var stellarParticleOffset: CGFloat = 0.0
    
    // MARK: - Initialization
    
    init(
        post: EACommunityPost,
        isLiked: Binding<Bool>,
        likeCount: Int = 0,
        style: EACommunityPostCardStyle = .standard,
        onLike: @escaping () -> Void,
        onPostTap: (() -> Void)? = nil,
        onUserTap: (() -> Void)? = nil,
        onImageTap: ((String, Int) -> Void)? = nil
    ) {
        self.post = post
        self._isLiked = isLiked
        self.likeCount = likeCount
        self.style = style
        self.onLike = onLike
        self.onPostTap = onPostTap
        self.onUserTap = onUserTap
        self.onImageTap = onImageTap
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.Community.PostCard.cardSpacing) {
            // 数字宇宙信标头部
            stellarBeaconHeader
            
            // 帖子内容（宇宙信息传输）
            postContent
            
            // 星际交互操作栏
            stellarActionBar
        }
        .padding(EAAppConstants.Community.PostCard.containerPadding)
        .background(digitalUniverseCardBackground)
        .clipShape(RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius))
        .overlay(style == .detail ? AnyView(EmptyView()) : AnyView(stellarEnergyOverlay))
        .shadow(
            color: Color.cyan.opacity(0.3),
            radius: EAAppConstants.Community.PostCard.shadowRadius,
            x: 0,
            y: EAAppConstants.Community.PostCard.shadowOffset
        )
        .scaleEffect(cardScale * (style == .detail ? 1.0 : beaconPulse))
        .onTapGesture {
            if let onPostTap = onPostTap {
                handleCardTap()
                onPostTap()
            }
        }
        .onLongPressGesture(
            minimumDuration: EAAppConstants.Community.Timing.longPressDuration, 
            maximumDistance: .infinity
        ) { isPressing in
            withAnimation(.easeInOut(duration: EAAppConstants.Community.Timing.cardAnimationDuration)) {
                isPressed = isPressing
                cardScale = isPressing ? EAAppConstants.Community.PostCard.pressedScale : EAAppConstants.Community.PostCard.normalScale
            }
        } perform: {}
        .onAppear {
            // 只在非详情页样式下启动动画
            if style != .detail {
                startDigitalUniverseAnimations()
            }
        }
    }
    
    // MARK: - Digital Universe Beacon Header
    
    /// 星际探索者信标头部 - 数字宇宙主题
    private var stellarBeaconHeader: some View {
        HStack(spacing: EAAppConstants.Community.PostCard.userInfoSpacing) {
            // 星际探索者头像
            stellarExplorerAvatar
            
            // 探索者信息 + 星际等级
            stellarExplorerInfo
            
            Spacer()
            
            // 宇宙信标时间戳
            cosmicTimestamp
        }
    }
    
    // MARK: - Legacy User Info Header (保持兼容性)
    
    private var userInfoHeader: some View {
        HStack(spacing: EAAppConstants.Community.PostCard.userInfoSpacing) {
            // 用户头像 - 复用EAAvatarView
            userAvatar
            
            // 用户信息
            userInfoText
            
            Spacer()
            
            // 发布时间
            postTimeText
        }
    }
    
    /// 星际探索者头像 - 数字宇宙主题
    private var stellarExplorerAvatar: some View {
        Button(action: {
            onUserTap?()
        }) {
            ZStack {
                // 外层能量光环
                Circle()
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.cyan.opacity(0.8),
                                Color.blue.opacity(0.6),
                                Color.purple.opacity(0.4)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 3
                    )
                    .frame(width: EAAppConstants.Community.PostCard.avatarSize + 8, 
                           height: EAAppConstants.Community.PostCard.avatarSize + 8)
                    .scaleEffect(beaconPulse)
                
                // 探索者头像
                EAAvatarView(
                    avatarData: nil, // 暂时使用默认头像
                    size: EAAppConstants.Community.PostCard.avatarSize,
                    showShadow: false
                )
                .overlay(
                    Circle()
                        .stroke(Color.cyan.opacity(0.5), lineWidth: 2)
                )
                
                // 星际等级指示器
                if let stellarLevel = getStellarLevel() {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            stellarLevelBadge(level: stellarLevel)
                                .offset(x: 8, y: 8)
                        }
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("查看星际探索者资料")
        .accessibilityHint("点击查看 \(post.getAuthorUsername()) 的探索者档案")
    }
    
    private var userAvatar: some View {
        Button(action: {
            onUserTap?()
        }) {
            // 复用EAAvatarView - 遵循开发规范要求
            // TODO: 等待用户模型添加avatarData属性
            EAAvatarView(
                avatarData: nil, // 暂时使用默认头像
                size: EAAppConstants.Community.PostCard.avatarSize,
                showShadow: false
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("查看用户资料")
        .accessibilityHint("点击查看 \(post.getAuthorUsername()) 的个人资料")
    }
    
    /// 星际探索者信息 - 数字宇宙主题
    private var stellarExplorerInfo: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.Community.PostCard.userTextSpacing) {
            // 探索者名称
            HStack(spacing: 6) {
                Text(post.getAuthorUsername())
                    .font(.system(size: EAAppConstants.Community.Typography.userNameFontSize, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                // 探索者认证标识
                Image(systemName: "checkmark.seal.fill")
                    .font(.system(size: 12))
                    .foregroundColor(Color.cyan)
            }
            
            // 星际等级和能量状态
            HStack(spacing: EAAppConstants.Community.PostCard.energyLevelSpacing) {
                // 星际能量指示器
                stellarEnergyIndicator
                
                if let stellarLevel = getStellarLevel() {
                    Text("Lv.\(stellarLevel)")
                        .font(.system(size: EAAppConstants.Community.Typography.userMetaFontSize, weight: .bold))
                        .foregroundColor(Color.cyan)
                } else {
                    // 兼容：显示传统能量等级
                    if post.energyLevel > 0 {
                        Text("能量 \(post.energyLevel)")
                            .font(.system(size: EAAppConstants.Community.Typography.userMetaFontSize, weight: .medium))
                            .foregroundColor(Color.cyan.opacity(0.8))
                    }
                }
                
                // 探索者称号
                if let explorerTitle = getExplorerTitle() {
                    Text("• \(explorerTitle)")
                        .font(.system(size: EAAppConstants.Community.Typography.userMetaFontSize - 1, weight: .medium))
                        .foregroundColor(Color.white.opacity(0.7))
                        .lineLimit(1)
                }
            }
        }
    }
    
    private var userInfoText: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.Community.PostCard.userTextSpacing) {
            // 用户名
            Text(post.getAuthorUsername())
                .font(.system(size: EAAppConstants.Community.Typography.userNameFontSize, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            // 用户能量等级或其他标识
            if post.energyLevel > 0 {
                let energyLevel = post.energyLevel
                HStack(spacing: EAAppConstants.Community.PostCard.energyLevelSpacing) {
                    Image(systemName: "bolt.fill")
                        .font(.system(size: EAAppConstants.Community.Typography.energyIconFontSize))
                        .foregroundColor(Color.hexColor("40E0D0"))
                    
                    Text("能量等级 \(energyLevel)")
                        .font(.system(size: EAAppConstants.Community.Typography.userMetaFontSize, weight: .medium))
                        .foregroundColor(Color.white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite))
                }
            }
        }
    }
    
    /// 宇宙信标时间戳 - 数字宇宙主题
    private var cosmicTimestamp: some View {
        VStack(alignment: .trailing, spacing: 4) {
            // 宇宙标准时间
            HStack(spacing: 4) {
                Image(systemName: "clock.badge.checkmark")
                    .font(.system(size: 10))
                    .foregroundColor(Color.cyan.opacity(0.8))
                
                Text(formatPostTime(post.creationDate))
                    .font(.system(size: EAAppConstants.Community.Typography.timeFontSize - 1, weight: .medium))
                    .foregroundColor(Color.cyan.opacity(0.8))
            }
            
            // 星际分类标识
            if let stellarCategory = getStellarCategory() {
                Text(stellarCategory)
                    .font(.system(size: 9, weight: .medium))
                    .foregroundColor(Color.purple.opacity(0.8))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(Color.purple.opacity(0.2))
                            .overlay(
                                Capsule()
                                    .stroke(Color.purple.opacity(0.4), lineWidth: 0.5)
                            )
                    )
            }
        }
    }
    
    private var postTimeText: some View {
        Text(formatPostTime(post.creationDate))
            .font(.system(size: EAAppConstants.Community.Typography.timeFontSize, weight: .medium))
            .foregroundColor(Color.white.opacity(EAAppConstants.Community.ColorOpacity.tertiaryWhite))
    }
    
    // MARK: - Post Content
    
    @ViewBuilder
    private var postContent: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.Community.PostCard.habitInfoSpacing) {
            // 帖子文本内容
            if !post.content.isEmpty {
                Text(post.content)
                    .font(.system(size: EAAppConstants.Community.Typography.contentFontSize, weight: .regular))
                    .foregroundColor(.white)
                    .lineSpacing(EAAppConstants.Community.Typography.lineSpacing)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            // 图片网格（如果有图片）
            if !post.imageURLs.isEmpty {
                EAImageGridView(
                    imagePaths: post.imageURLs,
                    onImageTap: { imagePath, index in
                        onImageTap?(imagePath, index)
                    }
                )
                .padding(.top, post.content.isEmpty ? 0 : 8)
            }
            
            // 相关习惯信息（如果有）
            if let habitName = post.habitName, !habitName.isEmpty {
                habitRelatedInfo(habitName)
            }
        }
    }
    
    private func habitRelatedInfo(_ habitPreview: String) -> some View {
        HStack(spacing: EAAppConstants.Community.PostCard.habitInfoSpacing) {
            Image(systemName: "leaf.circle.fill")
                .font(.system(size: EAAppConstants.Community.Typography.habitIconFontSize))
                .foregroundColor(Color.hexColor("40E0D0"))
            
            Text("相关习惯: \(habitPreview)")
                .font(.system(size: EAAppConstants.Community.Typography.habitTagFontSize, weight: .medium))
                .foregroundColor(Color.hexColor("40E0D0"))
                .lineLimit(1)
        }
        .padding(.horizontal, EAAppConstants.Community.PostCard.habitInfoSpacing + 4)
        .padding(.vertical, EAAppConstants.Community.PostCard.energyLevelSpacing + 2)
        .background(
            RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.habitTagCornerRadius)
                .fill(Color.hexColor("40E0D0").opacity(EAAppConstants.Community.ColorOpacity.habitTagBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.habitTagCornerRadius)
                        .stroke(Color.hexColor("40E0D0").opacity(EAAppConstants.Community.ColorOpacity.habitTagBorder), lineWidth: EAAppConstants.Community.PostCard.borderWidth)
                )
        )
    }
    
    // MARK: - Stellar Action Bar (数字宇宙主题)
    
    /// 星际交互操作栏 - 数字宇宙主题
    private var stellarActionBar: some View {
        HStack(spacing: EAAppConstants.Community.ActionBar.spacing) {
            // 能量共振按钮（点赞）
            energyResonanceButton
            
            // 信息传输按钮（评论）
            messageTransmissionButton
            
            Spacer()
            
            // 星际能量输出显示
            stellarEnergyOutput
        }
    }
    
    // MARK: - Legacy Action Bar (保持兼容性)
    
    private var actionBar: some View {
        HStack(spacing: EAAppConstants.Community.ActionBar.spacing) {
            // 点赞按钮
            likeButton
            
            // 评论按钮
            commentButton
            
            Spacer()
            
            // 能量值显示（如果有）
            if post.energyBoost > 0 {
                energyBoostDisplay
            }
        }
    }
    
    /// 能量共振按钮 - 数字宇宙主题
    private var energyResonanceButton: some View {
        Button(action: {
            handleLikeTap()
        }) {
            HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
                ZStack {
                    // 背景能量脉冲
                    if isLiked {
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color.cyan.opacity(0.8),
                                        Color.blue.opacity(0.4),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 2,
                                    endRadius: 12
                                )
                            )
                            .frame(width: 24, height: 24)
                            .scaleEffect(energyFlowProgress)
                    }
                    
                    // 能量共振图标
                    Image(systemName: isLiked ? "waveform.path.ecg" : "waveform.path")
                        .font(.system(size: EAAppConstants.Community.ActionBar.iconSize))
                        .foregroundColor(isLiked ? Color.cyan : Color.white.opacity(0.7))
                }
                
                Text("\(likeCount)")
                    .font(.system(size: EAAppConstants.Community.Typography.actionButtonFontSize, weight: .medium))
                    .foregroundColor(isLiked ? Color.cyan : Color.white.opacity(0.7))
            }
            .padding(.horizontal, EAAppConstants.Community.ActionBar.buttonPadding)
            .frame(height: EAAppConstants.Community.ActionBar.buttonHeight)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.Community.ActionBar.buttonCornerRadius)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.cyan.opacity(isLiked ? 0.3 : 0.1),
                                Color.blue.opacity(isLiked ? 0.2 : 0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: EAAppConstants.Community.ActionBar.buttonCornerRadius)
                            .stroke(Color.cyan.opacity(isLiked ? 0.6 : 0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(isLiked ? "取消能量共振" : "能量共振")
        .accessibilityHint("点击\(isLiked ? "取消" : "")与此信标产生能量共振")
    }
    
    private var likeButton: some View {
        Button(action: {
            handleLikeTap()
        }) {
            HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
                Image(systemName: isLiked ? "heart.fill" : "heart")
                    .font(.system(size: EAAppConstants.Community.ActionBar.iconSize))
                    .foregroundColor(isLiked ? .red : .white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite))
                
                Text("\(likeCount)")
                    .font(.system(size: EAAppConstants.Community.Typography.actionButtonFontSize, weight: .medium))
                    .foregroundColor(.white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite))
            }
            .padding(.horizontal, EAAppConstants.Community.ActionBar.buttonPadding)
            .frame(height: EAAppConstants.Community.ActionBar.buttonHeight)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.Community.ActionBar.buttonCornerRadius)
                    .fill(Color.white.opacity(EAAppConstants.Community.ColorOpacity.actionButtonBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(isLiked ? "取消点赞" : "点赞")
        .accessibilityHint("点击\(isLiked ? "取消" : "")点赞此帖子")
    }
    
    /// 信息传输按钮（评论） - 数字宇宙主题
    private var messageTransmissionButton: some View {
        Button(action: {
            handleCommentTap()
        }) {
            HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
                ZStack {
                    // 信息传输动效
                    Circle()
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.green.opacity(0.6),
                                    Color.blue.opacity(0.3)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                        .frame(width: 20, height: 20)
                        .scaleEffect(stellarParticleOffset)
                    
                    // 信息传输图标
                    Image(systemName: "antenna.radiowaves.left.and.right")
                        .font(.system(size: EAAppConstants.Community.ActionBar.iconSize))
                        .foregroundColor(Color.green.opacity(0.8))
                }
                
                Text("\(post.getVisibleCommentsCount())")
                    .font(.system(size: EAAppConstants.Community.Typography.actionButtonFontSize, weight: .medium))
                    .foregroundColor(Color.green.opacity(0.8))
            }
            .padding(.horizontal, EAAppConstants.Community.ActionBar.buttonPadding)
            .frame(height: EAAppConstants.Community.ActionBar.buttonHeight)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.Community.ActionBar.buttonCornerRadius)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.green.opacity(0.2),
                                Color.blue.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: EAAppConstants.Community.ActionBar.buttonCornerRadius)
                            .stroke(Color.green.opacity(0.4), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("星际通讯")
        .accessibilityHint("点击与其他探索者进行星际通讯")
    }
    
    private var commentButton: some View {
        Button(action: {
            handleCommentTap()
        }) {
            HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
                Image(systemName: "bubble.left")
                    .font(.system(size: EAAppConstants.Community.ActionBar.iconSize))
                    .foregroundColor(.white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite))
                
                Text("\(post.getVisibleCommentsCount())")
                    .font(.system(size: EAAppConstants.Community.Typography.actionButtonFontSize, weight: .medium))
                    .foregroundColor(.white.opacity(EAAppConstants.Community.ColorOpacity.secondaryWhite))
            }
            .padding(.horizontal, EAAppConstants.Community.ActionBar.buttonPadding)
            .frame(height: EAAppConstants.Community.ActionBar.buttonHeight)
            .background(
                RoundedRectangle(cornerRadius: EAAppConstants.Community.ActionBar.buttonCornerRadius)
                    .fill(Color.white.opacity(EAAppConstants.Community.ColorOpacity.actionButtonBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("查看评论")
        .accessibilityHint("点击查看帖子评论")
    }
    
    private var energyBoostDisplay: some View {
        HStack(spacing: EAAppConstants.Community.ActionBar.buttonSpacing) {
            Image(systemName: "sparkles")
                .font(.system(size: EAAppConstants.Community.ActionBar.iconSize))
                .foregroundColor(Color.hexColor("40E0D0"))
            
            Text("+\(post.energyBoost)")
                .font(.system(size: EAAppConstants.Community.Typography.actionButtonFontSize, weight: .bold))
                .foregroundColor(Color.hexColor("40E0D0"))
        }
        .accessibilityLabel("能量提升 \(post.energyBoost) 点")
    }
    
    // MARK: - Digital Universe Card Background
    
    /// 数字宇宙信标背景 - 科技感设计
    private var digitalUniverseCardBackground: some View {
        ZStack {
            // 主背景：深空渐变
            RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.black.opacity(0.9),
                            Color.blue.opacity(0.3),
                            Color.purple.opacity(0.2),
                            Color.black.opacity(0.95)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 星际网格效果
            RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius)
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.cyan.opacity(0.1),
                            Color.blue.opacity(0.05),
                            Color.clear
                        ]),
                        center: .topTrailing,
                        startRadius: 20,
                        endRadius: 100
                    )
                )
            
            // 能量流线条
            RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.cyan.opacity(0.6),
                            Color.blue.opacity(0.4),
                            Color.purple.opacity(0.3),
                            Color.cyan.opacity(0.2)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        }
    }
    
    /// 星际能量覆盖层 - 动态能量效果
    private var stellarEnergyOverlay: some View {
        GeometryReader { geometry in
            ZStack {
                // 能量流动路径
                Path { path in
                    let width = geometry.size.width
                    let height = geometry.size.height
                    
                    // 创建流动的能量线条
                    path.move(to: CGPoint(x: 0, y: height * 0.3))
                    path.addQuadCurve(
                        to: CGPoint(x: width, y: height * 0.7),
                        control: CGPoint(x: width * 0.5, y: height * 0.1)
                    )
                }
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.clear,
                            Color.cyan.opacity(0.4 * energyFlowProgress),
                            Color.blue.opacity(0.3 * energyFlowProgress),
                            Color.clear
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    ),
                    style: StrokeStyle(lineWidth: 2, lineCap: .round)
                )
                
                // 星际粒子效果
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(Color.cyan.opacity(0.6))
                        .frame(width: 3, height: 3)
                        .offset(
                            x: (geometry.size.width * CGFloat(index) / 3.0) + stellarParticleOffset,
                            y: geometry.size.height * 0.2
                        )
                        .animation(
                            .linear(duration: 2.0)
                                .repeatForever(autoreverses: false)
                                .delay(Double(index) * 0.5),
                            value: stellarParticleOffset
                        )
                }
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius))
        .allowsHitTesting(false)
    }
    
    // MARK: - Legacy Card Background (保持兼容性)
    
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.hexColor("2C5530").opacity(0.9),
                        Color.hexColor("1A3A1D").opacity(0.95)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: EAAppConstants.Community.PostCard.cardCornerRadius)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("40E0D0").opacity(0.3),
                                Color.white.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: EAAppConstants.Community.PostCard.borderWidth
                    )
            )
    }
    
    // MARK: - Action Handlers
    
    /// 处理卡片点击
    private func handleCardTap() {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 卡片缩放动画
        withAnimation(.easeInOut(duration: EAAppConstants.Community.Timing.cardAnimationDuration)) {
            cardScale = EAAppConstants.Community.PostCard.pressedScale
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + EAAppConstants.Community.Timing.cardAnimationDuration) {
            withAnimation(.easeInOut(duration: EAAppConstants.Community.Timing.cardAnimationDuration)) {
                cardScale = EAAppConstants.Community.PostCard.normalScale
            }
        }
    }
    
    /// 处理点赞点击
    private func handleLikeTap() {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 执行点赞回调
        onLike()
    }
    
    /// 处理评论按钮点击
    private func handleCommentTap() {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 执行帖子点击回调（显示详情页，包含评论）
        onPostTap?()
    }
    
    // MARK: - Helper Methods
    
    /// 启动数字宇宙动画
    private func startDigitalUniverseAnimations() {
        // 能量流动动画
        withAnimation(.linear(duration: 3.0).repeatForever(autoreverses: true)) {
            energyFlowProgress = 1.0
        }
        
        // 信标脉冲动画
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            beaconPulse = 1.05
        }
        
        // 星际粒子流动
        withAnimation(.linear(duration: 4.0).repeatForever(autoreverses: false)) {
            stellarParticleOffset = 200
        }
    }
    
    /// 获取星际等级
    private func getStellarLevel() -> Int? {
        // 通过帖子的能量等级模拟星际等级
        // 实际项目中应从EAUserSocialProfile获取
        guard post.energyLevel > 0 else { return nil }
        return min(post.energyLevel * 2, 20) // 简单映射到星际等级
    }
    
    /// 获取探索者称号
    private func getExplorerTitle() -> String? {
        guard let level = getStellarLevel() else { return nil }
        
        switch level {
        case 1...5:
            return "新手探索者"
        case 6...10:
            return "星际旅者"
        case 11...15:
            return "宇宙航行者"
        case 16...20:
            return "星系守护者"
        default:
            return "传奇探索者"
        }
    }
    
    /// 获取星际分类
    private func getStellarCategory() -> String? {
        // 优先使用数字宇宙分类，回退到传统分类映射
        if let stellarCategory = post.stellarCategory {
            return stellarCategory
        }
        
        // 传统分类到星际分类的映射
        switch post.category {
        case "achievement":
            return "成就展示"
        case "challenge":
            return "挑战宣言"
        case "sharing":
            return "能量传递"
        default:
            return "探索分享"
        }
    }
    
    /// 星际等级徽章
    private func stellarLevelBadge(level: Int) -> some View {
        Text("\(level)")
            .font(.system(size: 10, weight: .bold))
            .foregroundColor(.white)
            .frame(width: 18, height: 18)
            .background(
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.cyan,
                                Color.blue
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.8), lineWidth: 1)
                    )
            )
    }
    
    /// 星际能量指示器
    private var stellarEnergyIndicator: some View {
        HStack(spacing: 2) {
            ForEach(0..<3, id: \.self) { index in
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.cyan,
                                Color.blue
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 2, height: CGFloat(6 + index * 2))
                    .animation(
                        .easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                        value: energyFlowProgress
                    )
            }
        }
    }
    
    /// 星际能量输出显示
    private var stellarEnergyOutput: some View {
        HStack(spacing: 6) {
            // 能量输出图标
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.yellow.opacity(0.8),
                                Color.orange.opacity(0.4),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 2,
                            endRadius: 12
                        )
                    )
                    .frame(width: 20, height: 20)
                    .scaleEffect(beaconPulse)
                
                Image(systemName: "star.fill")
                    .font(.system(size: 12))
                    .foregroundColor(Color.yellow)
            }
            
            // 能量数值
            VStack(alignment: .leading, spacing: 2) {
                if let stellarEnergy = post.stellarEnergyValue ?? Optional(post.energyBoost + 10) {
                    Text("\(stellarEnergy)")
                        .font(.system(size: EAAppConstants.Community.Typography.actionButtonFontSize, weight: .bold))
                        .foregroundColor(Color.yellow)
                }
                
                Text("星际能量")
                    .font(.system(size: 8, weight: .medium))
                    .foregroundColor(Color.yellow.opacity(0.8))
            }
        }
        .accessibilityLabel("星际能量输出")
    }
    
    /// 格式化发布时间
    /// - Parameter date: 发布时间
    /// - Returns: 格式化后的时间字符串
    private func formatPostTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Card Style

/// 帖子卡片样式
enum EACommunityPostCardStyle {
    case standard
    case compact
    case featured
    case detail // 详情页样式，禁用动画效果
    
    var cardCornerRadius: CGFloat {
        switch self {
        case .standard:
            return EAAppConstants.Community.PostCard.cardCornerRadius
        case .compact:
            return EAAppConstants.Community.PostCard.cardCornerRadius - 4
        case .featured:
            return EAAppConstants.Community.PostCard.cardCornerRadius + 4
        case .detail:
            return EAAppConstants.Community.PostCard.cardCornerRadius
        }
    }
}

// MARK: - Preview

struct EACommunityPostCard_Previews: PreviewProvider {
    static var previews: some View {
    @Previewable @State var isLiked = false
    
    // 创建示例帖子数据
    let samplePost = EACommunityPost(
        content: "今天成功完成了早起习惯的第30天！感觉整个人的精神状态都有了很大提升，早晨的阳光格外美好。坚持真的会带来改变，感谢大家的支持和鼓励！💪🌅",
        habitName: "早起习惯",
        category: "achievement",
        energyLevel: 8
    )
    
    VStack(spacing: 20) {
        // 标准样式
        EACommunityPostCard(
            post: samplePost,
            isLiked: $isLiked,
            likeCount: 12,
            style: .standard,
            onLike: {
                isLiked.toggle()
            },
            onPostTap: {
                // 处理帖子点击
            },
            onUserTap: {
                // 处理用户头像点击
            }
        )
        .padding(.horizontal, 20)
        
        Spacer()
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(
        LinearGradient(
            gradient: Gradient(colors: [
                Color.hexColor("1A3A1D"),
                Color.hexColor("0F1E11")
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
    }
} 