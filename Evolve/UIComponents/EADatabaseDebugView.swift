import SwiftUI
import SwiftData

/// 数据库调试视图 - 用于验证持久化功能和查看数据库状态
/// 叶同学专用：在开发阶段查看数据是否真正保存到磁盘
struct EADatabaseDebugView: View {
    @Environment(\.repositoryContainer) private var repositories
    @StateObject private var databaseManager = EADatabaseManager()
    @EnvironmentObject var sessionManager: EASessionManager
    
    @State private var databaseStats: (users: Int, habits: Int, completions: Int) = (0, 0, 0)
    @State private var databaseFileSize: String = "计算中..."
    @State private var lastRefreshTime: Date = Date()
    @State private var persistenceTestResult: String = "未测试"
    @State private var isLoading: Bool = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    
                    // 顶部状态卡片
                    statusCard
                    
                    // 当前用户信息卡片
                    currentUserCard
                    
                    // 数据库统计卡片
                    statisticsCard
                    
                    // 持久化测试卡片
                    persistenceTestCard
                    
                    // 操作按钮区域
                    actionButtons
                    
                    Spacer(minLength: 20)
                }
                .padding()
            }
            .navigationTitle("数据库调试")
            .navigationBarTitleDisplayMode(.inline)
            .refreshable {
                await refreshAllData()
            }
            .onAppear {
                Task {
                    await refreshAllData()
                }
            }
        }
    }
    
    // MARK: - 状态卡片
    
    private var statusCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "cylinder.fill")
                    .foregroundColor(.green)
                Text("数据库状态")
                    .font(.headline)
                Spacer()
                Text(databaseManager.getStateDescription())
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .cornerRadius(8)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("文件大小: \(databaseFileSize)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("最后更新: \(formatTime(lastRefreshTime))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 当前用户卡片
    
    private var currentUserCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "person.circle.fill")
                    .foregroundColor(.blue)
                Text("当前用户")
                    .font(.headline)
                Spacer()
                if sessionManager.isLoggedIn {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            
            if let currentUser = sessionManager.currentUser {
                VStack(alignment: .leading, spacing: 4) {
                    Text("用户名: \(currentUser.username)")
                        .font(.subheadline)
                    Text("邮箱: \(currentUser.email ?? "未设置")")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Text("ID: \(currentUser.id.uuidString.prefix(8))...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                Text("未登录")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 统计卡片
    
    private var statisticsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.orange)
                Text("数据统计")
                    .font(.headline)
                Spacer()
            }
            
            HStack(spacing: 20) {
                statisticItem(title: "用户", count: databaseStats.users, icon: "person.fill")
                statisticItem(title: "习惯", count: databaseStats.habits, icon: "target")
                statisticItem(title: "完成记录", count: databaseStats.completions, icon: "checkmark.circle.fill")
            }
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }
    
    private func statisticItem(title: String, count: Int, icon: String) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.orange)
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 持久化测试卡片
    
    private var persistenceTestCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "checkmark.shield.fill")
                    .foregroundColor(.purple)
                Text("持久化测试")
                    .font(.headline)
                Spacer()
            }
            
            Text("测试结果: \(persistenceTestResult)")
                .font(.subheadline)
                .foregroundColor(persistenceTestResult.contains("成功") ? .green : 
                                persistenceTestResult.contains("失败") ? .red : .secondary)
            
            Text("此测试会创建一个临时用户，验证数据能否正确保存和读取，然后自动清理测试数据。")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.purple.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - 操作按钮
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            
            // 刷新数据按钮
            Button(action: {
                Task {
                    await refreshAllData()
                }
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "arrow.clockwise")
                    }
                    Text("刷新数据")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isLoading)
            
            // 测试持久化按钮
            Button(action: {
                Task {
                    await testPersistence()
                }
            }) {
                HStack {
                    Image(systemName: "checkmark.shield")
                    Text("测试持久化")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.purple)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isLoading)
            
            // 打印报告按钮
            Button(action: {
                Task {
                    let report = await databaseManager.generatePersistenceReport()
                    #if DEBUG
                    print(report)
                    #endif
                }
            }) {
                HStack {
                    Image(systemName: "doc.text")
                    Text("打印报告到控制台")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            
            // 紧急重置按钮（危险操作）
            Button(action: {
                // 这个按钮暂时只显示提示，实际重置需要在AppEntry.swift中手动操作
                #if DEBUG
                print("⚠️ 如需完全重置数据库，请：")
                print("1. 在AppEntry.swift中取消注释forceDeleteAllSwiftDataFiles()方法")
                print("2. 重新运行应用")
                print("3. 运行完成后记得重新注释该方法")
                #endif
            }) {
                HStack {
                    Image(systemName: "trash.fill")
                    Text("重置说明（查看控制台）")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func refreshAllData() async {
        isLoading = true
        
        // 刷新统计数据
        databaseStats = await databaseManager.getDatabaseStatistics()
        
        // 刷新文件大小
        databaseFileSize = databaseManager.getDatabaseFileSize()
        
        // 更新时间
        lastRefreshTime = Date()
        
        isLoading = false
    }
    
    private func testPersistence() async {
        isLoading = true
        persistenceTestResult = "测试中..."
        
        let success = await databaseManager.validatePersistence()
        persistenceTestResult = success ? "✅ 持久化测试成功" : "❌ 持久化测试失败"
        
        // 测试完成后刷新统计数据
        await refreshAllData()
        
        isLoading = false
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        formatter.dateStyle = .none
        return formatter.string(from: date)
    }
}

// MARK: - Preview

#Preview {
    EADatabaseDebugView()
        .modelContainer(for: EAUser.self, inMemory: true)
} 