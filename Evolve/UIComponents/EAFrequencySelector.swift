import SwiftUI

/// 增强的频率选择组件
/// 支持每周、全天、每月等多种打卡模式，提供灵活的日期选择
struct EAFrequencySelector: View {
    @Binding var selectedFrequencyType: FrequencyType
    @Binding var selectedWeekdays: Set<Int> // 1-7 代表周一到周日
    @Binding var dailyTarget: Int // 每日目标次数
    @Binding var monthlyTarget: Int // 每月目标次数
    @Binding var selectedMonthlyDates: Set<Int> // 每月自定义日期
    @Binding var monthlyMode: MonthlyMode // 每月执行模式
    
    // 预设的每日目标选项
    private let dailyOptions = [1, 2, 3, 5, 8, 10]
    // 预设的每月目标选项
    private let monthlyOptions = [5, 10, 15, 20, 25, 30]
    // 星期名称
    private let weekdayNames = ["一", "二", "三", "四", "五", "六", "日"]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 标题
            Text("打卡频率")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            
            // 频率类型选择器
            frequencyTypeSelector
            
            // 根据选择的频率类型显示不同的配置选项
            switch selectedFrequencyType {
            case .weekly:
                weeklySelector
            case .daily:
                dailySelector
            case .monthly:
                monthlySelector
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 频率类型选择器
    private var frequencyTypeSelector: some View {
        HStack(spacing: 12) {
            ForEach(FrequencyType.allCases, id: \.self) { type in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedFrequencyType = type
                        resetSettingsForType(type)
                    }
                }) {
                    Text(type.description)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(selectedFrequencyType == type ? .white : .white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedFrequencyType == type ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(
                                            selectedFrequencyType == type ? 
                                            Color.hexColor("40E0D0") : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - 每周选择器
    private var weeklySelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("选择执行日期")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        if selectedWeekdays.count == 7 {
                            selectedWeekdays.removeAll()
                        } else {
                            selectedWeekdays = Set(1...7)
                        }
                    }
                }) {
                    Text(selectedWeekdays.count == 7 ? "取消全选" : "全选")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            HStack(spacing: 8) {
                ForEach(1...7, id: \.self) { weekday in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            if selectedWeekdays.contains(weekday) {
                                selectedWeekdays.remove(weekday)
                            } else {
                                selectedWeekdays.insert(weekday)
                            }
                        }
                    }) {
                        VStack(spacing: 4) {
                            Text("周\(weekdayNames[weekday - 1])")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(selectedWeekdays.contains(weekday) ? .white : .white)
                            
                            Circle()
                                .fill(selectedWeekdays.contains(weekday) ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.3))
                                .frame(width: 8, height: 8)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedWeekdays.contains(weekday) ? 
                                      Color.hexColor("40E0D0").opacity(0.15) : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            selectedWeekdays.contains(weekday) ? 
                                            Color.hexColor("40E0D0").opacity(0.5) : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            if !selectedWeekdays.isEmpty {
                Text("已选择 \(selectedWeekdays.count) 天")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - 每日选择器
    private var dailySelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("每日目标次数")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
            
            // 🔑 优化：显示选择提示
            if dailyTarget == 0 {
                Text("请选择每日目标次数")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(dailyOptions, id: \.self) { option in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            dailyTarget = option
                        }
                    }) {
                        VStack(spacing: 4) {
                            Text("\(option)")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(dailyTarget == option ? .white : .white)
                            
                            Text("次/日")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(dailyTarget == option ? .white : Color.white.opacity(0.7))
                        }
                        .frame(height: 60)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(dailyTarget == option ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            dailyTarget == option ? 
                                            Color.hexColor("40E0D0") : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 🔑 优化：显示已选择状态
            if dailyTarget > 0 {
                Text("已选择每日 \(dailyTarget) 次")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
    }
    
    // MARK: - 每月选择器（重新设计，明确区分两种模式）
    private var monthlySelector: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 每月模式选择
            VStack(alignment: .leading, spacing: 12) {
                Text("执行方式")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                
                HStack(spacing: 12) {
                    // 按次数模式
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            monthlyMode = .target
                            // 切换到按次数模式时，清空自定义日期
                            selectedMonthlyDates.removeAll()
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: monthlyMode == .target ? "checkmark.circle.fill" : "circle")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(monthlyMode == .target ? Color.hexColor("40E0D0") : Color.white.opacity(0.6))
                            
                            Text("按次数目标")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(monthlyMode == .target ? .white : Color.white.opacity(0.7))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(monthlyMode == .target ? 
                                      Color.hexColor("40E0D0").opacity(0.15) : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            monthlyMode == .target ? 
                                            Color.hexColor("40E0D0").opacity(0.5) : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // 按日期模式
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            monthlyMode = .dates
                            // 🔑 优化：不自动设置默认日期，让用户自己选择
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: monthlyMode == .dates ? "checkmark.circle.fill" : "circle")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(monthlyMode == .dates ? Color.hexColor("40E0D0") : Color.white.opacity(0.6))
                            
                            Text("按指定日期")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(monthlyMode == .dates ? .white : Color.white.opacity(0.7))
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(monthlyMode == .dates ? 
                                      Color.hexColor("40E0D0").opacity(0.15) : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            monthlyMode == .dates ? 
                                            Color.hexColor("40E0D0").opacity(0.5) : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 根据选择的模式显示对应的配置
            if monthlyMode == .target {
                monthlyTargetSelector
            } else {
                monthlyDatesSelector
            }
        }
    }
    
    // MARK: - 每月目标次数选择器
    private var monthlyTargetSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("每月目标次数")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.white.opacity(0.8))
            
            Text("可以在任意时间完成，达到目标次数即可")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(Color.white.opacity(0.6))
            
            // 🔑 优化：显示选择提示
            if monthlyTarget == 0 {
                Text("请选择每月目标次数")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(monthlyOptions, id: \.self) { option in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            monthlyTarget = option
                        }
                    }) {
                        VStack(spacing: 4) {
                            Text("\(option)")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(monthlyTarget == option ? .white : .white)
                            
                            Text("次/月")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(monthlyTarget == option ? .white : Color.white.opacity(0.7))
                        }
                        .frame(height: 50)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(monthlyTarget == option ? 
                                      Color.hexColor("40E0D0") : 
                                      Color.white.opacity(0.05))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            monthlyTarget == option ? 
                                            Color.hexColor("40E0D0") : 
                                            Color.white.opacity(0.1),
                                            lineWidth: 1
                                        )
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // 🔑 优化：显示已选择状态
            if monthlyTarget > 0 {
                Text("已选择每月 \(monthlyTarget) 次")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
    }
    
    // MARK: - 每月自定义日期选择器
    private var monthlyDatesSelector: some View {
        EACalendarDateSelector(
            selectedDates: $selectedMonthlyDates,
            title: "选择执行日期",
            subtitle: "只在选定的日期提醒和执行"
        )
    }
}

// MARK: - 辅助方法
extension EAFrequencySelector {
    
    // 切换星期选择
    private func toggleWeekday(_ weekday: Int) {
        if selectedWeekdays.contains(weekday) {
            selectedWeekdays.remove(weekday)
        } else {
            selectedWeekdays.insert(weekday)
        }
    }
    
    // 全选/取消全选星期
    private func toggleAllWeekdays() {
        if selectedWeekdays.count == 7 {
            selectedWeekdays.removeAll()
        } else {
            selectedWeekdays = Set(1...7)
        }
    }
    
    // 🔑 优化：切换频率类型时重置设置 - 移除自动默认选择，让用户自己选择
    private func resetSettingsForType(_ type: FrequencyType) {
        switch type {
        case .weekly:
            // 🔑 优化：不自动设置默认选择，保持空状态让用户自己选择
            break
        case .daily:
            // 🔑 优化：不自动设置默认选择，保持空状态让用户自己选择
            break
        case .monthly:
            // 🔑 优化：不自动设置默认选择，保持空状态让用户自己选择
            break
        }
    }
}

// MARK: - 频率类型枚举
enum FrequencyType: String, CaseIterable {
    case weekly = "weekly"
    case daily = "daily"
    case monthly = "monthly"
    
    var description: String {
        switch self {
        case .weekly:
            return "每周"
        case .daily:
            return "全天"
        case .monthly:
            return "每月"
        }
    }
}

// MARK: - 每月模式枚举
enum MonthlyMode: String, CaseIterable {
    case target = "target" // 按次数目标
    case dates = "dates"   // 按指定日期
    
    var description: String {
        switch self {
        case .target:
            return "按次数目标"
        case .dates:
            return "按指定日期"
        }
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        EAFrequencySelector(
            selectedFrequencyType: .constant(.weekly),
            selectedWeekdays: .constant(Set([1, 3, 5])),
            dailyTarget: .constant(1),
            monthlyTarget: .constant(15),
            selectedMonthlyDates: .constant(Set([1, 15])),
            monthlyMode: .constant(.target)
        )
        .padding()
    }
} 