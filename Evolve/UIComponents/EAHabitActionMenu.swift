import SwiftUI
import SwiftData

// MARK: - 习惯功能菜单弹窗组件 - 重新设计符合iOS规范
struct EAHabitActionMenu: View {
    let habit: EAHabit
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var showReminderSettings = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color.secondary.opacity(0.4))
                .frame(width: 36, height: 5)
                .padding(.top, 12)
                .padding(.bottom, 24)
            
            // 菜单选项 - 重新设计为更简洁的iOS风格
            VStack(spacing: 1) {
                // 编辑
                ActionMenuItem(
                    icon: "pencil",
                    title: "编辑",
                    iconColor: Color.blue,
                    isFirst: true
                ) {
                    onEdit()
                    dismiss()
                }
                .accessibilityLabel("编辑习惯")
                .accessibilityHint("点击编辑习惯的名称、图标和频率设置")
                
                // 提醒设置
                ActionMenuItem(
                    icon: "bell",
                    title: "提醒设置",
                    iconColor: Color.orange
                ) {
                    showReminderSettings = true
                }
                .accessibilityLabel("设置提醒")
                .accessibilityHint("配置习惯的提醒时间和频率")
                
                // 删除习惯
                ActionMenuItem(
                    icon: "trash",
                    title: "删除习惯",
                    iconColor: Color.red,
                    isLast: true
                ) {
                    onDelete()
                    dismiss()
                }
                .accessibilityLabel("删除习惯")
                .accessibilityHint("永久删除此习惯及其所有记录")
            }
            .background(
                RoundedRectangle(cornerRadius: 14)
                    .fill(.regularMaterial)
            )
            .padding(.horizontal, 20)
            
            Spacer(minLength: 40)
        }
        .background(Color.clear)
        .sheet(isPresented: $showReminderSettings) {
            EAHabitReminderSettingsView(habit: habit)
        }
    }
}

// MARK: - 菜单项组件 - 重新设计为iOS原生风格
struct ActionMenuItem: View {
    let icon: String
    let title: String
    let iconColor: Color
    let isFirst: Bool
    let isLast: Bool
    let action: () -> Void
    
    init(
        icon: String,
        title: String,
        iconColor: Color = .primary,
        isFirst: Bool = false,
        isLast: Bool = false,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.iconColor = iconColor
        self.isFirst = isFirst
        self.isLast = isLast
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)
                
                // 标题
                Text(title)
                    .font(.system(size: 17, weight: .regular))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            Rectangle()
                .fill(Color.clear)
        )
        .overlay(
            // 分割线 - 除了最后一个项目
            Group {
                if !isLast {
                    VStack {
                        Spacer()
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 0.5)
                            .padding(.leading, 60)
                    }
                }
            }
        )
    }
}

    // MARK: - 计划编辑视图 - 复用创建页面但移除时间选择组件
struct EAHabitEditView: View {
    let habit: EAHabit
    @Environment(\.dismiss) private var dismiss
    // ✅ 修复：使用Repository容器替代直接ModelContext访问
    @Environment(\.repositoryContainer) private var repositoryContainer
    @EnvironmentObject var sessionManager: EASessionManager
    
    @StateObject private var viewModel: EAHabitCreationViewModel
    
    init(habit: EAHabit) {
        self.habit = habit
        // ✅ 修复：使用临时SessionManager初始化，在onAppear中更新
        self._viewModel = StateObject(wrappedValue: EAHabitCreationViewModel(
            sessionManager: EASessionManager(),
            editingHabit: habit
        ))
    }
    
    var body: some View {
        ZStack {
            // 背景
            EABackgroundView(style: .authentication, showParticles: false)
                .ignoresSafeArea(.all)
            
            ScrollView {
                VStack(spacing: 24) {
                    // 标题栏
                    titleSection
                        .padding(.horizontal, 16)
                    
                    // 习惯名称
                    habitNameSection
                    
                    // 图标选择
                    iconSection
                    
                    // 频率选择
                    frequencySection
                    
                    // AI建议
                    if viewModel.showAISuggestion {
                        aiSuggestionSection
                    }
                    
                    // 保存按钮
                    saveButton
                        .padding(.horizontal, 16)
                    
                    // 底部间距
                    Spacer(minLength: 40)
                }
            }
            .alert("错误", isPresented: $viewModel.showError) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(viewModel.errorMessage)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // ✅ 修复：设置正确的sessionManager和repositoryContainer
            viewModel.updateSessionManager(sessionManager)
            if let repositoryContainer = repositoryContainer {
                viewModel.setRepositoryContainer(repositoryContainer)
            }
            viewModel.loadHabitData(habit)
        }
    }
    
    // MARK: - Title Section
    private var titleSection: some View {
        HStack {
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            
            Spacer()
            
            Text("编辑习惯")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            // 占位符保持对称
            Color.clear
                .frame(width: 44, height: 44)
        }
    }
    
    // MARK: - Habit Name Section
    private var habitNameSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("习惯名称")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            
            TextField("输入习惯名称", text: $viewModel.habitName)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                        )
                )
                .keyboardType(.default)
                .autocorrectionDisabled(false)
                .onChange(of: viewModel.habitName) { _, newValue in
                    if !newValue.isEmpty && newValue.count >= 2 {
                        viewModel.generateAISuggestion()
                    }
                }
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Icon Section
    private var iconSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            EAIconCategorySelector(selectedIcon: $viewModel.selectedIcon)
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Frequency Section
    private var frequencySection: some View {
        VStack {
            EAFrequencySelector(
                selectedFrequencyType: $viewModel.selectedFrequencyType,
                selectedWeekdays: $viewModel.selectedWeekdays,
                dailyTarget: $viewModel.dailyTarget,
                monthlyTarget: $viewModel.monthlyTarget,
                selectedMonthlyDates: $viewModel.selectedMonthlyDates,
                monthlyMode: $viewModel.monthlyMode
            )
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - AI Suggestion Section
    private var aiSuggestionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "sparkles")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.hexColor("FFD700"))
                
                Text("AI建议")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text(viewModel.aiSuggestion)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.white.opacity(0.9))
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.hexColor("FFD700").opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.hexColor("FFD700").opacity(0.3), lineWidth: 1)
                        )
                )
            
            Button(action: {
                viewModel.adoptAISuggestion(viewModel.aiSuggestion)
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 12, weight: .medium))
                    Text("采用建议")
                        .font(.system(size: 12, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.hexColor("FFD700"),
                                    Color.hexColor("FFA500")
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                )
            }
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Save Button
    private var saveButton: some View {
        Button(action: {
            Task {
                await updateHabit()
                dismiss()
            }
        }) {
            HStack(spacing: 8) {
                if viewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "checkmark")
                        .font(.system(size: 16, weight: .semibold))
                }
                
                Text(viewModel.isLoading ? "保存中..." : "保存修改")
                    .font(.system(size: 16, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.hexColor("33FFDD"),
                                Color.hexColor("00CED1")
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
        }
        .disabled(viewModel.habitName.isEmpty || viewModel.isLoading)
        .opacity(viewModel.habitName.isEmpty ? 0.6 : 1.0)
    }
    
    // MARK: - 更新习惯方法
    private func updateHabit() async {
        // 更新习惯属性
        habit.name = viewModel.habitName
        habit.iconName = viewModel.selectedIcon
        habit.frequencyType = viewModel.selectedFrequencyType.rawValue
        habit.selectedWeekdays = Array(viewModel.selectedWeekdays)
        habit.dailyTarget = viewModel.dailyTarget
        habit.monthlyTarget = viewModel.monthlyTarget
        habit.monthlyMode = viewModel.monthlyMode.rawValue
        habit.selectedMonthlyDates = Array(viewModel.selectedMonthlyDates)
        
        // 根据频率类型设置目标频率
        switch viewModel.selectedFrequencyType {
        case .daily:
            habit.targetFrequency = viewModel.dailyTarget
        case .weekly:
            habit.targetFrequency = viewModel.selectedWeekdays.count
        case .monthly:
            habit.targetFrequency = viewModel.monthlyTarget
        }
        
        // 保存到数据库
        do {
            // ✅ 修复：通过Repository保存习惯
            guard let repositoryContainer = repositoryContainer else {
                throw EARepositoryError.dataNotFound
            }
            
            try await repositoryContainer.habitRepository.saveHabit(habit)
            
            // 发送习惯编辑通知
            NotificationCenter.default.post(
                name: NSNotification.Name("HabitEdited"),
                object: habit.id
            )
            
            // 发送习惯数据变化通知
            NotificationCenter.default.post(
                name: NSNotification.Name("HabitDataChanged"),
                object: nil
            )
            
        } catch {
            // 静默处理更新习惯失败
        }
    }
}

#Preview("习惯功能菜单") {
    let sheetManager = EASheetManager()
    
    EAHabitActionMenu(habit: PreviewData.sampleHabit, onEdit: {}, onDelete: {})
        .modelContainer(PreviewData.container)
        .environment(\.sheetManager, sheetManager)
        .withSheetManager(sheetManager)
}