import SwiftUI

/// 🎨 Emoji图标渲染组件
/// 使用Emoji Unicode字符实现天然多彩效果，确保真实物品颜色
struct EAEmojiIcon: View {
    let emoji: String
    let size: CGFloat
    
    /// 初始化Emoji图标组件
    /// - Parameters:
    ///   - emoji: 要显示的emoji字符
    ///   - size: 图标显示尺寸，默认24pt
    init(_ emoji: String, size: CGFloat = EAAppConstants.IconSelector.iconDisplaySize) {
        self.emoji = emoji
        self.size = size
    }
    
    var body: some View {
        Text(emoji)
            .font(.system(size: size))
    }
}

/// 🎨 彩色图标分类选择组件 - Emoji版本
/// 使用Emoji Unicode字符实现天然多彩效果，确保与真实物品颜色一致
/// 
/// 🔑 核心特性：
/// - 8个分类，每个分类18个独特emoji
/// - 天然多彩，无需手动配色
/// - 三个页面显示一致（创建页面、今日页面、图鉴页面）
/// - 完全符合真实物品颜色（如⚽黑白足球、🌛黄色月亮、📚彩色书本）
struct EAIconCategorySelector: View {
    @Binding var selectedIcon: String
    @State private var selectedCategory: IconCategory = .common
    
    var body: some View {
        VStack(alignment: .leading, spacing: EAAppConstants.Dimensions.standardPadding) {
            // 滑动提示
            headerSection
            
            // 分类标签
            categoryTabs
            
            // 图标网格
            iconGrid
        }
        .padding(.horizontal, EAAppConstants.Dimensions.standardPadding)
    }
    
    // MARK: - 组件视图
    
    /// 标题和提示区域
    private var headerSection: some View {
        HStack {
            Text("选择图标")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            
            Spacer()
            
            Text("左右滑动查看更多分类")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
        }
    }
    
    /// 分类标签滚动视图
    private var categoryTabs: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: EAAppConstants.IconSelector.categoryButtonSpacing) {
                ForEach(IconCategory.allCases, id: \.self) { category in
                    categoryTab(category)
                }
            }
            .padding(.horizontal, EAAppConstants.Dimensions.standardPadding)
        }
        .padding(.horizontal, -EAAppConstants.Dimensions.standardPadding)
    }
    
    /// 图标网格布局
    private var iconGrid: some View {
        let columns = Array(repeating: GridItem(.flexible(), spacing: EAAppConstants.IconSelector.gridSpacing), count: EAAppConstants.IconSelector.gridColumns)
        
        return LazyVGrid(columns: columns, spacing: EAAppConstants.IconSelector.gridSpacing) {
            ForEach(selectedCategory.icons, id: \.self) { emoji in
                iconButton(emoji)
            }
        }
        .padding(.horizontal, EAAppConstants.Dimensions.standardPadding)
        .animation(.easeInOut(duration: EAAppConstants.IconSelector.categoryAnimationDuration), value: selectedCategory)
    }
    
    // MARK: - 私有方法
    
    /// 创建分类标签按钮
    /// - Parameter category: 图标分类
    /// - Returns: 分类标签按钮视图
    private func categoryTab(_ category: IconCategory) -> some View {
        Button(action: {
            selectedCategory = category
        }) {
            Text(category.displayName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(selectedCategory == category ? .black : .white.opacity(0.8))
                .padding(.horizontal, EAAppConstants.IconSelector.categoryButtonHorizontalPadding)
                .padding(.vertical, EAAppConstants.IconSelector.categoryButtonVerticalPadding)
                .background(
                    Capsule()
                        .fill(selectedCategory == category ? Color.white : Color.white.opacity(0.1))
                )
        }
        .animation(.easeInOut(duration: EAAppConstants.IconSelector.animationDuration), value: selectedCategory)
    }
    
    /// 创建图标按钮
    /// - Parameter emoji: emoji字符
    /// - Returns: 图标按钮视图
    private func iconButton(_ emoji: String) -> some View {
        Button(action: {
            selectedIcon = emoji
        }) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: EAAppConstants.Dimensions.standardCornerRadius)
                    .fill(selectedIcon == emoji ? Color.white.opacity(0.2) : Color.white.opacity(0.05))
                    .frame(width: EAAppConstants.IconSelector.iconButtonSize, height: EAAppConstants.IconSelector.iconButtonSize)
                
                // Emoji图标
                EAEmojiIcon(emoji, size: EAAppConstants.IconSelector.iconDisplaySize)
            }
        }
        .scaleEffect(selectedIcon == emoji ? EAAppConstants.IconSelector.selectedIconScale : 1.0)
        .animation(.easeInOut(duration: EAAppConstants.IconSelector.animationDuration), value: selectedIcon)
    }
}

// MARK: - 图标分类枚举

/// 图标分类枚举
/// 定义所有支持的图标分类类型
enum IconCategory: String, CaseIterable {
    case common = "常用"
    case sports = "运动"
    case study = "学习"
    case life = "生活"
    case work = "工作"
    case hobby = "爱好"
    case health = "健康"
    case social = "社交"
    
    /// 分类显示名称
    var displayName: String {
        return self.rawValue
    }
    
    /// 获取指定分类的所有图标
    /// 每个分类精确包含18个emoji图标，天然多彩，完全匹配分类主题
    var icons: [String] {
        switch self {
        case .common:
            return [
                "⭐", "🎯", "❤️", "🔥", "✅", "🔔", "📅", "🕐",
                "☀️", "🌛", "⚡", "✨", "🏆", "💎", "🌟", "💫",
                "🎉", "🎊"
            ]
            
        case .sports:
            return [
                "🏃", "🏀", "🎾", "⚽", "⚾", "🚴", "🏊", "🚶",
                "🏋️", "🤸", "🧘", "🏐", "🏈", "🏓", "🥊", "🥋",
                "🛹", "⛷️"
            ]
            
        case .study:
            return [
                "📚", "🧠", "🎓", "✏️", "📖", "💡", "🔍", "📊",
                "📁", "📝", "🧮", "🌍", "🔬", "🧪", "📐", "📏",
                "🖊️", "🖍️"
            ]
            
        case .life:
            return [
                "🏠", "🍽️", "🛏️", "🚿", "🚗", "✈️", "👜", "💳",
                "🛒", "🎁", "📞", "📺", "🍳", "☕", "🛍️", "🗝️",
                "🚪", "🪑"
            ]
            
        case .work:
            return [
                "💼", "💻", "📱", "📧", "📋", "💰", "👥", "📢",
                "🖨️", "📄", "📈", "🏢", "⌚", "🖥️", "📞", "📊",
                "💹", "🏦"
            ]
            
        case .hobby:
            return [
                "🎵", "🎨", "📷", "🎬", "🎮", "🎧", "🔊", "🎲",
                "🖼️", "✂️", "🔨", "🌱", "🎺", "🎸", "🎹", "🎤",
                "📸", "🧵"
            ]
            
        case .health:
            return [
                "❤️‍🩹", "💊", "🩺", "🌡️", "🩹", "💉", "👁️", "👂",
                "🫁", "💓", "🧬", "🦷", "🧴", "⚕️", "🏥", "🚑",
                "🧘‍♀️", "💆"
            ]
            
        case .social:
            return [
                "👫", "💬", "📹", "👤", "🎉", "🔊", "🔔", "👋",
                "💕", "💭", "👨‍👩‍👧‍👦", "🎪", "🍰", "🥂", "📣", "🤝",
                "💌", "🎭"
            ]
        }
    }
}

// MARK: - 兼容性组件

/// 🎨 统一图标渲染组件 - 兼容旧版本
/// 新版本使用Emoji，旧版本兼容SF Symbols
/// 
/// 此组件确保在"创建习惯页面"、"今日页面"、"图鉴页面"三个地方的图标显示完全一致
struct EAColorfulIcon: View {
    let iconName: String
    let size: CGFloat
    
    /// 初始化统一图标组件
    /// - Parameters:
    ///   - iconName: 图标名称（emoji或SF Symbol名称）
    ///   - size: 图标显示尺寸
    init(_ iconName: String, size: CGFloat = EAAppConstants.IconSelector.iconDisplaySize) {
        self.iconName = iconName
        self.size = size
    }
    
    var body: some View {
        // 如果是emoji（长度为1-4个字符且不包含"."），直接显示
        if iconName.count <= 4 && !iconName.contains(".") {
            EAEmojiIcon(iconName, size: size)
        } else {
            // 否则显示为默认的金色奖杯emoji
            EAEmojiIcon("🏆", size: size)
        }
    }
}

// MARK: - 预览

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        EAIconCategorySelector(selectedIcon: .constant("⭐"))
    }
} 