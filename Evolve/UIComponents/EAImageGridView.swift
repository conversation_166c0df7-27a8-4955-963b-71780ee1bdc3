import SwiftUI

/// 图片网格显示组件
/// 用于在帖子中显示多张图片，支持点击查看大图
/// 自动适配1-9张图片的最佳布局
@MainActor
struct EAImageGridView: View {
    
    // MARK: - 属性
    
    /// 图片路径数组
    let imagePaths: [String]
    
    /// 点击查看大图回调
    let onImageTap: (String, Int) -> Void
    
    /// 最大显示高度
    private let maxHeight: CGFloat = 300
    
    // MARK: - 初始化
    
    init(
        imagePaths: [String],
        onImageTap: @escaping (String, Int) -> Void = { _, _ in }
    ) {
        self.imagePaths = imagePaths
        self.onImageTap = onImageTap
    }
    
    // MARK: - 主视图
    
    var body: some View {
        if !imagePaths.isEmpty {
            imageGridContent
                .frame(maxHeight: maxHeight)
                .clipped()
        }
    }
    
    // MARK: - 子视图
    
    /// 图片网格内容
    @ViewBuilder
    private var imageGridContent: some View {
        switch imagePaths.count {
        case 1:
            singleImageView
        case 2:
            twoImagesView
        case 3:
            threeImagesView
        case 4:
            fourImagesView
        default:
            multipleImagesView
        }
    }
    
    /// 单张图片视图
    private var singleImageView: some View {
        imageView(at: 0)
            .aspectRatio(contentMode: .fit)
            .frame(maxHeight: maxHeight)
    }
    
    /// 两张图片视图
    private var twoImagesView: some View {
        HStack(spacing: 4) {
            imageView(at: 0)
            imageView(at: 1)
        }
        .frame(height: maxHeight * 0.6)
    }
    
    /// 三张图片视图
    private var threeImagesView: some View {
        HStack(spacing: 4) {
            // 左侧大图
            imageView(at: 0)
                .frame(width: imageGridSize * 1.5)
            
            // 右侧两个小图
            VStack(spacing: 4) {
                imageView(at: 1)
                imageView(at: 2)
            }
            .frame(width: imageGridSize)
        }
        .frame(height: maxHeight * 0.7)
    }
    
    /// 四张图片视图
    private var fourImagesView: some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                imageView(at: 0)
                imageView(at: 1)
            }
            HStack(spacing: 4) {
                imageView(at: 2)
                imageView(at: 3)
            }
        }
        .frame(height: maxHeight * 0.8)
    }
    
    /// 多张图片视图（5-9张）
    private var multipleImagesView: some View {
        VStack(spacing: 4) {
            // 第一行
            HStack(spacing: 4) {
                ForEach(0..<min(3, imagePaths.count), id: \.self) { index in
                    imageView(at: index)
                }
            }
            
            // 第二行
            if imagePaths.count > 3 {
                HStack(spacing: 4) {
                    ForEach(3..<min(6, imagePaths.count), id: \.self) { index in
                        imageView(at: index)
                    }
                    
                    // 如果有第7张及以上，显示"+N"覆盖层
                    if imagePaths.count > 6 {
                        ZStack {
                            imageView(at: 5)
                            
                            // 覆盖层显示剩余图片数量
                            Rectangle()
                                .fill(.black.opacity(0.5))
                                .overlay(
                                    Text("+\(imagePaths.count - 6)")
                                        .font(.system(size: 18, weight: .bold))
                                        .foregroundColor(.white)
                                )
                        }
                        .onTapGesture {
                            onImageTap(imagePaths[5], 5)
                        }
                    }
                }
            }
            
            // 第三行（如果有7-9张图片且不需要显示"+N"）
            if imagePaths.count >= 7 && imagePaths.count <= 9 {
                HStack(spacing: 4) {
                    ForEach(6..<imagePaths.count, id: \.self) { index in
                        imageView(at: index)
                    }
                    
                    // 填充空白位置
                    ForEach(imagePaths.count..<9, id: \.self) { _ in
                        Color.clear
                            .frame(width: imageGridSize, height: imageGridSize)
                    }
                }
            }
        }
        .frame(height: maxHeight)
    }
    
    /// 单个图片视图
    private func imageView(at index: Int) -> some View {
        Group {
            if index < imagePaths.count {
                AsyncImage(url: URL(fileURLWithPath: getFullImagePath(imagePaths[index]))) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(.gray.opacity(0.2))
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.6)
                                .tint(.gray)
                        )
                }
                .frame(width: imageGridSize, height: imageGridSize)
                .clipped()
                .cornerRadius(8)
                .onTapGesture {
                    onImageTap(imagePaths[index], index)
                }
            } else {
                Rectangle()
                    .fill(.clear)
                    .frame(width: imageGridSize, height: imageGridSize)
            }
        }
    }
    
    // MARK: - 计算属性
    
    /// 网格图片大小
    private var imageGridSize: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = 32 // 左右边距
        let spacing: CGFloat = 8 // 图片间距
        return (screenWidth - padding - spacing) / 3
    }
    
    // MARK: - 辅助方法
    
    /// 获取图片完整路径
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }
}

// MARK: - 预览

struct EAImageGridView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 单张图片
    VStack {
        EAImageGridView(
            imagePaths: ["Images/sample1.jpg"]
        )
        .padding()
        
        Spacer()
    }
    .background(Color.hexColor("002b20"))
            .previewDisplayName("单张图片")

            // 多张图片
    VStack {
        EAImageGridView(
            imagePaths: [
                "Images/sample1.jpg",
                "Images/sample2.jpg",
                "Images/sample3.jpg",
                "Images/sample4.jpg",
                "Images/sample5.jpg"
            ]
        )
        .padding()
        
        Spacer()
    }
    .background(Color.hexColor("002b20"))
            .previewDisplayName("多张图片")
        }
    }
} 