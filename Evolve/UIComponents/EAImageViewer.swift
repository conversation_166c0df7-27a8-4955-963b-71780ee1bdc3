import SwiftUI

/// 图片查看器组件
/// 支持全屏查看、缩放、滑动浏览多张图片
/// 用于帖子详情页和社区图片大图查看
@MainActor
struct EAImageViewer: View {
    
    // MARK: - 属性
    
    /// 图片路径数组
    let imagePaths: [String]
    
    /// 初始显示的图片索引
    let initialIndex: Int
    
    /// 关闭回调
    let onDismiss: () -> Void
    
    // MARK: - 状态属性
    
    /// 当前图片索引
    @State private var currentIndex: Int
    
    /// 缩放比例
    @State private var scale: CGFloat = 1.0
    
    /// 拖拽偏移
    @State private var offset: CGSize = .zero
    
    /// 是否显示UI控件
    @State private var showUI = true
    
    /// UI隐藏定时器
    @State private var hideUITimer: Timer?
    
    // MARK: - 初始化
    
    init(
        imagePaths: [String],
        initialIndex: Int = 0,
        onDismiss: @escaping () -> Void
    ) {
        self.imagePaths = imagePaths
        self.initialIndex = initialIndex
        self.onDismiss = onDismiss
        self._currentIndex = State(initialValue: initialIndex)
    }
    
    // MARK: - 主视图
    
    var body: some View {
        ZStack {
            // 背景
            Color.black
                .ignoresSafeArea()
            
            // 图片内容
            TabView(selection: $currentIndex) {
                ForEach(Array(imagePaths.enumerated()), id: \.offset) { index, imagePath in
                    imageContentView(imagePath: imagePath)
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .onTapGesture {
                toggleUI()
            }
            
            // 顶部控制栏
            VStack {
                topControlBar
                    .opacity(showUI ? 1 : 0)
                    .animation(.easeInOut(duration: 0.3), value: showUI)
                
                Spacer()
            }
            
            // 底部控制栏
            VStack {
                Spacer()
                
                bottomControlBar
                    .opacity(showUI ? 1 : 0)
                    .animation(.easeInOut(duration: 0.3), value: showUI)
            }
        }
        .onAppear {
            startHideUITimer()
        }
        .onDisappear {
            hideUITimer?.invalidate()
        }
        .preferredColorScheme(.dark)
        .statusBarHidden(!showUI)
    }
    
    // MARK: - 子视图
    
    /// 图片内容视图
    private func imageContentView(imagePath: String) -> some View {
        GeometryReader { geometry in
            AsyncImage(url: URL(fileURLWithPath: getFullImagePath(imagePath))) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(scale)
                    .offset(offset)
                    .gesture(
                        SimultaneousGesture(
                            // 缩放手势
                            MagnificationGesture()
                                .onChanged { value in
                                    scale = max(1.0, min(value, 5.0))
                                    restartHideUITimer()
                                }
                                .onEnded { _ in
                                    if scale < 1.2 {
                                        withAnimation(.spring()) {
                                            scale = 1.0
                                            offset = .zero
                                        }
                                    }
                                },
                            
                            // 拖拽手势
                            DragGesture()
                                .onChanged { value in
                                    if scale > 1.0 {
                                        offset = value.translation
                                    } else {
                                        // 垂直拖拽关闭
                                        if abs(value.translation.height) > abs(value.translation.width) {
                                                                                          offset = CGSize(width: 0, height: value.translation.height)
                                        }
                                    }
                                    restartHideUITimer()
                                }
                                .onEnded { value in
                                    if scale > 1.0 {
                                        // 缩放状态下限制拖拽范围
                                        let maxOffsetX = (geometry.size.width * scale - geometry.size.width) / 2
                                        let maxOffsetY = (geometry.size.height * scale - geometry.size.height) / 2
                                        
                                        withAnimation(.spring()) {
                                            offset = CGSize(
                                                width: max(-maxOffsetX, min(maxOffsetX, offset.width)),
                                                height: offset.height
                                            )
                                                                                        offset = CGSize(
                                                width: offset.width,
                                                height: max(-maxOffsetY, min(maxOffsetY, offset.height))
                                            )
                                        }
                                    } else {
                                        // 垂直拖拽关闭检测
                                        if abs(value.translation.height) > 150 {
                                            onDismiss()
                                        } else {
                                            withAnimation(.spring()) {
                                                offset = .zero
                                            }
                                        }
                                    }
                                }
                        )
                    )
                    .onTapGesture(count: 2) {
                        // 双击缩放
                        withAnimation(.spring()) {
                            if scale > 1.0 {
                                scale = 1.0
                                offset = .zero
                            } else {
                                scale = 2.0
                            }
                        }
                        restartHideUITimer()
                    }
            } placeholder: {
                Rectangle()
                    .fill(.gray.opacity(0.2))
                    .overlay(
                        ProgressView()
                            .scaleEffect(1.5)
                            .tint(.white)
                    )
            }
        }
    }
    
    /// 顶部控制栏
    private var topControlBar: some View {
        HStack {
            // 关闭按钮
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(.black.opacity(0.3), in: Circle())
            }
            
            Spacer()
            
            // 图片计数
            if imagePaths.count > 1 {
                Text("\(currentIndex + 1) / \(imagePaths.count)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.black.opacity(0.3), in: Capsule())
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    /// 底部控制栏
    private var bottomControlBar: some View {
        VStack(spacing: 16) {
            // 缩略图导航（仅在多张图片时显示）
            if imagePaths.count > 1 {
                thumbnailNavigation
            }
            
            // 操作按钮
            actionButtons
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 30)
    }
    
    /// 缩略图导航
    private var thumbnailNavigation: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(Array(imagePaths.enumerated()), id: \.offset) { index, imagePath in
                    AsyncImage(url: URL(fileURLWithPath: getFullImagePath(imagePath))) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(.gray.opacity(0.3))
                    }
                    .frame(width: 50, height: 50)
                    .clipped()
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(
                                currentIndex == index ? Color.accentColor : Color.clear,
                                lineWidth: 2
                            )
                    )
                    .onTapGesture {
                        currentIndex = index
                        restartHideUITimer()
                    }
                }
            }
            .padding(.horizontal, 20)
        }
    }
    
    /// 操作按钮
    private var actionButtons: some View {
        HStack(spacing: 24) {
            // 保存到相册按钮
            Button(action: {
                saveCurrentImageToPhotos()
                restartHideUITimer()
            }) {
                Image(systemName: "square.and.arrow.down")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(.black.opacity(0.3), in: Circle())
            }
            
            Spacer()
            
            // 分享按钮
            Button(action: {
                shareCurrentImage()
                restartHideUITimer()
            }) {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(.black.opacity(0.3), in: Circle())
            }
        }
    }
    
    // MARK: - 方法
    
    /// 切换UI显示状态
    private func toggleUI() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showUI.toggle()
        }
        
        if showUI {
            startHideUITimer()
        } else {
            hideUITimer?.invalidate()
        }
    }
    
    /// 开始UI隐藏定时器
    private func startHideUITimer() {
        hideUITimer?.invalidate()
        
        // 创建新的定时器（避免capture问题）
        let timer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            Task { @MainActor in
                withAnimation(.easeInOut(duration: 0.3)) {
                    showUI = false
                }
            }
        }
        hideUITimer = timer
    }
    
    /// 重新开始UI隐藏定时器
    private func restartHideUITimer() {
        if showUI {
            startHideUITimer()
        }
    }
    
    /// 保存当前图片到相册
    private func saveCurrentImageToPhotos() {
        guard currentIndex < imagePaths.count else { return }
        
        let imagePath = imagePaths[currentIndex]
        let fullPath = getFullImagePath(imagePath)
        
        if let image = UIImage(contentsOfFile: fullPath) {
            UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
            // TODO: 添加保存成功提示
        }
    }
    
    /// 分享当前图片
    private func shareCurrentImage() {
        guard currentIndex < imagePaths.count else { return }
        
        let imagePath = imagePaths[currentIndex]
        let fullPath = getFullImagePath(imagePath)
        
        if let image = UIImage(contentsOfFile: fullPath) {
            let activityVC = UIActivityViewController(
                activityItems: [image],
                applicationActivities: nil
            )
            
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootVC = windowScene.windows.first?.rootViewController {
                rootVC.present(activityVC, animated: true)
            }
        }
    }
    
    /// 获取图片完整路径
    private func getFullImagePath(_ relativePath: String) -> String {
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        return (documentsPath as NSString).appendingPathComponent(relativePath)
    }
}

// MARK: - 预览

#Preview("图片查看器") {
    EAImageViewer(
        imagePaths: [
            "Images/sample1.jpg",
            "Images/sample2.jpg",
            "Images/sample3.jpg"
        ],
        initialIndex: 0
    ) {
        // 关闭回调
    }
} 