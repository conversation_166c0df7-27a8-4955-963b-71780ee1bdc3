import SwiftUI

// MARK: - 多时间提醒选择器组件
struct EAMultiTimeReminderSelector: View {
    @Binding var reminderTimes: [Date]
    @Binding var isReminderEnabled: Bool
    @Binding var isPushNotificationEnabled: Bool
    
    let maxTotalReminders: Int = 40 // 全局提醒上限
    let currentTotalReminders: Int // 当前所有习惯的提醒总数
    let onReminderCountChanged: () -> Void // 提醒数量变化回调
    
    @State private var showingTimePicker = false
    @State private var newReminderTime = Date()
    @State private var showingMaxLimitAlert = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 全局提醒统计区域
            globalReminderStatsSection
            
            // 提醒开关和标题
            reminderToggleHeader
            
            // 多时间管理区域（提醒开启时显示）
            if isReminderEnabled {
                multiTimeManagementSection
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
                
                // 推送通知开关
                pushNotificationToggle
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.3), value: isReminderEnabled)
        .alert("提醒数量已达上限", isPresented: $showingMaxLimitAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text("所有习惯的提醒总数不能超过\(maxTotalReminders)个，请先删除其他提醒或减少其他习惯的提醒时间。")
        }
        .sheet(isPresented: $showingTimePicker) {
            timePickerSheet
        }
    }
    
    // MARK: - 全局提醒统计区域
    private var globalReminderStatsSection: some View {
        HStack {
            // 图标
            Image(systemName: "bell.badge.fill")
                .font(.system(size: 18))
                .foregroundColor(Color.hexColor("FF7F50"))
            
            VStack(alignment: .leading, spacing: 2) {
                Text("全局提醒统计")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                
                Text("所有习惯的提醒总数")
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(Color.white.opacity(0.6))
            }
            
            Spacer()
            
            // 统计数字 - 使用0/40格式
            Text("\(currentTotalReminders)/\(maxTotalReminders)")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(currentTotalReminders >= maxTotalReminders ? Color.red : Color.hexColor("40E0D0"))
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            currentTotalReminders >= maxTotalReminders ? 
                            Color.red.opacity(0.3) : Color.hexColor("40E0D0").opacity(0.3), 
                            lineWidth: 1
                        )
                )
        )
    }
    
    // MARK: - 提醒开关和标题
    private var reminderToggleHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("启用提醒")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                if isReminderEnabled {
                    Text("已设置 \(reminderTimes.count) 个提醒时间")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                } else {
                    Text("关闭所有提醒")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
            }
            
            Spacer()
            
            Toggle("", isOn: $isReminderEnabled)
                .toggleStyle(SwitchToggleStyle(tint: Color.hexColor("40E0D0")))
                .onChange(of: isReminderEnabled) { _, newValue in
                    if !newValue {
                        // 关闭提醒时清空所有时间
                        reminderTimes.removeAll()
                        onReminderCountChanged()
                    }
                }
        }
    }
    
    // MARK: - 多时间管理区域
    private var multiTimeManagementSection: some View {
        VStack(spacing: 16) {
            // 标题和添加按钮
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "clock.fill")
                        .foregroundColor(Color.hexColor("40E0D0"))
                        .font(.system(size: 14))
                    
                    Text("提醒时间")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // 添加按钮
                Button(action: {
                    // 检查是否达到上限
                    if currentTotalReminders >= maxTotalReminders {
                        showingMaxLimitAlert = true
                        return
                    }
                    
                    // 设置默认时间（避免重复）
                    let calendar = Calendar.current
                    var newTime = Date()
                    
                    // 如果已有提醒时间，设置为最后一个时间的1小时后
                    if let lastTime = reminderTimes.last {
                        newTime = calendar.date(byAdding: .hour, value: 1, to: lastTime) ?? Date()
                    }
                    
                    newReminderTime = newTime
                    showingTimePicker = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 14))
                        Text("添加")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(canAddMoreReminders ? Color.hexColor("40E0D0") : Color.white.opacity(0.3))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(canAddMoreReminders ? Color.hexColor("40E0D0").opacity(0.5) : Color.white.opacity(0.2), lineWidth: 1)
                    )
                }
                .disabled(!canAddMoreReminders)
            }
            
            // 现有提醒时间列表或空状态
            if reminderTimes.isEmpty {
                // 空状态
                VStack(spacing: 8) {
                    Image(systemName: "clock.badge.plus")
                        .font(.system(size: 24))
                        .foregroundColor(Color.white.opacity(0.3))
                    
                    Text("还没有设置提醒时间")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.5))
                }
                .frame(height: 60)
            } else {
                existingRemindersSection
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 现有提醒时间列表
    private var existingRemindersSection: some View {
        VStack(spacing: 8) {
            ForEach(Array(reminderTimes.enumerated()), id: \.offset) { index, time in
                reminderTimeRow(time: time, index: index)
            }
        }
    }
    
    // MARK: - 单个提醒时间行
    private func reminderTimeRow(time: Date, index: Int) -> some View {
        HStack {
            // 时间显示
            Text(timeFormatter.string(from: time))
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            // 删除按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    reminderTimes.remove(at: index)
                    onReminderCountChanged()
                }
            }) {
                Image(systemName: "minus.circle.fill")
                    .foregroundColor(.red.opacity(0.8))
                    .font(.system(size: 18))
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.white.opacity(0.1))
        )
    }
    
    // MARK: - 推送通知开关
    private var pushNotificationToggle: some View {
        VStack(spacing: 12) {
            // 分割线
            Rectangle()
                .fill(Color.white.opacity(0.1))
                .frame(height: 1)
            
            HStack {
                HStack(spacing: 12) {
                    Image(systemName: "app.badge")
                        .font(.system(size: 16))
                        .foregroundColor(Color.hexColor("FF7F50"))
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("系统推送")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                        
                        Text(isPushNotificationEnabled ? "即使应用未打开也能收到提醒" : "关闭后将使用AI应用内提醒")
                            .font(.system(size: 11, weight: .regular))
                            .foregroundColor(Color.white.opacity(0.6))
                    }
                }
                
                Spacer()
                
                Toggle("", isOn: $isPushNotificationEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: Color.hexColor("FF7F50")))
            }
        }
    }
    
    // MARK: - 时间选择器弹窗
    private var timePickerSheet: some View {
        NavigationView {
            ZStack {
                // 使用与项目一致的背景设计
                EABackgroundView(style: .authentication, showParticles: false)
                    .ignoresSafeArea(.all)
                
                VStack(spacing: 0) {
                    // 标题区域
                    VStack(spacing: 8) {
                        Text("选择提醒时间")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                        
                        Text("设置您希望收到提醒的时间")
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(Color.white.opacity(0.7))
                    }
                    .padding(.top, 30)
                    .padding(.bottom, 20)
                    
                    // 时间选择器容器
                    DatePicker(
                        "选择时间",
                        selection: $newReminderTime,
                        displayedComponents: .hourAndMinute
                    )
                    .datePickerStyle(WheelDatePickerStyle())
                    .labelsHidden()
                    .colorScheme(.dark) // 确保使用深色主题
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
                    .padding(.horizontal, 20)
                    
                    Spacer()
                    
                    // 底部按钮区域
                    HStack(spacing: 16) {
                        // 取消按钮
                        Button(action: {
                            showingTimePicker = false
                        }) {
                            Text("取消")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.white.opacity(0.8))
                                .frame(maxWidth: .infinity)
                                .frame(height: 48)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.white.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                        )
                                )
                        }
                        
                        // 确定按钮
                        Button(action: {
                            addReminderTime()
                            showingTimePicker = false
                        }) {
                            Text("确定")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 48)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(
                                            LinearGradient(
                                                colors: [
                                                    Color.hexColor("40E0D0"),
                                                    Color.hexColor("40E0D0").opacity(0.8)
                                                ],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                }
            }
            .navigationBarHidden(true)
        }
        .presentationDetents([.height(350)])
        .presentationDragIndicator(.visible)
    }
    
    // MARK: - 计算属性
    private var canAddMoreReminders: Bool {
        currentTotalReminders < maxTotalReminders
    }
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }
    
    // MARK: - 方法
    private func addReminderTime() {
        // 检查时间是否已存在
        let timeString = timeFormatter.string(from: newReminderTime)
        let existingTimeStrings = reminderTimes.map { timeFormatter.string(from: $0) }
        
        if !existingTimeStrings.contains(timeString) {
            reminderTimes.append(newReminderTime)
            reminderTimes.sort()
            onReminderCountChanged()
        }
    }
}

#Preview("多时间提醒选择器") {
    struct PreviewWrapper: View {
        @State private var reminderTimes: [Date] = [
            Calendar.current.date(bySettingHour: 8, minute: 0, second: 0, of: Date()) ?? Date(),
            Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
        ]
        @State private var isReminderEnabled = true
        @State private var isPushNotificationEnabled = true
        
        var body: some View {
            ZStack {
                Color.black.ignoresSafeArea()
                
                EAMultiTimeReminderSelector(
                    reminderTimes: $reminderTimes,
                    isReminderEnabled: $isReminderEnabled,
                    isPushNotificationEnabled: $isPushNotificationEnabled,
                    currentTotalReminders: 15,
                    onReminderCountChanged: {
                        // 提醒数量变化回调
                    }
                )
                .padding()
            }
        }
    }
    
    return PreviewWrapper()
} 