import SwiftUI
import SwiftData

// MARK: - 星际成就时间轴组件（Phase 3 Day 7新增）

/// 星际成就时间轴：展示用户的习惯成就、等级提升、徽章获得等里程碑事件
struct EAStellarAchievementTimeline: View {
    
    // MARK: - 属性
    
    let userId: UUID
    let filter: TimelineFilter
    @ObservedObject private var energyService: EAStellarEnergyService
    
    @State private var timelineEvents: [TimelineEvent] = []
    @State private var isLoading = false
    @State private var selectedEvent: TimelineEvent?
    @State private var showEventDetail = false
    
    // MARK: - 初始化
    
    init(userId: UUID, filter: TimelineFilter, energyService: EAStellarEnergyService) {
        self.userId = userId
        self.filter = filter
        self.energyService = energyService
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(spacing: 0) {
            if isLoading {
                loadingView
            } else if timelineEvents.isEmpty {
                emptyStateView
            } else {
                timelineContentView
            }
        }
        .task {
            await loadTimelineEvents()
        }
        .onChange(of: filter) { _, newFilter in
            Task {
                await loadTimelineEvents()
            }
        }
        .sheet(isPresented: $showEventDetail) {
            if let event = selectedEvent {
                TimelineEventDetailView(event: event)
                    .presentationDetents([.medium, .large])
            }
        }
    }
    
    // MARK: - 加载视图
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .controlSize(.large)
                .tint(.blue)
            
            Text("加载星际成就记录...")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .background(cosmicCardBackground)
    }
    
    // MARK: - 空状态视图
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "timeline.selection")
                .font(.system(size: 48))
                .foregroundStyle(.blue.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("暂无成就记录")
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                Text("完成第一个习惯开始你的星际探索之旅")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .background(cosmicCardBackground)
    }
    
    // MARK: - 时间轴内容视图
    
    private var timelineContentView: some View {
        LazyVStack(spacing: 0) {
            ForEach(Array(timelineEvents.enumerated()), id: \.element.id) { index, event in
                TimelineEventRow(
                    event: event,
                    isFirst: index == 0,
                    isLast: index == timelineEvents.count - 1,
                    onTap: {
                        selectedEvent = event
                        showEventDetail = true
                    }
                )
            }
        }
        .padding(.vertical, 16)
        .background(cosmicCardBackground)
    }
    
    // MARK: - 宇宙卡片背景
    
    private var cosmicCardBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [.blue.opacity(0.5), .purple.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - 数据加载
    
    private func loadTimelineEvents() async {
        isLoading = true
        defer { isLoading = false }
        
        // 模拟加载时间轴事件数据
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
        var events: [TimelineEvent] = []
        
        // 根据filter类型加载不同的事件
        switch filter {
        case .all:
            events = await loadAllEvents()
        case .habits:
            events = await loadHabitEvents()
        case .levels:
            events = await loadLevelEvents()
        case .badges:
            events = await loadBadgeEvents()
        case .challenges:
            events = await loadChallengeEvents()
        }
        
        // 按时间倒序排序
        timelineEvents = events.sorted { $0.timestamp > $1.timestamp }
    }
    
    private func loadAllEvents() async -> [TimelineEvent] {
        // 模拟综合事件数据
        return [
            TimelineEvent(
                id: UUID(),
                type: .levelUp,
                title: "等级提升",
                description: "恭喜！您已升级为星际旅者 LV.4",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 2), // 2天前
                energyGained: 100,
                details: "通过持续的习惯完成，您的星际能量已达到1000点，成功晋升为星际旅者！",
                relatedData: ["previousLevel": 3, "newLevel": 4]
            ),
            TimelineEvent(
                id: UUID(),
                type: .badgeEarned,
                title: "获得徽章",
                description: "🔥 连击新手 - 连续完成习惯7天",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 5), // 5天前
                energyGained: 50,
                details: "连续7天完成习惯目标，展现了出色的坚持能力！",
                relatedData: ["badgeType": "weekStreak", "streakDays": 7]
            ),
            TimelineEvent(
                id: UUID(),
                type: .habitMilestone,
                title: "习惯里程碑",
                description: "⭐ 早起习惯已完成30次",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 7), // 7天前
                energyGained: 30,
                details: "早起习惯已累计完成30次，您的作息正在变得更加规律！",
                relatedData: ["habitName": "早起", "completionCount": 30]
            ),
            TimelineEvent(
                id: UUID(),
                type: .habitCreated,
                title: "创建新习惯",
                description: "📚 创建了新习惯：每日阅读",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 10), // 10天前
                energyGained: 20,
                details: "开始培养阅读习惯，为自己的成长投资！",
                relatedData: ["habitName": "每日阅读", "frequency": "daily"]
            ),
            TimelineEvent(
                id: UUID(),
                type: .communityShare,
                title: "社区分享",
                description: "💫 分享了习惯完成心得",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 12), // 12天前
                energyGained: 25,
                details: "与其他探索者分享了您的习惯养成经验，激励了更多人！",
                relatedData: ["shareType": "experience", "likes": 5]
            )
        ]
    }
    
    private func loadHabitEvents() async -> [TimelineEvent] {
        // 模拟习惯相关事件
        return [
            TimelineEvent(
                id: UUID(),
                type: .habitMilestone,
                title: "习惯里程碑",
                description: "⭐ 早起习惯已完成30次",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 7),
                energyGained: 30,
                details: "早起习惯已累计完成30次，您的作息正在变得更加规律！",
                relatedData: ["habitName": "早起", "completionCount": 30]
            ),
            TimelineEvent(
                id: UUID(),
                type: .habitCreated,
                title: "创建新习惯",
                description: "📚 创建了新习惯：每日阅读",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 10),
                energyGained: 20,
                details: "开始培养阅读习惯，为自己的成长投资！",
                relatedData: ["habitName": "每日阅读", "frequency": "daily"]
            )
        ]
    }
    
    private func loadLevelEvents() async -> [TimelineEvent] {
        // 模拟等级提升事件
        return [
            TimelineEvent(
                id: UUID(),
                type: .levelUp,
                title: "等级提升",
                description: "恭喜！您已升级为星际旅者 LV.4",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 2),
                energyGained: 100,
                details: "通过持续的习惯完成，您的星际能量已达到1000点，成功晋升为星际旅者！",
                relatedData: ["previousLevel": 3, "newLevel": 4]
            )
        ]
    }
    
    private func loadBadgeEvents() async -> [TimelineEvent] {
        // 模拟徽章获得事件
        return [
            TimelineEvent(
                id: UUID(),
                type: .badgeEarned,
                title: "获得徽章",
                description: "🔥 连击新手 - 连续完成习惯7天",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 5),
                energyGained: 50,
                details: "连续7天完成习惯目标，展现了出色的坚持能力！",
                relatedData: ["badgeType": "weekStreak", "streakDays": 7]
            )
        ]
    }
    
    private func loadChallengeEvents() async -> [TimelineEvent] {
        // 模拟挑战完成事件
        return [
            TimelineEvent(
                id: UUID(),
                type: .challengeCompleted,
                title: "挑战完成",
                description: "🏆 完成了30天早起挑战",
                timestamp: Date().addingTimeInterval(-3600 * 24 * 15),
                energyGained: 200,
                details: "成功完成30天早起挑战，展现了卓越的自律能力！",
                relatedData: ["challengeName": "30天早起挑战", "duration": 30]
            )
        ]
    }
}

// MARK: - 时间轴事件行组件

struct TimelineEventRow: View {
    let event: TimelineEvent
    let isFirst: Bool
    let isLast: Bool
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            // 时间轴左侧线条和节点
            VStack(spacing: 0) {
                // 上方连接线
                if !isFirst {
                    Rectangle()
                        .fill(Color.blue.opacity(0.3))
                        .frame(width: 2, height: 20)
                }
                
                // 事件节点
                ZStack {
                    Circle()
                        .fill(event.type.color)
                        .frame(width: 16, height: 16)
                    
                    Circle()
                        .stroke(Color.white, lineWidth: 2)
                        .frame(width: 16, height: 16)
                }
                
                // 下方连接线
                if !isLast {
                    Rectangle()
                        .fill(Color.blue.opacity(0.3))
                        .frame(width: 2, height: 20)
                }
            }
            
            // 事件内容
            Button(action: onTap) {
                VStack(spacing: 0) {
                    HStack(spacing: 12) {
                        // 事件图标
                        ZStack {
                            Circle()
                                .fill(event.type.color.opacity(0.2))
                                .frame(width: 40, height: 40)
                            
                            Image(systemName: event.type.icon)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundStyle(event.type.color)
                        }
                        
                        // 事件信息
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text(event.title)
                                    .font(.subheadline.weight(.semibold))
                                    .foregroundStyle(.primary)
                                
                                Spacer()
                                
                                if event.energyGained > 0 {
                                    HStack(spacing: 4) {
                                        Image(systemName: "bolt.fill")
                                            .font(.caption2)
                                            .foregroundStyle(.yellow)
                                        
                                        Text("+\(event.energyGained)")
                                            .font(.caption.weight(.medium))
                                            .foregroundStyle(.yellow)
                                    }
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(
                                        Capsule()
                                            .fill(.yellow.opacity(0.2))
                                    )
                                }
                            }
                            
                            Text(event.description)
                                .font(.caption)
                                .foregroundStyle(.secondary)
                                .lineLimit(2)
                            
                            Text(formatEventTime(event.timestamp))
                                .font(.caption2)
                                .foregroundStyle(.tertiary)
                        }
                        
                        // 详情箭头
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundStyle(.tertiary)
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.regularMaterial)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(event.type.color.opacity(0.3), lineWidth: 1)
                            )
                    )
                    
                    // 间距
                    if !isLast {
                        Spacer()
                            .frame(height: 16)
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
    }
    
    private func formatEventTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - 时间轴事件详情视图

struct TimelineEventDetailView: View {
    let event: TimelineEvent
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 事件头部
                    eventHeader
                    
                    // 事件详情
                    eventDetails
                    
                    // 相关数据
                    if !event.relatedData.isEmpty {
                        relatedDataSection
                    }
                    
                    // 操作按钮
                    actionButtons
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("成就详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var eventHeader: some View {
        VStack(spacing: 16) {
            // 事件图标
            ZStack {
                Circle()
                    .fill(event.type.color.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: event.type.icon)
                    .font(.system(size: 32, weight: .medium))
                    .foregroundStyle(event.type.color)
            }
            
            // 事件标题和描述
            VStack(spacing: 8) {
                Text(event.title)
                    .font(.title2.bold())
                    .foregroundStyle(.primary)
                    .multilineTextAlignment(.center)
                
                Text(event.description)
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 时间和能量
            HStack(spacing: 20) {
                VStack(spacing: 4) {
                    Text("时间")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Text(formatFullDate(event.timestamp))
                        .font(.caption.weight(.medium))
                        .foregroundStyle(.primary)
                }
                
                if event.energyGained > 0 {
                    VStack(spacing: 4) {
                        Text("获得能量")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        HStack(spacing: 4) {
                            Image(systemName: "bolt.fill")
                                .font(.caption)
                                .foregroundStyle(.yellow)
                            
                            Text("\(event.energyGained)")
                                .font(.caption.weight(.medium))
                                .foregroundStyle(.yellow)
                        }
                    }
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(event.type.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var eventDetails: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("详细描述")
                .font(.headline)
                .foregroundStyle(.primary)
            
            Text(event.details)
                .font(.body)
                .foregroundStyle(.secondary)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
    
    private var relatedDataSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("相关信息")
                .font(.headline)
                .foregroundStyle(.primary)
            
            VStack(spacing: 8) {
                ForEach(Array(event.relatedData.keys.sorted()), id: \.self) { key in
                    HStack {
                        Text(formatDataKey(key))
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                        
                        Text("\(event.relatedData[key] ?? "")")
                            .font(.subheadline.weight(.medium))
                            .foregroundStyle(.primary)
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            Button(action: {
                shareEvent()
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text("分享成就")
                        .font(.subheadline.weight(.medium))
                }
                .foregroundStyle(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.blue)
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            Button(action: {
                viewSimilarEvents()
            }) {
                HStack {
                    Image(systemName: "clock.arrow.circlepath")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text("查看类似成就")
                        .font(.subheadline.weight(.medium))
                }
                .foregroundStyle(.blue)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.blue.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(.blue.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    private func formatFullDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
    
    private func formatDataKey(_ key: String) -> String {
        switch key {
        case "previousLevel": return "原等级"
        case "newLevel": return "新等级"
        case "badgeType": return "徽章类型"
        case "streakDays": return "连击天数"
        case "habitName": return "习惯名称"
        case "completionCount": return "完成次数"
        case "frequency": return "频率"
        case "shareType": return "分享类型"
        case "likes": return "获赞数"
        case "challengeName": return "挑战名称"
        case "duration": return "持续天数"
        default: return key
        }
    }
    
    private func shareEvent() {
        // TODO: 实现分享功能
    }
    
    private func viewSimilarEvents() {
        // TODO: 实现查看类似成就功能
    }
}

// MARK: - 时间轴事件模型

struct TimelineEvent: Identifiable {
    let id: UUID
    let type: TimelineEventType
    let title: String
    let description: String
    let timestamp: Date
    let energyGained: Int
    let details: String
    let relatedData: [String: Any]
    
    init(id: UUID, type: TimelineEventType, title: String, description: String, timestamp: Date, energyGained: Int, details: String, relatedData: [String: Any] = [:]) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.timestamp = timestamp
        self.energyGained = energyGained
        self.details = details
        self.relatedData = relatedData
    }
}

enum TimelineEventType: String, CaseIterable {
    case habitCreated = "habitCreated"
    case habitCompleted = "habitCompleted"
    case habitMilestone = "habitMilestone"
    case levelUp = "levelUp"
    case badgeEarned = "badgeEarned"
    case challengeCompleted = "challengeCompleted"
    case communityShare = "communityShare"
    case achievementUnlocked = "achievementUnlocked"
    
    var icon: String {
        switch self {
        case .habitCreated: return "plus.circle.fill"
        case .habitCompleted: return "checkmark.circle.fill"
        case .habitMilestone: return "star.circle.fill"
        case .levelUp: return "arrow.up.circle.fill"
        case .badgeEarned: return "trophy.circle.fill"
        case .challengeCompleted: return "flag.circle.fill"
        case .communityShare: return "square.and.arrow.up.circle.fill"
        case .achievementUnlocked: return "crown.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .habitCreated: return .green
        case .habitCompleted: return .blue
        case .habitMilestone: return .yellow
        case .levelUp: return .purple
        case .badgeEarned: return .orange
        case .challengeCompleted: return .red
        case .communityShare: return .cyan
        case .achievementUnlocked: return .pink
        }
    }
}

// MARK: - SwiftUI预览

#Preview("时间轴") {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: 
        EAUser.self,
        EAUserSettings.self,
        EAHabit.self,
        EACompletion.self,
        configurations: config
    )
    
    let sampleContainer = EARepositoryContainerImpl(modelContainer: container)
    let sampleEnergyService = EAStellarEnergyService(
        repositoryContainer: sampleContainer,
        cacheManager: EAAICacheManager()
    )
    
    NavigationView {
        EAStellarAchievementTimeline(
            userId: UUID(),
            filter: .all,
            energyService: sampleEnergyService
        )
    }
}

#Preview("事件详情") {
    let sampleEvent = TimelineEvent(
        id: UUID(),
        type: .levelUp,
        title: "等级提升",
        description: "恭喜！您已升级为星际旅者 LV.4",
        timestamp: Date(),
        energyGained: 100,
        details: "通过持续的习惯完成，您的星际能量已达到1000点，成功晋升为星际旅者！您现在可以解锁更多宇宙探索功能。",
        relatedData: ["previousLevel": 3, "newLevel": 4, "totalEnergy": 1000]
    )
    
    TimelineEventDetailView(event: sampleEvent)
} 