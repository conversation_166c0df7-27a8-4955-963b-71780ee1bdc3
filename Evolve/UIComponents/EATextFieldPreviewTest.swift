import SwiftUI

/// 专门用于测试TextField键盘输入的预览组件
/// 解决Xcode预览中TextField无法输入文字的问题
struct EATextFieldPreviewTest: View {
    @State private var text1 = ""
    @State private var text2 = ""
    @State private var text3 = ""
    @State private var email = ""
    @State private var password = ""
    @State private var phone = ""
    
    var body: some View {
        // 使用ZStack包装，避免顶级View的@FocusState bug
        ZStack {
            EABackgroundView(style: .authentication, showParticles: true)
            
            ScrollView {
                VStack(spacing: 20) {
                    Text("TextField 键盘输入测试")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.top)
                    
                    Text("在预览中测试各种TextField的键盘输入功能")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                    
                    VStack(spacing: 16) {
                        // 基础文本输入
                        EATextField(
                            text: $text1,
                            placeholder: "基础文本输入",
                            type: .text,
                            leftIcon: "textformat"
                        )
                        
                        // 邮箱输入
                        EATextField(
                            text: $email,
                            placeholder: "邮箱地址",
                            type: .email,
                            leftIcon: "envelope",
                            validator: EATextField.emailValidator
                        )
                        
                        // 密码输入
                        EATextField(
                            text: $password,
                            placeholder: "密码",
                            type: .password,
                            leftIcon: "lock",
                            validator: EATextField.passwordValidator
                        )
                        
                        // 手机号输入
                        EATextField(
                            text: $phone,
                            placeholder: "手机号",
                            type: .phone,
                            leftIcon: "phone",
                            validator: EATextField.phoneValidator
                        )
                        
                        // 搜索输入
                        EATextField(
                            text: $text2,
                            placeholder: "搜索内容",
                            type: .search,
                            leftIcon: "magnifyingglass"
                        )
                        
                        // 多行文本输入
                        EATextField(
                            text: $text3,
                            placeholder: "多行文本输入",
                            type: .text,
                            leftIcon: "doc.text",
                            maxLength: 200
                        )
                    }
                    .padding(.horizontal)
                    
                    // 显示当前输入值
                    VStack(alignment: .leading, spacing: 8) {
                        Text("当前输入值:")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Group {
                            Text("文本1: \(text1.isEmpty ? "未输入" : text1)")
                            Text("邮箱: \(email.isEmpty ? "未输入" : email)")
                            Text("密码: \(password.isEmpty ? "未输入" : "已输入 \(password.count) 个字符")")
                            Text("手机: \(phone.isEmpty ? "未输入" : phone)")
                            Text("搜索: \(text2.isEmpty ? "未输入" : text2)")
                            Text("文本3: \(text3.isEmpty ? "未输入" : text3)")
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.3))
                    )
                    .padding(.horizontal)
                    
                    Spacer(minLength: 100)
                }
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - 预览

struct EATextFieldPreviewTest_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 主要测试预览
            EATextFieldPreviewTest()
                .previewDisplayName("TextField 键盘测试")
            
            // 固定尺寸预览 - 有时候能解决预览问题
            EATextFieldPreviewTest()
                .previewLayout(.fixed(width: 390, height: 844))
                .previewDisplayName("TextField 固定尺寸")
            
            // 设备预览
            EATextFieldPreviewTest()
                .previewDevice(PreviewDevice(rawValue: "iPhone 15 Pro"))
                .previewDisplayName("TextField iPhone 15 Pro")
        }
    }
} 