import SwiftUI

/// Today页面智慧核心测试视图
/// 用于验证新的发光呼吸智慧核心的动态效果
struct EATodayWisdomCoreTest: View {
    @State private var isThinking = false
    
    var body: some View {
        ZStack {
            // 背景 - 与Today页面一致的深海绿渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color("BackgroundDeepGreen"),
                    Color("BackgroundDeepGreen").opacity(0.8)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 40) {
                // 标题
                Text("Today页面智慧核心测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.top, 60)
                
                // 新的发光呼吸智慧核心
                VStack(spacing: 20) {
                    Text("新版发光呼吸智慧核心")
                        .font(.headline)
                        .foregroundColor(Color("PrimaryTurquoise"))
                    
                    // 不同尺寸测试
                    VStack(spacing: 30) {
                        EABreathingWisdomCore(size: 60)
                        EABreathingWisdomCore(size: 80)
                        EABreathingWisdomCore(size: 100)
                    }
                    
                    Text("可交互版本")
                        .font(.headline)
                        .foregroundColor(Color("PrimaryTurquoise"))
                        .padding(.top, 20)
                    
                    EAInteractiveBreathingWisdomCore(size: 80) {
                        // 智慧核心点击处理 - 实际使用时会有具体逻辑
                    }
                    
                    Text("点击与Aura一同成长")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color("PrimaryTurquoise"))
                }
                
                // 效果说明
                VStack(spacing: 12) {
                    Text("动态效果特性")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("• 真正的呼吸动画 - 从小变大循环")
                        Text("• 多层发光效果 - 外层、中层、核心")
                        Text("• 动态阴影 - 创造真实发光感")
                        Text("• 触觉反馈 - 点击时的震动反馈")
                        Text("• 无图标设计 - 纯动态特效")
                    }
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.8))
                }
                .padding(.horizontal, 20)
                
                Spacer()
            }
        }
    }
}

// MARK: - 预览
#Preview("Today智慧核心测试") {
    EATodayWisdomCoreTest()
} 