# Evolve项目AI开发审查规则 v3.3

**更新日期：** 2025-01-06  
**适用范围：** Evolve iOS应用AI辅助开发的代码审查与质量控制  
**审查目标：** 确保AI开发产出符合项目规范、iOS设计标准和代码质量要求  
**文档依据：** 严格遵循《开发规范文档 v2.5》（.cursorrules）作为唯一权威标准

---

## 📝 SwiftData关系定义口诀（务必牢记！）
> **一对关系，一端inverse，另一端普通属性，SwiftData自动帮你搞定双向！**
> 
> 生活比喻：就像两个人结婚，户口本上只需要一方写"配偶是谁"，另一方自动就能查到，不用两边都写。

---

## 🎯 一、审查执行原则

### 核心要求
- **分层审查**：架构基础层→服务层→视图层，按优先级系统审查
- **一次性全面**：禁止"发现一批问题→修复→再发现新问题"的循环模式
- **问题分级**：🔴严重→🟡重要→🟢一般，优先处理架构性问题
- **严禁print()调试语句**：生产代码中严禁保留print()调试语句

### 使用方式
1. **开发完成后**：AI完成任何开发任务后，按本规则逐项检查
2. **问题修正**：发现问题必须立即修正，直到完全符合规范
3. **编译验证**：每次修复后必须完整编译验证

---

## 🔴 二、架构基础层审查（最高优先级）

### 2.1 SwiftData关系模式检查

**🚨 强制要求（绝对不可违反）**：
- [ ] **禁止外键模式**：不得使用`userId`、`habitId`等外键字段
- [ ] **关系模式强制**：必须使用标准SwiftData `@Relationship`模式
- [ ] **单端inverse规则**：每对关系只在一端用`@Relationship(inverse:)`，另一端用普通属性，SwiftData自动维护双向关系
- [ ] **关系完整性**：所有关系必须有对应的反向关系定义（但只需一端写@Relationship）
- [ ] **关系赋值顺序**：所有关系赋值必须遵循"插入Context→赋值关系→保存Context"，禁止在init中赋值关系
- [ ] **🚨 Context一致性（iOS 18+关键）**：所有关系赋值操作必须保证两端对象属于同一ModelContext，禁止跨Context赋值（防止iOS 18+崩溃）

**🔒 共享Container架构检查（新增关键要求）**：
- [ ] **共享ModelContainer**：所有Repository必须使用同一个ModelContainer实例
- [ ] **禁止独立Context**：不得在Repository中独立创建ModelContext
- [ ] **Container传递正确**：EARepositoryContainer必须传递共享Container给所有Repository
- [ ] **🚫 拒绝统一数据管理器**：严禁创建统一数据管理器替代Repository架构

**快速检测方法**：
```bash
# 外键违规检测
grep -r "var.*Id: UUID" --include="*.swift" Evolve/
grep -r "userId\|habitId\|profileId" --include="*.swift" Evolve/

# Context一致性检测
grep -r "ModelContext(" --include="*.swift" Evolve/
grep -r "static let shared.*Manager" --include="*.swift" Evolve/
```

**标准关系模式**：
```swift
// ✅ 正确：标准SwiftData关系
@Model final class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] // 只在这里写@Relationship
}
@Model final class EAHabit {
    var user: EAUser? // 只用普通属性，不用@Relationship
}

// ❌ 禁止：外键模式
@Model final class EAHabit {
    var userId: UUID? // 严格禁止
}

// ❌ 禁止：统一数据管理器
class EAUnifiedDataManager {
    static let shared = EAUnifiedDataManager() // 严格禁止
}
```

### 2.2 Repository模式完整性检查

**强制要求**：
- [ ] **数据访问统一**：所有数据访问必须通过Repository层
- [ ] **禁止直接ModelContext**：Service、ViewModel不得直接注入ModelContext
- [ ] **Repository协议**：每个Repository必须有对应的协议定义
- [ ] **Repository必须@ModelActor修饰**：所有Repository都必须用@ModelActor修饰，禁止普通class/struct实现Repository，保证线程安全

**检测方法**：
```bash
# 违规模式检测
grep -r "@Environment(\\.modelContext)" --include="*.swift" Evolve/
grep -r "private.*modelContext" --include="*.swift" Evolve/
```

### 2.3 数据模型复杂度控制

**限制要求**：
- [ ] **关系数量限制**：单个模型@Relationship不得超过5个
- [ ] **模型拆分**：超过限制的模型必须拆分为多个相关模型，必须通过一对一关系连接相关模型，禁止直接多对多关系
- [ ] **关系访问**：通过关系访问数据，禁止外键查询

---

## 🟡 三、服务层架构审查（高优先级）

### 3.1 单例模式禁用检查

**强制要求**：
- [ ] **禁用单例**：不得使用`static let shared = XXX()`模式
- [ ] **依赖注入**：必须通过Environment传递服务
- [ ] **协议抽象**：服务必须有协议定义
- [ ] **禁止Environment默认值隐式单例**：Environment扩展禁止设置默认值，避免隐式单例

**检测方法**：
```bash
# 单例违规检测
grep -r "static let shared" --include="*.swift" Evolve/
grep -r "\\.shared\\." --include="*.swift" Evolve/
```

### 3.2 AI数据桥接架构检查

**要求**：
- [ ] **AI服务隔离**：AI服务不得直接访问Repository
- [ ] **数据桥接层**：通过专门的数据桥接层访问数据
- [ ] **格式转换统一**：AI数据格式转换集中处理
- [ ] **AI分析结果和缓存独立存储**：AI分析结果和缓存必须独立存储，桥接层需实现缓存和数据格式转换

### 3.3 业务服务层架构检查

**核心检查项**：
- [ ] **服务职责单一**：每个Service专注单一业务领域
- [ ] **错误处理统一**：使用统一的错误类型和处理机制
- [ ] **异步操作规范**：正确使用Swift Concurrency（async/await）
- [ ] **缓存策略合理**：实现适当的缓存机制，避免重复计算

**检测方法**：
```bash
# 服务层检查
grep -r "class.*Service" --include="*.swift" Evolve/Core/Services/
grep -r "protocol.*Service" --include="*.swift" Evolve/Core/Services/
```

---

## 🟢 四、视图层合规审查（中优先级）

### 4.1 MVVM架构检查

**必须检查项**：
- [ ] **@MainActor标记**：所有ViewModel必须标记@MainActor
- [ ] **状态管理**：正确使用@Published暴露状态
- [ ] **单向数据流**：View → ViewModel → Repository
- [ ] **依赖注入**：通过Environment注入依赖

### 4.2 SwiftUI实现检查

**核心检查项**：
- [ ] **Sheet状态管理**：使用统一的Sheet状态枚举
- [ ] **组件复用**：重复UI元素封装为组件
- [ ] **EA命名规范**：所有自定义类型必须用EA前缀
- [ ] **预览支持**：所有View必须包含SwiftUI预览

### 4.3 UI设计规范检查

**设计一致性**：
- [ ] **色彩系统**：使用Assets.xcassets中定义的颜色
- [ ] **间距系统**：使用8pt基础间距的倍数
- [ ] **字体系统**：使用系统字体和定义的层级
- [ ] **安全区域**：正确处理刘海屏和Home Indicator

### 4.4 状态管理统一性检查

**SwiftUI状态管理规范**：
- [ ] **@State使用正确**：仅用于单一视图本地状态
- [ ] **@Binding使用正确**：用于双向数据绑定
- [ ] **@ObservedObject/@StateObject使用正确**：ViewModel引用
- [ ] **@Environment使用正确**：全局依赖注入

**Sheet状态管理特殊检查**：
- [ ] **统一状态管理**：所有Sheet状态在单一ViewModel中集中管理
- [ ] **枚举驱动模式**：使用枚举定义所有可能的Sheet类型
- [ ] **避免多重Sheet冲突**：确保同时只有一个Sheet处于活跃状态

### 4.5 组件复用与设计系统检查

**组件库一致性**：
- [ ] **组件复用阈值**：3个或以上地方重复使用的UI元素已封装为组件
- [ ] **参数传递清晰**：使用明确的参数传递组件配置
- [ ] **组件接口设计**：定义清晰的组件API，隐藏内部实现
- [ ] **@ViewBuilder支持**：提供内容定制能力
- [ ] **EA前缀**：所有自定义类型必须用EA前缀，组件接口需清晰、参数传递明确

### 4.6 无障碍支持检查

**可访问性要求**：
- [ ] **VoiceOver支持**：所有交互元素提供合适的标签和提示
- [ ] **动态字体支持**：响应系统字体大小设置变化
- [ ] **对比度合规**：文本对比度遵循WCAG 2.1 AA标准
- [ ] **减少动画支持**：响应系统"减少动画"设置

---

## 🔧 五、集成与性能审查

### 5.1 AI服务集成检查

**AI集成架构要求**：
- [ ] **接口封装**：EAAIService处理API通信，EAAIManager管理会话
- [ ] **类型安全**：使用Codable协议，定义请求/响应模型
- [ ] **成本控制**：实现AI调用成本控制机制
- [ ] **缓存策略**：AI响应缓存管理，避免重复调用

### 5.2 通知系统集成检查

**通知系统要求**：
- [ ] **权限管理**：合适时机请求通知权限
- [ ] **智能调度**：基于用户设定时间和行为模式
- [ ] **个性化内容**：根据教练风格生成不同提醒内容
- [ ] **通知管理**：用户可查看和管理已安排的通知

### 5.3 性能优化检查

**性能指标要求**：
- [ ] **启动时间**：应用启动时间<1.5秒
- [ ] **UI流畅度**：保持60fps的UI流畅度
- [ ] **内存使用**：内存使用<200MB
- [ ] **网络优化**：合理的网络请求频率和缓存策略

### 5.4 内存管理检查

**内存安全要求**：
- [ ] **循环引用防范**：正确使用[weak self]
- [ ] **资源释放**：及时取消Task和移除监听器
- [ ] **大对象管理**：图片延迟加载和适当缓存
- [ ] **SwiftData内存管理**：及时释放大型查询结果

---

## 🔒 六、安全与隐私审查

### 6.1 数据安全检查

**安全存储要求**：
- [ ] **敏感数据保护**：API密钥、支付信息使用Keychain存储
- [ ] **本地存储安全**：非敏感数据使用UserDefaults
- [ ] **网络安全**：强制HTTPS，实现证书验证
- [ ] **数据传输加密**：敏感数据传输使用额外加密

### 6.2 用户隐私检查

**隐私保护要求**：
- [ ] **数据收集透明**：清晰说明收集的数据类型和用途
- [ ] **用户控制权**：提供数据导出和删除功能
- [ ] **匿名化处理**：行为分析数据匿名化处理
- [ ] **隐私政策**：提供简明的隐私政策

### 6.3 身份验证检查

**认证安全要求**：
- [ ] **多种认证方式**：支持密码、生物认证和第三方认证
- [ ] **会话管理**：合理的会话过期时间
- [ ] **敏感操作保护**：重要操作需额外验证
- [ ] **异常检测**：检测异常登录行为

---

## 🧪 七、测试与质量保证审查

### 7.1 SwiftUI预览检查

**预览规范要求**：
- [ ] **预览完整性**：所有SwiftUI视图必须包含预览
- [ ] **预览数据**：使用PreviewData.swift中的模拟数据
- [ ] **多场景支持**：不同状态和设备尺寸的预览
- [ ] **预览配置**：正确配置ModelContainer用于预览

### 7.2 测试覆盖率检查

**测试要求**：
- [ ] **单元测试**：所有ViewModel和Service需有单元测试
- [ ] **测试覆盖率**：核心业务逻辑≥80%
- [ ] **依赖模拟**：使用协议和依赖注入便于测试
- [ ] **UI测试**：测试核心用户流程，使用可访问性标识符

### 7.3 错误处理检查

**错误处理标准**：
- [ ] **错误分类**：网络错误、数据错误、业务错误
- [ ] **用户体验**：友好的错误提示和恢复建议
- [ ] **降级策略**：缓存数据、离线模式
- [ ] **错误日志**：适当的错误日志记录

---

## 🚀 八、社区功能专项审查

### 8.1 社区架构检查

**社区功能架构要求**：
- [ ] **独立Repository**：社区功能必须有独立的EACommunityRepository
- [ ] **模型独立管理**：社区相关模型不得与核心用户模型混合
- [ ] **权限独立控制**：社区功能的权限控制独立于核心功能

### 8.2 社区性能检查

**性能优化要求**：
- [ ] **分页加载**：社区内容加载必须分页，每页≤20条
- [ ] **图片懒加载**：社区图片必须实现懒加载机制
- [ ] **缓存策略**：社区内容必须有合理的本地缓存策略
- [ ] **加载性能**：页面加载<2秒

### 8.3 社区内容管理检查

**内容管理要求**：
- [ ] **审核机制**：关键词过滤、用户举报、AI辅助审核
- [ ] **质量保障**：鼓励真实分享、标识AI生成内容
- [ ] **隐私保护**：可选分享范围、匿名选项、用户数据控制
- [ ] **数据安全**：社区数据分离存储、遵循GDPR法规

---

## 🔧 九、快速修复指导

### 9.1 常见问题快速修复

**SwiftData关系错误**：
1. 检查inverse参数的KeyPath是否正确
2. 确认关系两端的类型匹配
3. 验证属性名拼写和单复数
4. 检查关系赋值顺序和上下文一致性

**循环修复防范**：
1. **3次规则**：同一错误修复超过3次必须停止
2. **批量修复**：相关文件必须同时修改
3. **系统分析**：重新审查项目架构和依赖关系

### 9.2 编译错误处理

**强制流程**：
1. **全面收集**：收集所有编译错误和警告
2. **根因分析**：SwiftData关系→@MainActor→重复定义→依赖缺失
3. **批量修复**：一次性修复所有相关文件
4. **完整验证**：修复后必须完整编译验证

---

## 📋 十、审查报告标准格式

### 输出格式要求

```
## 🔍 系统性架构审查报告

### 🔴 严重问题（必须立即修复）
- [具体问题] - 影响：[架构/性能/安全]
- 涉及文件：[文件列表]

### 🟡 重要问题（影响架构一致性）
- [具体问题] - 影响：[维护性/扩展性]

### 🟢 一般问题（代码质量优化）
- [具体问题] - 影响：[代码风格/性能]

### 📋 系统性修复计划
**批次1（架构基础）**：[文件列表] - [修复内容]
**批次2（服务层）**：[文件列表] - [修复内容]
**批次3（视图层）**：[文件列表] - [修复内容]

### ✅ 合规验证结果
- [ ] 架构基础层合规
- [ ] 服务层架构合规  
- [ ] 视图层实现合规
- [ ] 业务逻辑层合规
- [ ] 集成与性能合规
- [ ] 安全与隐私合规
- [ ] 测试与质量合规
- [ ] 社区功能合规
- [ ] 整个项目编译无错误无警告
```

---

## ⚡ 十一、应急处理机制

### 触发条件
- 连续3次修复同一问题
- 发现严重架构违规
- 工具调用接近限制

### 处理步骤
1. **立即停止**：停止当前修复操作
2. **全面分析**：重新审查项目架构和依赖关系
3. **系统方案**：制定系统性解决方案
4. **请求指导**：向用户说明情况并请求具体指导

### 质量红线（绝对不可违反）
1. **架构红线**：禁止外键模式、禁止单例模式、禁止直接ModelContext
2. **修复红线**：禁止循环修复、禁止单点修复、禁止跳过验证
3. **审查红线**：禁止分批发现、禁止跳过层级、禁止忽略优先级

**⚠️ 最终保障**：当AI无法确保质量时，必须主动向用户说明情况并请求人工介入。 