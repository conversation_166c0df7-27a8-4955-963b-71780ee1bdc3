<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AuraMind AI - 认证与全局UI组件</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #1a202c; /* 深色背景 */
            padding: 30px;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        .mockup-gallery-title {
            text-align: center;
            font-size: 2.5rem; /* 标题字号 */
            font-weight: 700; /* 标题字重 */
            color: #e2e8f0; /* 标题颜色 */
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .mockup-unit {
            margin-bottom: 40px;
        }

        .mockup-page-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .iphone-mockup {
            width: 393px; /* iPhone 14/15 Pro 宽度 */
            height: 852px; /* iPhone 14/15 Pro 高度 */
            background-color: #1c1c1e; /* iPhone 模拟器外壳颜色 */
            border-radius: 50px; /* 外壳圆角 */
            padding: 14px; /* 内边距 */
            box-shadow: 0 20px 50px rgba(0,0,0,0.4), 0 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #000; /* 屏幕背景色 */
            border-radius: 36px; /* 屏幕圆角 */
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .iphone-mockup::before { /* 灵动岛 */
            content: '';
            position: absolute;
            top: 22px;
            left: 50%;
            transform: translateX(-50%);
            width: 130px;
            height: 30px;
            background-color: #000;
            border-radius: 15px;
            z-index: 1010;
        }
        
        .ios-status-bar {
            height: 44px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 13px;
            font-weight: 500;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1005;
            padding-top: 12px; /* 为灵动岛腾出空间 */
        }
        .ios-status-bar .time { margin-left: 5px; }
        .ios-status-bar .signals { display: flex; align-items: center; gap: 5px; margin-right: 5px; }
        .ios-status-bar .signals svg { width: 16px; height: 16px; fill: white; }

        .app-container { /* 主应用容器背景和粒子效果 */
            flex-grow: 1;
            background-color: #002b20; /* 深青色基底 */
            background-image: 
                radial-gradient(ellipse at 15% 25%, rgba(0, 128, 128, 0.35) 0%, transparent 55%),
                radial-gradient(ellipse at 85% 60%, rgba(13, 71, 161, 0.25) 0%, transparent 65%),
                radial-gradient(circle at 20% 85%, rgba(56, 239, 125, 0.18) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 55%, rgba(0, 105, 92, 0.22) 0%, transparent 70%),
                linear-gradient(170deg, #0A2F51 0%, #005A4B 55%, #002b20 100%); 
            color: white;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            padding-top: 44px; /* 为状态栏留出空间 */
        }
        
        .page-content-wrapper {
            display: flex; 
            flex-direction: column;
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
            width: 100%;
            color: #e2e8f0; 
            position: relative;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none;  /* IE and Edge */
        }
        .page-content-wrapper::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }


        @keyframes float {
            0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.5; }
            50% { transform: translateY(-15px) translateX(8px) scale(1.05); opacity: 0.2; }
            100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.5; }
        }
        .light-particle {
            position: absolute;
            background-color: rgba(173, 216, 230, 0.25); /* 淡青色光点 */
            border-radius: 50%;
            animation: float 10s infinite ease-in-out;
            pointer-events: none;
        }

        /* 通用表单元素样式 */
        .auth-form-container {
            padding: 20px 28px; /* 内边距调整 */
            display: flex;
            flex-direction: column;
            justify-content: center; /* 垂直居中内容 */
            flex-grow: 1;
        }

        .auth-title {
            font-size: 2.25rem; /* 36px */
            font-weight: 700;
            color: #e0f2fe; /* Light Sky Blue */
            text-align: center;
            margin-bottom: 16px; /* 减小标题和下方元素的间距 */
            line-height: 1.2;
        }
        
        .auth-subtitle {
            font-size: 1rem; /* 16px */
            color: #bae6fd; /* Sky Blue 300 */
            text-align: center;
            margin-bottom: 32px; /* 副标题和表单的间距 */
        }

        .form-input-group {
            margin-bottom: 20px; /* 输入框组之间的间距 */
        }

        .form-input {
            width: 100%;
            background-color: rgba(255, 255, 255, 0.08); /* 更柔和的背景 */
            border: 1px solid rgba(255, 255, 255, 0.15); /* 更清晰的边框 */
            color: #e2e8f0;
            border-radius: 16px; /* 更圆润的有机形态 */
            padding: 14px 18px; /* 调整内边距 */
            font-size: 0.95rem; /* 约15px */
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            appearance: none; /* 移除默认外观 */
        }
        .form-input::placeholder {
            color: #94a3b8; /* Slate 400 */
        }
        .form-input:focus {
            outline: none;
            border-color: #2dd4bf; /* Teal 400 */
            box-shadow: 0 0 0 3px rgba(45, 212, 191, 0.3); /* Teal 辉光效果 */
        }

        .auth-button {
            width: 100%;
            background: linear-gradient(135deg, #FF7F50, #40E0D0); /* 能量渐变 (珊瑚粉到荧光青) */
            color: white;
            font-weight: 600;
            font-size: 1.05rem; /* 约16.8px */
            padding: 14px 20px;
            border-radius: 16px; /* 保持圆润 */
            border: none;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .auth-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,107,71,0.3), 0 6px 20px rgba(64,224,208,0.3);
        }
        .auth-button:active {
            transform: translateY(0px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .auth-link {
            color: #67e8f9; /* Cyan 300 */
            text-decoration: none;
            font-size: 0.875rem; /* 14px */
            transition: color 0.3s ease;
        }
        .auth-link:hover {
            color: #22d3ee; /* Cyan 400 */
            text-decoration: underline;
        }

        .divider-container {
            display: flex;
            align-items: center;
            text-align: center;
            color: #94a3b8; /* Slate 400 */
            font-size: 0.8rem; /* 12.8px */
            margin: 28px 0; /* 上下边距 */
        }
        .divider-container::before,
        .divider-container::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        }
        .divider-container:not(:empty)::before {
            margin-right: .5em;
        }
        .divider-container:not(:empty)::after {
            margin-left: .5em;
        }

        .social-login-buttons {
            display: flex;
            justify-content: center; /* 居中排列 */
            gap: 20px; /* 图标间距 */
        }
        .social-login-button {
            width: 48px;
            height: 48px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%; /* 圆形按钮 */
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.15);
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        .social-login-button:hover {
            background-color: rgba(255, 255, 255, 0.18);
            transform: scale(1.05);
        }
        .social-login-button svg {
            width: 24px;
            height: 24px;
            fill: #e2e8f0; /* 图标颜色 */
        }

        /* 返回按钮 (用于非主页的页面顶部) */
        .page-header-back-button {
            position: absolute;
            top: 12px; /* 根据状态栏调整，确保在内容区 */
            left: 12px;
            padding: 8px;
            border-radius: 50%;
            background-color: rgba(0,0,0,0.2);
            z-index: 10; /* 确保在粒子效果之上 */
            transition: background-color 0.2s ease;
        }
        .page-header-back-button:hover {
             background-color: rgba(0,0,0,0.4);
        }
        .page-header-back-button svg {
            width: 24px;
            height: 24px;
            color: #AFEEEE;
        }
        
        /* 加载指示器样式 */
        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .loading-core {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(56,239,125,0.7) 0%, rgba(0,200,200,0.6) 70%, rgba(0,128,128,0.4) 100%);
            position: relative;
            animation: pulseCore 1.8s infinite ease-in-out, rotateCore 8s infinite linear;
        }
        .loading-core::before, .loading-core::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            opacity: 0.2;
        }
        .loading-core::before {
            width: 120%;
            height: 120%;
            border: 3px solid rgba(173, 255, 230, 0.5); /* Light Cyan/Teal */
            animation: pulseRingOuter 1.8s infinite ease-in-out;
            box-shadow: 0 0 10px rgba(173, 255, 230, 0.3), inset 0 0 8px rgba(173, 255, 230, 0.2);
        }
        .loading-core::after {
            width: 150%;
            height: 150%;
            border: 2px dashed rgba(173, 255, 230, 0.3);
            animation: pulseRingInner 1.8s infinite ease-in-out reverse, rotateCore 16s infinite linear reverse; 
        }

        @keyframes pulseCore {
            0%, 100% { transform: scale(0.95); box-shadow: 0 0 20px 5px rgba(56,239,125,0.4), 0 0 30px 10px rgba(0,200,200,0.3); }
            50% { transform: scale(1.05); box-shadow: 0 0 30px 10px rgba(56,239,125,0.6), 0 0 45px 15px rgba(0,200,200,0.45); }
        }
        @keyframes rotateCore {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        @keyframes pulseRingOuter {
            0%, 100% { opacity: 0.2; transform: translate(-50%, -50%) scale(0.9); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
        }
        @keyframes pulseRingInner {
            0%, 100% { opacity: 0.1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.95); }
        }

        .loading-text {
            margin-top: 20px;
            font-size: 1rem;
            color: #a5f3fc; /* Light Teal */
            text-shadow: 0 0 5px rgba(165, 243, 252, 0.5);
        }

        /* 空状态组件样式 */
        .empty-state-illustration svg {
            filter: drop-shadow(0 4px 10px rgba(127, 255, 212, 0.3)); /* Aquamarine glow */
        }

        /* 模态框 (对话框) 样式 */
        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(10, 20, 30, 0.6); /* 深色半透明背景 */
            backdrop-filter: blur(8px); /* 毛玻璃效果 */
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            z-index: 1000; /* 确保在最上层 */
        }
        .modal-content {
            background-color: #1e293b; /* Slate 800 - 深蓝灰色 */
            border-radius: 24px; /* 有机圆角 */
            padding: 28px;
            width: 100%;
            max-width: 320px; /* 模态框最大宽度 */
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.08) inset;
            border: 1px solid rgba(255,255,255,0.1);
            transform: scale(0.95);
            opacity: 0;
            animation: modalAppear 0.3s ease-out forwards;
        }
        @keyframes modalAppear {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        .modal-icon-area {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-title {
            font-size: 1.375rem; /* 22px */
            font-weight: 600;
            color: #e0f2fe; /* Light Sky Blue */
            margin-bottom: 10px;
        }
        .modal-message {
            font-size: 0.9rem; /* 14.4px */
            color: #cbd5e1; /* Slate 300 */
            line-height: 1.6;
            margin-bottom: 24px;
        }
        .modal-button-group {
            display: flex;
            gap: 12px;
        }
        .modal-button {
            flex-grow: 1;
            padding: 12px 16px;
            border-radius: 14px; /* 圆润按钮 */
            font-size: 0.95rem; /* 15.2px */
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
            border: none;
        }
        .modal-button.primary-button {
            background: linear-gradient(135deg, #FF7F50, #40E0D0); /* 主按钮能量渐变 */
            color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        .modal-button.primary-button:hover {
            box-shadow: 0 4px 15px rgba(255,107,71,0.2), 0 4px 15px rgba(64,224,208,0.2);
        }
        .modal-button.secondary-button {
            background-color: rgba(255, 255, 255, 0.12); /* 次要按钮背景 */
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .modal-button.secondary-button:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .modal-button:active {
            transform: scale(0.97);
        }

        /* 错误模态框特定颜色 */
        .modal-content.error-modal .modal-button.primary-button {
             background: linear-gradient(135deg, #EF4444, #F87171); /* 红色系渐变 */
        }
        .modal-content.error-modal .modal-button.primary-button:hover {
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }
    </style>
</head>
<body>
    <h1 class="mockup-gallery-title">AuraMind AI - 认证与全局UI</h1>
    <div class="flex flex-wrap justify-center gap-x-8 gap-y-16">
        <!-- Mockup 1: 登录页面 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.2 登录页面</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:41</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 6px; height: 6px; top: 10%; left: 15%; animation-duration: 12s;"></div>
                        <div class="light-particle" style="width: 8px; height: 8px; top: 40%; left: 80%; animation-duration: 15s; animation-delay: 2s;"></div>
                        <div class="light-particle" style="width: 5px; height: 5px; top: 70%; left: 20%; animation-duration: 10s; animation-delay: 1s;"></div>
                        
                        <div class="page-content-wrapper">
                            <div class="auth-form-container">
                                <h1 class="auth-title">欢迎回来</h1>
                                <p class="auth-subtitle">登录您的 Evolve AI 心境账户</p>
                                
                                <div class="form-input-group">
                                    <input type="text" class="form-input" placeholder="手机号 / 邮箱">
                                </div>
                                <div class="form-input-group">
                                    <input type="password" class="form-input" placeholder="密码">
                                </div>
                                <div class="text-right mb-6">
                                    <a href="#" class="auth-link">忘记密码？</a>
                                </div>
                                
                                <button class="auth-button mb-6">登录</button>
                                
                                <div class="divider-container">或通过以下方式登录</div>
                                
                                <div class="social-login-buttons mb-8">
                                    <!-- Apple Icon -->
                                    <button class="social-login-button" aria-label="通过Apple登录">
                                        <svg viewBox="0 0 24 24"><path d="M19.228 14.145c-.076.008-.15.016-.224.016-.889 0-1.528-.461-2.256-1.233-.69-.725-1.153-1.791-1.043-2.905.103-1.112.935-2.068 1.908-2.642.973-.575 2.008-.74 2.928-.358-.165.895-.626 1.983-1.482 2.896-.792.834-1.706 1.325-2.83 1.175.014.012.027.023.04.035.003-.006.006-.012.009-.018l.002-.003zm-2.336-7.746c.86-.978 1.359-2.256 1.448-3.55h-2.97c-.663.061-1.28.345-1.797.79-.517.444-.921 1.016-1.162 1.661-.24.644-.305 1.33-.184 2.001.614.067 1.227-.09 1.775-.437.549-.347.975-.842 1.223-1.422.248-.58.29-1.213.267-1.843h1.4zM12.042 22c1.46 0 2.582-.632 3.642-1.812.973-1.081 1.575-2.448 1.62-3.968H14.34c-.605.999-1.522 1.878-2.804 1.878-.964 0-1.968-.67-2.868-1.868-.94-1.25-1.677-2.934-1.677-4.665 0-1.923.88-3.595 2.119-4.688.98-.864 2.178-1.308 3.388-1.308.58 0 1.427.16 2.082.758l.013-.008c.048-.055.098-.108.15-.16.067.022.133.047.197.075l-1.088 1.088c-.903.903-1.26 1.568-1.26 2.68 0 1.01.33 1.79 1.192 2.507.86.717 1.828.95 2.92.765.213 1.21.03 2.398-.52 3.464-.552 1.066-1.382 1.925-2.472 2.543-1.09.618-2.327.94-3.68.94z"/></svg>
                                    </button>
                                    <!-- WeChat Icon (Simplified) -->
                                    <button class="social-login-button" aria-label="通过微信登录">
                                        <svg viewBox="0 0 24 24"><path d="M12 2C6.486 2 2 6.486 2 12c0 4.32 2.795 7.995 6.638 9.372.16.296.27.614.323.942H8.05C6.447 22.314 5.5 21.186 5.5 19.714c0-.952.502-1.734 1.217-2.271-.154-.335-.266-.688-.323-1.047C3.016 15.303 1 12.037 1 8.333c0-3.808 2.766-6.903 6.358-7.27.09-.04.18-.08.272-.115C8.616.34 9.973 0 11.5 0c2.053 0 3.828.78 5.156 2.057C18.06 3.46 19 5.284 19 7.286c0 2.116-.94 3.997-2.498 5.395-.27.246-.502.526-.687.832.132.352.212.72.212 1.101 0 .975-.46 1.827-1.16 2.383.245.36.38.756.38 1.173 0 1.205-.792 2.204-1.947 2.545.055-.33.164-.65.328-.95.06-.11.12-.22.18-.33C16.205 20.005 19 16.32 19 12c0-5.514-4.486-10-10-10S2 6.486 2 12zm6.904 13.177c-.024.002-.047.005-.07.005-.368 0-.81-.09-1.238-.27-.585-.246-1.06-.667-1.382-1.22-.32-.553-.46-1.196-.396-1.84.186.04.37.06.55.06.482 0 .93-.09 1.337-.26.56-.23.998-.624 1.263-1.126.265-.502.352-1.055.24-1.586.413.05.83.075 1.242.075.248 0 .496-.008.74-.024l.384-3.07c0-.003 0-.005-.002-.008l-2.685-.002c-.162 0-.32-.018-.476-.05-.555-.117-1.02-.407-1.332-.806-.312-.4-.456-.88-.398-1.36.094-.01.19-.016.283-.016.47 0 .928.068 1.354.197.58.177 1.043.524 1.323.976.28.452.36.97.21 1.446-.36-.03-.722-.046-1.086-.046-.164 0-.33.002-.49.008l-.28 2.242c0 .003 0 .005.002.008l2.29.002c.537 0 1.043-.07 1.506-.202.613-.174 1.092-.54 1.362-1.036.27-.496.32-.98.14-1.432.342-.17.712-.296 1.108-.37l.002 1.076s.002.002.002.002l.002.005v.003l.002.002.002.002h.002l.002.002h.002l.002.002c0 .002.002.002.002.003l.002.003v.002l.002.002c.03.623-.104 1.33-.463 1.982-.358.654-.916 1.163-1.607 1.452-.69.29-1.45.375-2.196.248zM8.75 8.5a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm3 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm3 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/></svg>
                                    </button>
                                    <!-- Google Icon -->
                                    <button class="social-login-button" aria-label="通过Google登录">
                                        <svg viewBox="0 0 24 24"><path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/><path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/><path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/><path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/><path d="M1 1h22v22H1z" fill="none"/></svg>
                                    </button>
                                </div>
                                
                                <div class="text-center mt-8">
                                    <span class="text-slate-400 text-sm">还没有账户？</span>
                                    <a href="#" class="auth-link font-semibold">立即注册</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 更多模拟器将在此处添加 -->
        <!-- Mockup 2: 注册页面 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.3 注册页面</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:42</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 5px; height: 5px; top: 15%; left: 70%; animation-duration: 13s;"></div>
                        <div class="light-particle" style="width: 7px; height: 7px; top: 50%; left: 25%; animation-duration: 11s; animation-delay: 1.5s;"></div>
                        
                        <div class="page-content-wrapper">
                            <!-- 返回按钮 -->
                            <button class="page-header-back-button" aria-label="返回">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>

                            <div class="auth-form-container pt-16"> <!-- 增加pt以避开返回按钮 -->
                                <h1 class="auth-title">创建您的心境账户</h1>
                                <p class="auth-subtitle">加入 Evolve AI，开启微习惯之旅</p>
                                
                                <div class="form-input-group">
                                    <input type="text" class="form-input" placeholder="昵称">
                                </div>
                                <div class="form-input-group">
                                    <input type="text" class="form-input" placeholder="手机号 / 邮箱">
                                </div>
                                <div class="form-input-group">
                                    <input type="password" class="form-input" placeholder="设置密码">
                                </div>
                                <div class="form-input-group">
                                    <input type="password" class="form-input" placeholder="确认密码">
                                </div>
                                
                                <button class="auth-button mt-4 mb-6">注册</button> <!-- 调整了mt -->
                                
                                <div class="text-center mt-6">
                                    <span class="text-slate-400 text-sm">已有账户？</span>
                                    <a href="#" class="auth-link font-semibold">立即登录</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mockup 3: 找回密码页面 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.4 找回密码</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:43</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 6px; height: 6px; top: 20%; left: 10%; animation-duration: 14s;"></div>
                        <div class="light-particle" style="width: 8px; height: 8px; top: 60%; left: 85%; animation-duration: 12s; animation-delay: 2.5s;"></div>
                        
                        <div class="page-content-wrapper">
                             <!-- 返回按钮 -->
                             <button class="page-header-back-button" aria-label="返回">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <div class="auth-form-container pt-16">
                                <h1 class="auth-title">找回密码</h1>
                                <p class="auth-subtitle">请输入您的注册手机号或邮箱以重置密码</p>
                                
                                <div class="form-input-group">
                                    <input type="text" class="form-input" placeholder="手机号 / 邮箱">
                                </div>
                               
                                <button class="auth-button mt-8 mb-6">发送验证码</button> 
                                <!-- 或者 <button class="auth-button mt-8 mb-6">发送重置链接</button> -->
                                
                                <div class="text-center mt-6">
                                    <a href="#" class="auth-link font-semibold">返回登录</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mockup 4: 设置新密码页面 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.4 设置新密码</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:44</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 7px; height: 7px; top: 25%; left: 75%; animation-duration: 11s;"></div>
                        <div class="light-particle" style="width: 5px; height: 5px; top: 65%; left: 15%; animation-duration: 14s; animation-delay: 1s;"></div>
                        
                        <div class="page-content-wrapper">
                             <!-- 返回按钮 -->
                             <button class="page-header-back-button" aria-label="返回">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <div class="auth-form-container pt-16">
                                <h1 class="auth-title">设置新密码</h1>
                                <p class="auth-subtitle">请输入验证码并设置您的新密码</p>
                                
                                <div class="form-input-group">
                                    <input type="text" class="form-input" placeholder="验证码">
                                </div>
                                <div class="form-input-group">
                                    <input type="password" class="form-input" placeholder="设置新密码">
                                </div>
                                <div class="form-input-group">
                                    <input type="password" class="form-input" placeholder="确认新密码">
                                </div>
                               
                                <button class="auth-button mt-8 mb-6">确认重置</button> 
                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mockup 5: 加载状态指示器 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.5 加载状态指示器</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:45</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 6px; height: 6px; top: 10%; left: 15%; animation-duration: 12s;"></div>
                        <div class="light-particle" style="width: 8px; height: 8px; top: 40%; left: 80%; animation-duration: 15s; animation-delay: 2s;"></div>
                        
                        <div class="page-content-wrapper flex items-center justify-center">
                            <div class="loading-indicator">
                                <div class="loading-core"></div>
                                <p class="loading-text">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mockup 6: 空状态页面/组件 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.6 空状态组件</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:46</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 5px; height: 5px; top: 20%; left: 80%; animation-duration: 13s;"></div>
                        <div class="light-particle" style="width: 7px; height: 7px; top: 65%; left: 30%; animation-duration: 11s; animation-delay: 1s;"></div>
                        
                        <!-- 假设这是一个习惯列表的容器，但为空 -->
                        <div class="page-content-wrapper flex flex-col items-center justify-center p-6 text-center">
                            <div class="empty-state-illustration mb-6">
                                <!-- 生态插画: 小树苗 -->
                                <svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                    <defs>
                                        <linearGradient id="gradSoil" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#8B4513;stop-opacity:0.2" />
                                            <stop offset="100%" style="stop-color:#A0522D;stop-opacity:0.3" />
                                        </linearGradient>
                                        <linearGradient id="gradSprout" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:#AFFFAF;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#50C878;stop-opacity:1" />
                                        </linearGradient>
                                         <filter id="glow">
                                            <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
                                            <feMerge>
                                                <feMergeNode in="coloredBlur"/>
                                                <feMergeNode in="SourceGraphic"/>
                                            </feMerge>
                                        </filter>
                                    </defs>
                                    <path d="M35 85 Q50 80 65 85 A20 5 0 0 0 35 85 Z" fill="url(#gradSoil)" />
                                    <path d="M50 82 Q50 70 52 60 T50 45" stroke="url(#gradSprout)" stroke-width="5" fill="none" stroke-linecap="round" filter="url(#glow)"/>
                                    <path d="M50 55 Q40 50 42 45" stroke="url(#gradSprout)" stroke-width="4" fill="none" stroke-linecap="round" filter="url(#glow)"/>
                                    <path d="M50 60 Q60 55 58 50" stroke="url(#gradSprout)" stroke-width="4" fill="none" stroke-linecap="round" filter="url(#glow)"/>
                                    <path d="M50 68 Q45 65 47 60" stroke="url(#gradSprout)" stroke-width="3" fill="none" stroke-linecap="round" filter="url(#glow)"/>
                                </svg>
                            </div>
                            <h3 class="empty-state-title text-xl font-semibold text-sky-200 mb-3">开启您的微习惯之旅</h3>
                            <p class="empty-state-message text-slate-300 text-sm mb-6 leading-relaxed">
                                您的"生态图鉴"还是空的哦。<br>尝试"播种"第一个微元，让生命力在此绽放吧！
                            </p>
                            <button class="auth-button w-auto px-8 py-3 text-base">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 inline-block mr-2 -ml-1">
                                    <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                                </svg>
                                播种新元
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mockup 7: 错误提示对话框 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.7 错误提示对话框</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar"><!-- ... status bar ... --></div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 6px; height: 6px; top: 10%; left: 15%; animation-duration: 12s;"></div>
                        <!-- Dummy background content -->
                        <div class="page-content-wrapper flex items-center justify-center p-6 text-center text-slate-400">
                            <p>这里是应用的一些背景内容...</p>
                        </div>
                        <!-- Modal Overlay -->
                        <div class="modal-overlay">
                            <div class="modal-content error-modal">
                                <div class="modal-icon-area bg-red-500/30">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-10 h-10 text-red-400">
                                        <path fill-rule="evenodd" d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.519 13.007a3.001 3.001 0 01-2.598 4.502H4.48a3.001 3.001 0 01-2.598-4.502L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <h3 class="modal-title">操作失败</h3>
                                <p class="modal-message">登录失败，请检查您的邮箱或密码是否正确，或稍后再试。</p>
                                <button class="modal-button primary-button">知道了</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mockup 8: 确认对话框 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.7 确认对话框</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar"><!-- ... status bar ... --></div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 8px; height: 8px; top: 70%; left: 80%; animation-duration: 13s; animation-delay: 1s;"></div>
                        <!-- Dummy background content -->
                        <div class="page-content-wrapper flex items-center justify-center p-6 text-center text-slate-400">
                            <p>这里是应用的一些背景内容...</p>
                        </div>
                        <!-- Modal Overlay -->
                        <div class="modal-overlay">
                            <div class="modal-content confirmation-modal">
                                <div class="modal-icon-area bg-sky-500/30">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-10 h-10 text-sky-300">
                                        <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <h3 class="modal-title">确认操作</h3>
                                <p class="modal-message">您确定要退出当前账户吗？退出后需要重新登录。</p>
                                <div class="modal-button-group">
                                    <button class="modal-button secondary-button">取消</button>
                                    <button class="modal-button primary-button">确认退出</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mockup 9: 通知权限请求前置引导 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">5.8 通知权限前置引导</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar"><!-- ... status bar ... --></div>
                    <div class="app-container">
                         <div class="light-particle" style="width: 7px; height: 7px; top: 22%; left: 88%; animation-duration: 14s; animation-delay: 0.5s;"></div>
                        <!-- Dummy background content -->
                        <div class="page-content-wrapper flex items-center justify-center p-6 text-center text-slate-400">
                            <p>这里是应用的一些背景内容...</p>
                        </div>
                        <!-- Modal Overlay -->
                        <div class="modal-overlay">
                            <div class="modal-content permission-request-modal">
                                <div class="modal-icon-area bg-teal-500/30">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-10 h-10 text-teal-300">
                                        <path d="M11.25 4.533A9.707 9.707 0 006 3a9.735 9.735 0 00-3.25.555.75.75 0 00-.5.707v14.522c0 .318.218.596.5.707a9.735 9.735 0 003.25.555 9.707 9.707 0 005.25-1.533 9.707 9.707 0 005.25 1.533 9.735 9.735 0 003.25-.555.75.75 0 00.5-.707V4.262a.75.75 0 00-.5-.707A9.735 9.735 0 0018 3a9.707 9.707 0 00-5.25 1.533A9.707 9.707 0 0011.25 4.533zM12.75 7.5a.75.75 0 00-1.5 0v6a.75.75 0 001.5 0V7.5z" />
                                        <path d="M7.5 15.375c0-.494.311-.947.766-1.158L9.75 13.5l-1.484-.717A1.5 1.5 0 017.5 11.25v-3.75a.75.75 0 011.5 0V11.25l1.484.717a.75.75 0 010 1.366L9 14.051V15.75a.75.75 0 01-1.5 0v-.375z" />
                                        <path d="M15 15.375c0-.494.311-.947.766-1.158L17.25 13.5l-1.484-.717a.75.75 0 01-.766-1.158.75.75 0 011.158-.766L17.25 11.25l1.484-.717a.75.75 0 01.766 1.158.75.75 0 01-.766 1.158L17.25 13.5l1.484.717a.75.75 0 010 1.366l-1.484.717-.001.001A1.502 1.502 0 0115 16.5v-1.125z" />
                                    </svg>
                                </div>
                                <h3 class="modal-title">开启通知，保持同步</h3>
                                <p class="modal-message">
                                    为了让 Evolve AI 更好地陪伴您的成长，我们建议您开启通知权限。
                                    <br><br>这样，您就能及时收到：<br>
                                    - 每日习惯提醒<br>
                                    - AI教练的鼓励与指导<br>
                                    - 灵感源泉的新内容推送
                                </p>
                                <div class="modal-button-group">
                                    <button class="modal-button secondary-button">以后再说</button>
                                    <button class="modal-button primary-button">去开启</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 脚本内容将在此处添加
    </script>
</body>
</html> 