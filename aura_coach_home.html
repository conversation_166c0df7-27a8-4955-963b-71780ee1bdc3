<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微光教练 - 页面预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #1a202c;
            padding: 30px;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        .mockup-gallery-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .mockup-unit {
            margin-bottom: 40px;
        }

        .mockup-page-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .iphone-mockup {
            width: 393px;
            height: 852px;
            background-color: #1c1c1e;
            border-radius: 50px;
            padding: 14px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4), 0 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #000;
            border-radius: 36px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .iphone-mockup::before {
            content: '';
            position: absolute;
            top: 22px;
            left: 50%;
            transform: translateX(-50%);
            width: 130px;
            height: 30px;
            background-color: #000;
            border-radius: 15px;
            z-index: 1010;
        }
        
        .iphone-mockup::after {
            content: '';
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 5px;
            background-color: rgba(200, 200, 200, 0.3);
            border-radius: 2.5px;
            z-index: 1001;
        }

        .ios-status-bar {
            height: 44px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 13px;
            font-weight: 500;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1005;
            padding-top: 12px;
        }
        .ios-status-bar .time { margin-left: 5px; }
        .ios-status-bar .signals { display: flex; align-items: center; gap: 5px; margin-right: 5px; }
        .ios-status-bar .signals svg { width: 16px; height: 16px; fill: white; }

        .app-container {
            flex-grow: 1;
            background-color: #002b20;
            background-image: 
                radial-gradient(ellipse at 15% 25%, rgba(0, 128, 128, 0.35) 0%, transparent 55%),
                radial-gradient(ellipse at 85% 60%, rgba(13, 71, 161, 0.25) 0%, transparent 65%),
                radial-gradient(circle at 20% 85%, rgba(56, 239, 125, 0.18) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 55%, rgba(0, 105, 92, 0.22) 0%, transparent 70%),
                radial-gradient(ellipse at 70% 80%, rgba(72, 181, 163, 0.25) 0%, transparent 60%),
                radial-gradient(ellipse at 30% 10%, rgba(13, 71, 161, 0.2) 0%, transparent 60%),
                linear-gradient(170deg, #0A2F51 0%, #005A4B 55%, #002b20 100%); 
            color: white;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            padding-top: 54px;
        }

        .page-visible-content {
            flex-grow: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        @keyframes float {
            0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.7; }
            50% { transform: translateY(-20px) translateX(10px) scale(1.1); opacity: 0.4; }
            100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.7; }
        }
        .light-particle {
            position: absolute;
            background-color: rgba(173, 216, 230, 0.3);
            border-radius: 50%;
            animation: float 10s infinite ease-in-out;
            pointer-events: none;
        }

        .top-area {
            padding: 20px 20px 20px 20px;
            position: relative;
            z-index: 1;
        }

        @keyframes breatheWisdomCoreShadow {
            0%, 100% { box-shadow: 0 0 15px 5px rgba(56,239,125,0.5), 0 0 25px 10px rgba(0,200,200,0.3); opacity: 0.8; }
            50% { box-shadow: 0 0 22px 10px rgba(56,239,125,0.75), 0 0 35px 18px rgba(0,200,200,0.45); opacity: 1; }
        }
        .wisdom-core {
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(56,239,125,0.8) 0%, rgba(0,200,200,0.7) 70%, rgba(0,128,128,0.5) 100%);
            border-radius: 50%;
            margin: 0 auto 10px auto;
            cursor: pointer;
            transition: transform 0.3s ease;
            animation: breatheWisdomCoreShadow 4s ease-in-out infinite;
        }
        .wisdom-core:hover {
            transform: scale(1.1);
        }
        .wisdom-core-subtitle {
            text-align: center;
            margin-top: 4px;
            font-size: 0.75rem;
            color: #AFEEEE;
        }

        .today-energy-module {
            padding: 0 20px 20px 20px;
            z-index: 1;
        }
        .energy-bar-container {
            background-color: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 4px;
            height: 30px;
            overflow: hidden;
            position: relative;
        }
        .energy-bar-fill {
            background: linear-gradient(90deg, #FF7F50, #FF6347, #40E0D0, #AFEEEE);
            height: 100%;
            border-radius: 12px;
            box-shadow: 0 0 8px rgba(255,107,71,0.5);
            position: relative;
            overflow: hidden;
        }

        @keyframes energyFlowAnimation {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .energy-bar-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.3) 40%,
                rgba(255, 255, 255, 0.5) 50%,
                rgba(255, 255, 255, 0.3) 60%,
                rgba(255, 255, 255, 0) 100%
            );
            border-radius: 12px;
            transform: translateX(-100%);
            animation: energyFlowAnimation 2.5s linear infinite;
            opacity: 0.8;
        }

        .habit-list {
            padding: 10px 20px;
            z-index: 1;
        }
        .habit-island {
            border-radius: 24px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2), 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
            display: flex;
            align-items: center;
            position: relative;
        }
        .habit-island:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow: 0 10px 30px rgba(0,0,0,0.25), 0 4px 15px rgba(0,0,0,0.15);
        }
        .habit-island.completed .habit-check-button {
            background-color: #FF8C00;
            border-color: #FF8C00;
        }
        .habit-island.completed .habit-check-button svg {
            opacity: 1;
        }
        .habit-icon-slot {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        .habit-icon-slot svg {
            width: 24px;
            height: 24px;
        }
        .habit-text-content { flex-grow: 1; }
        .habit-title { font-weight: 600; font-size: 1.0rem; line-height: 1.4; color: #ffffff; }
        .habit-subtitle { font-size: 0.75rem; color: #cbd5e1; line-height: 1.3; }
        .habit-check-button {
            width: 32px; height: 32px; border-radius: 50%; border: 2px solid #cbd5e1;
            display: flex; align-items: center; justify-content: center; cursor: pointer;
            transition: background-color 0.2s ease, border-color 0.2s ease; margin-left: 12px;
            background-color: rgba(255,255,255,0.1);
        }
        .habit-check-button svg {
            width: 20px; height: 20px; stroke-width: 2.5;
            color: white; opacity: 0; transition: opacity 0.2s ease;
            stroke-linecap: round; stroke-linejoin: round;
        }
        
        .theme-mindfulness { background-color: rgba(20, 184, 166, 0.25); }
        .theme-mindfulness .habit-icon-slot { background-color: rgba(245, 158, 11, 0.15); }
        .theme-mindfulness .habit-icon-slot svg { color: #F59E0B; }
        .theme-mindfulness.completed { background-color: rgba(20, 184, 166, 0.4); }
        .theme-stretch { background-color: rgba(59, 130, 246, 0.25); }
        .theme-stretch .habit-icon-slot { background-color: rgba(96, 165, 250, 0.15); }
        .theme-stretch .habit-icon-slot svg { color: #93c5fd; }
        .theme-stretch.completed { background-color: rgba(59, 130, 246, 0.4); }
        .theme-reading { background-color: rgba(133, 133, 84, 0.25); }
        .theme-reading .habit-icon-slot { background-color: rgba(234, 179, 8, 0.15); }
        .theme-reading .habit-icon-slot svg { color: #fde047; }
        .theme-reading.completed { background-color: rgba(133, 133, 84, 0.4); }

        .bottom-tab-nav {
            background-color: rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            padding: 10px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            z-index: 1000;
            margin-top: auto;
            display: flex;
            justify-content: space-around;
            align-items: center;
            width: 100%;
        }
        .tab-item {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 5px 0;
            cursor: pointer;
            color: #94a3b8;
            font-size: 0.75rem;
            transition: color 0.2s ease;
        }
        .tab-item:hover {
            color: #e2e8f0;
        }
        .tab-item svg {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            transition: transform 0.2s ease-in-out, filter 0.2s ease-in-out;
        }
        .tab-item.active {
            color: #AFEEEE;
        }
        .tab-item.active svg {
            filter: drop-shadow(0 0 5px #40E0D0);
            transform: scale(1.1);
            color: #AFEEEE;
        }
        .placeholder-page-content {
            padding: 20px;
            text-align: center;
            color: #cbd5e1;
        }
        .placeholder-page-content h2 {
            font-size: 1.75rem;
            font-weight: 600;
            color: white;
            margin-bottom: 12px;
        }
         .placeholder-page-content p {
             font-size: 1rem;
             line-height: 1.6;
         }
        .atlas-item, .auraspace-interaction, .me-stat, .me-badge-container {
            background-color: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .atlas-item h3 { color: #5eead4; }
        .auraspace-interaction textarea {
            width: 100%;
            height: 80px;
            background-color: rgba(0,0,0,0.2);
            color: white;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid rgba(255,255,255,0.1);
            resize: none;
        }
         .auraspace-interaction button {
             margin-top: 10px;
             background-color: #14b8a6;
             color: white;
             font-weight: 600;
             padding: 8px 16px;
             border-radius: 8px;
             transition: background-color 0.2s;
         }
         .auraspace-interaction button:hover { background-color: #0d9488; }
         .me-avatar { width: 70px; height: 70px; border-radius: 50%; margin-right: 15px; border: 2px solid #2dd4bf; }
         .me-stat p:first-child { font-size: 1.5rem; font-weight: 700; color: #2dd4bf; }

        .atlas-nav-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            height: 56px;
            flex-shrink: 0;
        }
        .atlas-nav-bar h1 {
            font-size: 17px;
            font-weight: 600;
            color: white;
            flex-grow: 1;
            text-align: center;
            margin-right: -38px;
        }
        .sow-new-button {
            background: none; border: none; padding: 5px; cursor: pointer;
            width: 38px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .sow-new-button svg {
            width: 28px; height: 28px;
            fill: #33FFDD;
            transition: transform 0.2s ease;
        }
        .sow-new-button:hover svg { transform: scale(1.1); }

        .eco-atlas-scene {
            flex-grow: 1;
            position: relative;
            padding: 20px;
            overflow-y: auto;
        }
        .eco-unit {
            position: absolute;
            width: 100px; height: 100px;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            background-color: rgba(255,255,255,0.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .eco-unit:hover {
            transform: scale(1.05) translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        .eco-visual {
            width: 40px; height: 40px; margin-bottom: 5px;
            display: flex; align-items: center; justify-content: center;
            font-size: 24px;
        }
        .eco-name { font-size: 12px; font-weight: 500; color: #e2e8f0; }
        .eco-days { font-size: 10px; color: #a0aec0; }

        .eco-unit.type-plant .eco-visual {
            background-color: #22c55e33; border-radius: 50%;
        }
        .eco-unit.type-crystal .eco-visual {
            background-color: #60a5fa33; border-radius: 8px; transform: rotate(45deg);
        }
        .eco-unit.type-star .eco-visual {
            background-color: #facc1533; border-radius: 50%;
        }

        .eco-unit.state-cultivating { border: 1px dashed #33FFDD80; }
        .eco-unit.state-mature { border: 2px solid #33FFDD; box-shadow: 0 0 15px #33FFDD60; }
        .eco-unit.state-dormant { opacity: 0.7; background-color: rgba(255,255,255,0.02); }
        .eco-unit.state-dormant .eco-visual { filter: grayscale(80%); }

        .tab-item.active .tab-icon,
        .tab-item.active span {
            color: #40E0D0; /* 荧光青色 */
            font-weight: 600;
        }

        .tab-item .tab-icon {
            width: 28px;
            height: 28px;
            margin-bottom: 2px;
            transition: color 0.3s ease, transform 0.2s ease;
        }
        .tab-item:hover .tab-icon {
            transform: scale(1.1);
        }

        /* 统一页面标题样式 */
        .page-title-text {
            font-size: 1.125rem; /* 18px, 可根据实际效果调整 */
            font-weight: 600;
            color: #e2e8f0; /* 浅色文字 */
            text-align: center; /* 居中 */
            padding: 10px 0;
        }
        
        /* 灵境空间 - AI教练页面特定样式 - 旧样式清理 */
        /* 
        #page-aura .page-header { ... }
        .aura-back-arrow svg { ... }
        .aura-page-title-center .wisdom-core-mini { ... }
        .aura-brand-text { ... }
        .user-bubble { ... } // Note: .user-bubble is redefined later with new styles
        .ai-bubble { ... } // Note: .ai-bubble related classes are redifined later
        .ai-bubble .wisdom-core-avatar { ... }
        .wisdom-treasury-icon svg { ... }
        .send-button svg { ... }
        .chat-history { ... } // Note: .chat-history is redefined later
        .chat-bubble { ... }
        // .user-bubble (already listed)
        // .ai-bubble (already listed)
        // .ai-bubble .wisdom-core-avatar (already listed)
        .ai-guidance-card { ... } // Note: .ai-guidance-card is redefined later
        .quick-reply-capsules { ... } // Note: .quick-reply-capsules is redefined later
        .quick-reply-capsule { ... } // Note: .quick-reply-capsule is redefined later
        .chat-input-area { ... } // Note: .chat-input-area related styles are handled by new classes or Tailwind
        .wisdom-treasury-icon { ... }
        .wisdom-treasury-icon svg { ... }
        .chat-input-area textarea { ... }
        .chat-input-area textarea::placeholder { ... }
        .send-button { ... } // Note: .send-button is redefined later
        .send-button svg { ... }
        .send-button:hover svg { ... }
        */

        /*灵境空间 - AI 教练页面特定优化 - 新增样式 START */
        #mockup-auraspace .app-container { /* Ensure padding-top is correctly set if status bar is overlayed */
            padding-top: 0; /* Reset if nav bar handles its own spacing */
        }

        #mockup-auraspace .page-visible-content {
            display: flex;
            flex-direction: column;
            height: 100%; 
        }

        #mockup-auraspace .page-header {
            position: sticky;
            top: 0; /* Stick to the top of the screen content area */
            z-index: 20; 
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem 0.75rem; /* 12px左右 */
            background-color: rgba(10, 20, 30, 0.6); /* Darker, slightly transparent for depth */
            backdrop-filter: blur(12px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }
        #mockup-auraspace .page-header .aura-brand-name {
            font-family: 'SF Pro Rounded', 'Arial Rounded MT Bold', 'Helvetica Rounded', Arial, sans-serif;
            letter-spacing: .01em;
        }

        #mockup-auraspace .chat-history {
            flex-grow: 1;
            overflow-y: auto;
            padding: 1rem; /* 16px */
            space-y: 1rem; /* 16px gap between messages */
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 128, 128, 0.5) transparent;
        }
        #mockup-auraspace .chat-history::-webkit-scrollbar {
            width: 6px;
        }
        #mockup-auraspace .chat-history::-webkit-scrollbar-track {
            background: transparent;
        }
        #mockup-auraspace .chat-history::-webkit-scrollbar-thumb {
            background-color: rgba(0, 128, 128, 0.5);
            border-radius: 3px;
        }

        .user-bubble {
            /* Tailwind: bg-gradient-to-br from-coral-500 to-orange-500 */
            border-top-left-radius: 1.25rem; 
            border-top-right-radius: 1.25rem;
            border-bottom-left-radius: 1.25rem;
            border-bottom-right-radius: 0.5rem; 
            padding: 0.75rem 1rem; /* 12px 16px */
            line-height: 1.5;
        }

        .ai-bubble-content {
            /* Tailwind: bg-gradient-to-br from-teal-600/80 via-cyan-600/70 to-sky-600/80 backdrop-blur-sm */
            border-top-left-radius: 1.25rem;
            border-top-right-radius: 1.25rem;
            border-bottom-right-radius: 1.25rem;
            border-bottom-left-radius: 0.5rem;
            padding: 0.75rem 1rem; /* 12px 16px */
            line-height: 1.5;
        }

        @keyframes pulseWisdomCoreMini {
            0%, 100% { box-shadow: 0 0 7px 2px rgba(56,239,125,0.4), 0 0 2px 1px rgba(173,255,230,0.6) inset; transform: scale(1); }
            50% { box-shadow: 0 0 10px 3px rgba(56,239,125,0.6), 0 0 4px 2px rgba(173,255,230,0.8) inset; transform: scale(1.05); }
        }
        .ai-avatar-icon {
             /* Tailwind: w-7 h-7 rounded-full bg-gradient-radial from-teal-400 to-cyan-600 flex-shrink-0 shadow-lg */
            animation: pulseWisdomCoreMini 3.5s infinite ease-in-out;
        }
        
        .ai-guidance-card {
            /* Tailwind: bg-slate-700/60 border border-teal-600/50 p-3 rounded-lg my-2 mx-auto max-w-[90%] text-center shadow */
            line-height: 1.5;
        }
        .quick-reply-capsule {
            /* Tailwind: bg-teal-500 hover:bg-teal-400 text-white text-xs px-4 py-1.5 rounded-full shadow transiton-all duration-200 */
            transition: transform 0.15s ease-out, background-color 0.15s ease-out;
        }
        .quick-reply-capsule:active {
            transform: scale(0.95);
        }


        #mockup-auraspace .chat-input-area {
            position: sticky;
            bottom: 0;
            z-index: 20;
             /* Tailwind: p-3 bg-black/50 backdrop-blur-md border-t border-gray-700/50 flex items-center gap-2 */
        }
        #auraChatInput {
            transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            min-height: 44px; 
            max-height: 120px; 
            line-height: 1.4;
        }
        #auraChatInput:focus {
            /* Tailwind: focus:ring-1 focus:ring-teal-500 focus:border-teal-500 */
        }
        #auraSendButton {
             /* Tailwind: p-2.5 rounded-full bg-teal-500 hover:bg-teal-400 active:bg-teal-600 transition-all duration-200 shadow-lg */
            transition: transform 0.15s ease-out, background-color 0.15s ease-out;
        }
        #auraSendButton:active {
            transform: scale(0.9);
        }
        /*灵境空间 - AI 教练页面特定优化 - 新增样式 END */

        /* 我的成就页面特定样式 */
        #page-me .page-header {
            /* 导航栏标题可以是用户昵称或"我的能量场" */
        }
        .me-profile-header {
            /* padding: 20px; /* Adjusted in HTML */
            /* text-align: center; /* Adjusted in HTML where needed */
            position: relative; 
        }
        .me-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid #40E0D0; 
            box-shadow: 0 0 15px 3px rgba(64, 224, 208, 0.7); 
            margin: 0 auto 10px; /* Ensured auto margins for centering if parent is block */
            background-color: #333; 
            background-size: cover;
            background-position: center;
        }
        .me-nickname {
            font-size: 1.25rem; /* 20px */
            font-weight: 600;
            color: #e2e8f0;
            margin-bottom: 5px;
        }
        .me-pro-badge {
            display: inline-block;
            background: linear-gradient(45deg, #FFD700, #FFA500); 
            color: #1A202C; 
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 0.7rem; /* 11.2px */
            font-weight: bold; /* Tailwind semibold is 600, bold is 700 */
            text-transform: uppercase;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.6); 
            border: 1px solid rgba(255,255,255,0.5);
        }
        .me-pro-star { /* 醒目Pro星芒 */
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2rem; /* 调整大小 */
            color: #FFD700; /* 金色 */
            filter: drop-shadow(0 0 10px #FFD700);
        }

        .me-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 0 20px 20px;
        }
        .me-stat-card {
            background-color: rgba(0, 128, 128, 0.15); 
            backdrop-filter: blur(8px);
            border-radius: 25px; 
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(64, 224, 208, 0.2); 
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .me-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 128, 128, 0.3);
        }
        .me-stat-card .stat-value {
            font-size: 1.75rem; /* 28px */
            font-weight: bold; /* Tailwind font-bold */
            margin-bottom: 5px;
            line-height: 1.2;
        }
        .me-stat-card .stat-label {
            font-size: 0.8rem; /* 12.8px */
            color: #cbd5e1; 
        }
        
        /* Pro会员卡片样式 */
        .me-pro-upsell-card {
            margin: 0; /* Removed margin: 20px to use padding on parent */
            padding: 25px;
            border-radius: 30px; /* 有机形态 */
            background: linear-gradient(135deg, rgba(255, 127, 80, 0.8) 0%, rgba(255, 99, 71, 0.7) 25%, rgba(64, 224, 208, 0.6) 75%, rgba(0, 200, 200, 0.7) 100%); /* 珊瑚粉与荧光青的动态流光渐变 */
            background-size: 200% 200%; /* For animation */
            position: relative;
            overflow: hidden; /* For pseudo-elements if used for animation */
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: left; /* Changed from center */
            animation: proCardFlow 8s ease-in-out infinite alternate; /* Adjusted timing */
        }
        
        @keyframes proCardFlow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }
        
        .me-pro-benefits {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .me-pro-benefit-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: white;
            font-size: 0.9rem;
        }
        
        .me-pro-upgrade-button {
            background: linear-gradient(90deg, #FF7F50, #FF6347); /* 能量色 */
            color: white;
            font-weight: bold;
            padding: 12px 30px;
            border-radius: 25px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(255,107,71,0.4);
            animation: breatheButton 2s ease-in-out infinite;
        }
        
        @keyframes breatheButton {
            0%, 100% { box-shadow: 0 4px 15px rgba(255,107,71,0.4); transform: scale(1); }
            50% { box-shadow: 0 4px 20px rgba(255,107,71,0.6); transform: scale(1.02); }
        }

        .me-badges-section {
            padding: 0 20px 20px;
        }
        .me-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #e2e8f0;
            margin-bottom: 15px;
            padding-left: 5px; /* Slight indent to align with cards */
        }
        .me-badges-container {
            background: linear-gradient(160deg, rgba(0, 128, 128, 0.2), rgba(64, 224, 208, 0.1) 70%, rgba(0,0,0,0.1)); /* 流动光带/星云背景 */
            border-radius: 20px;
            padding: 15px;
            display: flex;
            gap: 15px;
            overflow-x: auto; /* Allow horizontal scrolling for badges */
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .me-badges-container::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Chrome, Safari, Opera */
        }

        .me-badge-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 70px; /* Ensure badges are not too squeezed */
        }
        .me-badge-icon { /* 能量水晶、发光植物果实、星辰碎片等 */
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.1); /* Placeholder */
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 1.5rem; /* For emoji/text based icons */
            border: 2px solid transparent;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .me-badge-item:hover .me-badge-icon {
            transform: scale(1.1);
            box-shadow: 0 0 15px 3px var(--badge-glow-color, #40E0D0);
        }
        
        /* 设置列表样式 */
        .me-setting-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background-color 0.2s ease;
        }
        
        .me-setting-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .me-setting-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background-color: rgba(0, 128, 128, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .me-setting-icon svg {
            width: 22px;
            height: 22px;
            color: #4fd1c5; /* 定制化生态图标 */
        }
        
        .me-setting-name {
            flex-grow: 1;
            font-size: 0.95rem;
            color: #e2e8f0;
        }
        
        .me-setting-arrow {
            color: #a0aec0;
            font-size: 1.5rem;
            font-weight: 300;
        }

        /* Example badge styles (replace with actual SVG or complex CSS later) */
        .badge-crystal { background: linear-gradient(145deg, #8A2BE2, #4A00E0); border-color: #DA70D6; --badge-glow-color: #DA70D6; } /* Amethyst */
        .badge-glowing-fruit { background: linear-gradient(145deg, #FFD700, #FFA500); border-color: #FF8C00; --badge-glow-color: #FF8C00;} /* Orange */
        .badge-star-fragment { background: linear-gradient(145deg, #00BFFF, #1E90FF); border-color: #87CEEB; --badge-glow-color: #87CEEB;} /* Sky Blue */
        .badge-leaf-energy { background: linear-gradient(145deg, #32CD32, #228B22); border-color: #90EE90; --badge-glow-color: #90EE90;} /* Lime Green */
         .badge-cosmos-swirl { background: radial-gradient(circle, #483D8B, #2F2F4F, #1A1A3A); border-color: #9370DB; --badge-glow-color: #9370DB;} /* Dark Slate Blue / Purple */

        .me-badge-name {
            font-size: 0.7rem;
            color: #cbd5e1;
            text-align: center;
            white-space: nowrap;
        }

        .me-pro-upsell-card {
            margin: 0; 
            padding: 25px;
            border-radius: 30px; 
            background: linear-gradient(135deg, rgba(255, 127, 80, 0.8) 0%, rgba(255, 99, 71, 0.7) 25%, rgba(64, 224, 208, 0.6) 75%, rgba(0, 200, 200, 0.7) 100%); 
            background-size: 200% 200%; 
            position: relative;
            overflow: hidden; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: left; 
            animation: proCardFlow 8s ease-in-out infinite alternate; 
        }
        @keyframes proCardFlow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }
        .me-pro-benefits {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        .me-pro-benefit-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: white;
            font-size: 0.9rem;
        }
        .me-pro-benefit-item svg { 
            width: 22px;
            height: 22px;
            fill: white; 
            opacity: 0.9;
        }
        .me-pro-upgrade-button {
            background: linear-gradient(90deg, #FF7F50, #FF6347); 
            color: white;
            font-weight: bold;
            padding: 12px 30px;
            border-radius: 25px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(255,107,71,0.4);
            animation: breatheButton 2s ease-in-out infinite;
        }
        @keyframes breatheButton {
            0%, 100% { transform: scale(1); box-shadow: 0 4px 15px rgba(255,107,71,0.4); }
            50% { transform: scale(1.05); box-shadow: 0 6px 20px rgba(255,107,71,0.6); }
        }
        .me-pro-status-card { /* Pro用户状态卡片 */
            margin: 20px;
            padding: 20px;
            border-radius: 25px;
            background-color: rgba(0, 128, 128, 0.25); /* 更沉稳雅致 */
            backdrop-filter: blur(10px);
            border: 1px solid rgba(64, 224, 208, 0.3);
            text-align: center;
            color: #e2e8f0;
        }
        .me-pro-status-card h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #40E0D0; /* 荧光青 */
            margin-bottom: 8px;
        }
        .me-pro-status-card p {
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        .me-settings-list {
            margin: 20px;
        }
        .me-setting-item {
            display: flex;
            align-items: center;
            padding: 15px 15px; 
            border-bottom: 1px solid rgba(255,255,255,0.08);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .me-setting-item:hover {
            background-color: rgba(255,255,255,0.1); 
        }
        .me-setting-item:last-child {
            border-bottom: none;
        }
        .me-setting-icon svg { /* 定制化生态图标 */
            width: 24px;
            height: 24px;
            fill: #AFEEEE; /* Pale Turquoise, can be varied */
            margin-right: 15px;
        }
        .me-setting-name {
            flex-grow: 1;
            color: #cbd5e1;
            font-size: 0.95rem;
        }
        .me-setting-arrow {
            color: #718096; /* Cool Gray 500 */
            font-size: 1.2rem;
        }
    </style>
</head>
<body>

    <h1 class="mockup-gallery-title">微光教练 App 概念预览</h1>

    <div class="flex flex-wrap justify-center gap-x-8 gap-y-16">

        <div class="mockup-unit">
            <h2 id="title-today" class="mockup-page-title">首页 - 今日</h2>
            <div id="mockup-today" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:41</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <div class="page-visible-content">
                            <div class="light-particle" style="width: 5px; height: 5px; top: 10%; left: 15%; animation-duration: 12s; animation-delay: 0s;"></div>
                            <div class="light-particle" style="width: 8px; height: 8px; top: 30%; left: 80%; animation-duration: 15s; animation-delay: 2s;"></div>
                            <div class="light-particle" style="width: 6px; height: 6px; top: 60%; left: 10%; animation-duration: 10s; animation-delay: 1s;"></div>
                            <div class="light-particle" style="width: 7px; height: 7px; top: 80%; left: 70%; animation-duration: 13s; animation-delay: 3s;"></div>

                            <div class="top-area">
                                <p class="text-sm text-slate-300 tracking-wide font-light">6月23日，星期日</p>
                                <p class="text-xl font-normal text-white mt-1">晚上好，叶同学！</p>
                                <div class="wisdom-core mt-6 mb-4"></div>
                                <p class="wisdom-core-subtitle">点击探索智慧核心</p>
                            </div>

                            <div class="today-energy-module">
                                <h2 class="text-2xl font-semibold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-green-400" style="font-family: 'SF Pro Rounded', 'Arial Rounded MT Bold', 'Helvetica Rounded', Arial, sans-serif; letter-spacing: .02em;">
                                    今日能量
                                </h2>
                                <div class="energy-bar-container shadow-inner">
                                    <div class="energy-bar-fill" style="width: 70%;"></div>
                                </div>
                                <p class="text-right text-xs mt-1 text-slate-300">已完成 70%</p>
                            </div>

                            <div class="habit-list pb-5">
                                <div class="habit-island theme-mindfulness">
                                    <div class="habit-icon-slot">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.39-3.423 3.352c-.772.752-.297 2.075.752 2.267l4.34.628 1.953 4.275c.345.752 1.45.752 1.795 0l1.953-4.275 4.34-.628c1.05-.192 1.525-1.515.752-2.267l-3.423-3.352-4.753-.39-1.83-4.401Z" clip-rule="evenodd" /></svg>                                                              
                                    </div>
                                    <div class="habit-text-content">
                                        <h3 class="habit-title">清晨正念5分钟</h3>
                                        <p class="habit-subtitle">07:00</p>
                                    </div>
                                    <div class="habit-check-button">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" /></svg>
                                    </div>
                                </div>
                                <div class="habit-island theme-stretch">
                                    <div class="habit-icon-slot">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M2.879 8.143A4.502 4.502 0 0 0 6.5 4.5V3a.5.5 0 0 1 1 0v1.51c.175.02.348.05.518.086A3.5 3.5 0 0 1 11.5 8v.087c.195.03.387.07.574.12A2.5 2.5 0 0 1 14.5 11c.99 0 1.802.775 1.94 1.745A3.502 3.502 0 0 1 13.5 16H.5a.5.5 0 0 1 0-1h13a2.5 2.5 0 0 0 2.36-3.606 1.5 1.5 0 0 0-2.778-.967.5.5 0 0 1-.964-.253c-.046-.204-.1-.405-.158-.6H6.5a3.502 3.502 0 0 1-3.404-********* 0 0 1 .783-.6Z" clip-rule="evenodd" /></svg>                                                                                                                      
                                    </div>
                                    <div class="habit-text-content">
                                        <h3 class="habit-title">午后活力拉伸</h3>
                                        <p class="habit-subtitle">14:00</p>
                                    </div>
                                    <div class="habit-check-button">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" /></svg>
                                    </div>
                                </div>
                                <div class="habit-island theme-reading">
                                    <div class="habit-icon-slot">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M7.502 1.667C5.085 2.494 3.333 4.747 3.333 7.5c0 3.038 2.044 5.56 4.793 6.525CF5.625 14.075 4.167 15.55 4.167 17.5h11.666c0-1.95-1.458-3.425-4.042-3.975C14.623 13.06 16.667 10.538 16.667 7.5c0-2.753-1.752-4.995-4.165-5.833A2.5 2.5 0 0 0 10 0c-.77 0-1.459.347-1.921.833-.168.133-.325.272-.477.417Z" /></svg>                                                                                                            
                                    </div>
                                    <div class="habit-text-content">
                                        <h3 class="habit-title">睡前阅读</h3>
                                        <p class="habit-subtitle">21:30</p>
                                    </div>
                                    <div class="habit-check-button">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" /></svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bottom-tab-nav">
                            <a href="#page-today" class="tab-item active" data-mockup-target="mockup-today">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>今日</title><path fill="currentColor" d="M12 5c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-9c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4 4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>
                                <span>今日</span>
                            </a>
                            <a href="#page-atlas" class="tab-item" data-mockup-target="mockup-atlas">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>图鉴</title><path fill="currentColor" d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 15H8V7h8v10zm-1-8h-2v2H9v-2H7V8h2v1.5h2V8h2v1zm4-6H5V4h14v2zM12 10.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5m-3.55 3.17c-.52.2-.88.61-1.09.97C7.11 14.96 7 15.21 7 15.5c0 .27.09.52.25.74.23.32.58.56 1 .71.28.1.58.15.9.15.39 0 .76-.08 1.09-.23.52-.24.93-.66 1.16-1.1.16-.3.24-.63.24-.97s-.09-.68-.25-.98c-.22-.46-.62-.86-1.13-1.09-.31-.14-.65-.21-1-.21s-.69.07-1 .21z"/></svg>
                                <span>图鉴</span>
                            </a>
                            <a href="#page-aura" class="tab-item" data-mockup-target="mockup-auraspace">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>灵境</title><path fill="currentColor" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 3a7 7 0 100 14 7 7 0 000-14zm-1.52 9.436a.75.75 0 01.104-1.054 4.002 4.002 0 014.832 0 .75.75 0 11-.95 1.158 2.501 2.501 0 00-2.932 0 .75.75 0 01-1.054-.104zM12 7.5a1 1 0 110 2 1 1 0 010-2zm-3 3.5a1 1 0 110 2 1 1 0 010-2zm6 0a1 1 0 110 2 1 1 0 010-2z"/></svg>
                                <span>灵境</span>
                            </a>
                            <a href="#page-me" class="tab-item" data-mockup-target="mockup-me">
                                 <svg class="tab-icon" viewBox="0 0 24 24"> <title>我的</title><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2.01V20H6v-1.99c.2-.72 3.3-2.01 6-2.01M12 4C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
                                <span>我的</span>
                            </a>
                            </div>
                            </div>
                </div>
            </div>
        </div>

        <div class="mockup-unit">
            <h2 id="title-atlas" class="mockup-page-title">习惯图鉴</h2>
            <div id="mockup-atlas" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:41</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <div class="page-visible-content">
                            <div class="atlas-nav-bar">
                                <h1>习惯图鉴</h1>
                                <button class="sow-new-button" title="播种新元">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S12.41,20,12,20z\"/>
                                        <path d="M12 2a.75.75 0 01.75.75v.252a12.94 12.94 0 012.171.298 11.442 11.442 0 017.329 7.33 12.94 12.94 0 01.298 2.17V14.5a.75.75 0 01-1.5 0v-.252a12.94 12.94 0 01-.298-2.171 11.442 11.442 0 01-7.33-7.329A12.94 12.94 0 019.502 4V3.75A.75.75 0 0110.25 3h3.5a.75.75 0 010 1.5h-2.69a9.941 9.941 0 00-1.938 1.938A9.941 9.941 0 006.25 10.25v3.5a.75.75 0 01-1.5 0v-3.5a9.941 9.941 0 001.938-1.938A9.941 9.941 0 0010.25 6.25h1.008a.75.75 0 01.742.658 12.04 12.04 0 00.618 2.411.75.75 0 01-.568.877 12.03 12.03 0 00-1.66 1.15.75.75 0 11-.94-1.164 10.53 10.53 0 011.008 1.452.75.75 0 01.568.877 10.54 10.54 0 01-.618 2.411.75.75 0 01-.742.658H8a.75.75 0 01-.75-.75v-1.008a9.941 9.941 0 001.938-1.938A9.941 9.941 0 0011.75 12V8.25a.75.75 0 01.75-.75zm6.074 1.69a.75.75 0 10-1.148-.948 9.003 9.003 0 00-1.18 1.803.75.75 0 101.348.674c.302-.603.655-1.175 1.006-1.696l-.026.167zM5.074 6.24a.75.75 0 00-1.148-.948c-.47.758-.86 1.58-1.18 2.443a.75.75 0 101.348.674 9.002 9.002 0 011.006-2.002l-.026-.167zM17.76 19.074a.75.75 0 00.948-1.148 9.003 9.003 0 00-1.803-********** 0 10-.674 1.348c.603.302 1.175.655 1.696 1.006l-.167-.026zM6.24 17.76a.75.75 0 00.948-1.148 9.003 9.003 0 01-2.002-*********** 0 10-.674 1.348c.758.47 1.58.86 2.443 1.18l-.167-.026z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                            <div class="eco-atlas-scene">
                                <div class="light-particle" style="width: 7px; height: 7px; top: 15%; left: 75%; animation-duration: 13s; animation-delay: 0.5s;"></div>
                                <div class="light-particle" style="width: 5px; height: 5px; top: 50%; left: 20%; animation-duration: 11s; animation-delay: 1.5s;"></div>
                                
                                <div class="eco-unit type-plant state-cultivating" style="top: 30px; left: 40px; width: 110px; height:120px;">
                                    <div class="eco-visual">🌱</div>
                                    <p class="eco-name">清晨正念</p>
                                    <p class="eco-days">培育中: 5天</p>
                                </div>
                                <div class="eco-unit type-crystal state-mature" style="top: 80px; left: 180px; width: 90px; height:90px;">
                                    <div class="eco-visual">💎</div>
                                    <p class="eco-name">午后拉伸</p>
                                    <p class="eco-days">已成熟: 62天</p>
                                </div>
                                <div class="eco-unit type-star state-dormant" style="top: 200px; left: 70px; width: 120px; height:100px;">
                                    <div class="eco-visual">🌟</div>
                                    <p class="eco-name">睡前阅读</p>
                                    <p class="eco-days">休眠中</p>
                                </div>
                                 <div class="eco-unit type-plant state-mature" style="top: 250px; left: 200px; width: 100px; height:130px;">
                                    <div class="eco-visual">🌳</div>
                                    <p class="eco-name">健康饮食</p>
                                    <p class="eco-days">已结果: 90天</p>
                                </div>
                            </div>
                        </div>
                        <div class="bottom-tab-nav">
                            <a href="#page-today" class="tab-item active" data-mockup-target="mockup-today">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>今日</title><path fill="currentColor" d="M12 5c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-9c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4 4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>
                                <span>今日</span>
                            </a>
                            <a href="#page-atlas" class="tab-item" data-mockup-target="mockup-atlas">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>图鉴</title><path fill="currentColor" d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 15H8V7h8v10zm-1-8h-2v2H9v-2H7V8h2v1.5h2V8h2v1zm4-6H5V4h14v2zM12 10.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5m-3.55 3.17c-.52.2-.88.61-1.09.97C7.11 14.96 7 15.21 7 15.5c0 .27.09.52.25.74.23.32.58.56 1 .71.28.1.58.15.9.15.39 0 .76-.08 1.09-.23.52-.24.93-.66 1.16-1.1.16-.3.24-.63.24-.97s-.09-.68-.25-.98c-.22-.46-.62-.86-1.13-1.09-.31-.14-.65-.21-1-.21s-.69.07-1 .21z"/></svg>
                                <span>图鉴</span>
                            </a>
                            <a href="#page-aura" class="tab-item" data-mockup-target="mockup-auraspace">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>灵境</title><path fill="currentColor" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 3a7 7 0 100 14 7 7 0 000-14zm-1.52 9.436a.75.75 0 01.104-1.054 4.002 4.002 0 014.832 0 .75.75 0 11-.95 1.158 2.501 2.501 0 00-2.932 0 .75.75 0 01-1.054-.104zM12 7.5a1 1 0 110 2 1 1 0 010-2zm-3 3.5a1 1 0 110 2 1 1 0 010-2zm6 0a1 1 0 110 2 1 1 0 010-2z"/></svg>
                                <span>灵境</span>
                            </a>
                            <a href="#page-me" class="tab-item" data-mockup-target="mockup-me">
                                 <svg class="tab-icon" viewBox="0 0 24 24"> <title>我的</title><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2.01V20H6v-1.99c.2-.72 3.3-2.01 6-2.01M12 4C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
                                <span>我的</span>
                            </a>
                            </div>
                            </div>
                </div>
            </div>
        </div>

        <div class="mockup-unit">
            <h2 id="title-auraspace" class="mockup-page-title">灵境空间 - AI教练</h2>
            <div id="mockup-auraspace" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container" style="padding-top: 0;"> <!-- Removed padding-top from app-container -->
                        <div class="ios-status-bar">
                            <span class="time">9:41</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <!-- Top Navigation Bar - Moved here, directly under ios-status-bar -->
                        <div class="sticky top-[44px] z-10 flex items-center justify-between px-4 py-2"> <!-- Removed bg-black/20 backdrop-blur-md -->
                            <!-- Removed Back Button -->
                            <div class="w-7 h-7"></div> <!-- Placeholder for spacing, as back button is removed -->
                            <!-- New Central AI Core Element -->
                            <div class="flex flex-col items-center">
                                <div class="wisdom-core" style="width: 50px; height: 50px; margin: 0 auto 4px auto; background: radial-gradient(circle, rgba(56,239,125,0.9) 0%, rgba(0,200,200,0.8) 60%, rgba(0,128,128,0.6) 100%); box-shadow: 0 0 15px 5px rgba(56,239,125,0.5), 0 0 25px 10px rgba(0,200,200,0.3);"></div>
                                <!-- Removed text: <p class="text-teal-300 text-sm mt-1">灵光正在聆听...</p> -->
                            </div>
                            <button class="p-1 text-teal-400 hover:text-teal-300">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-7 h-7">
                                    <path fill-rule="evenodd" d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                        <div class="page-visible-content flex flex-col h-full" style="padding-top: 0;"> <!-- Removed padding-top, nav bar handles it -->
                            <!-- Content Area (Scrolling) -->
                            <div class="flex-grow overflow-y-auto px-4 pt-4 pb-2">
                            <div class="light-particle" style="width: 6px; height: 6px; top: 22%; left: 18%; animation-duration: 14s; animation-delay: 0.2s;"></div>
                            <div class="light-particle" style="width: 8px; height: 8px; top: 70%; left: 82%; animation-duration: 12s; animation-delay: 1.2s;"></div>

                                <!-- Introductory Text -->
                                <p class="text-center text-sm text-slate-300 mb-6 leading-relaxed mt-8">
                                    与您的AI微习惯教练"灵光"对话，获取个性化指导和支持。
                                </p>

                                <!-- AI Core Visual - This section will be removed -->
                                
                            
                                <!-- Chat History Area -->
                                <div class="chat-history flex-grow space-y-4 pr-1"> <!-- Reduced pr slightly -->
                                    <!-- AI Bubble -->
                                    <div class="flex justify-start">
                                        <div class="flex items-start gap-2 max-w-[85%]">
                                            <div class="ai-avatar-icon w-7 h-7 rounded-full bg-gradient-radial from-teal-400 to-cyan-600 flex-shrink-0 shadow-lg mt-1"></div>
                                            <div class="ai-bubble-content bg-teal-700 text-white shadow-md">
                                                <p class="text-sm"><strong class="font-semibold text-teal-200">灵光:</strong> 叶同学你好！今天有什么习惯培养上的困惑吗？或者有什么新的想法想和我聊聊？</p>
                            </div>
                        </div>
                            </div>
                                    <!-- User Bubble Placeholder -->
                                    <div class="flex justify-end">
                                        <div class="flex items-end gap-2 max-w-[85%]">
                                            <div class="user-bubble bg-gradient-to-br from-pink-500 via-red-500 to-orange-500 text-white shadow-md">
                                                <p class="text-sm">我今天想聊聊关于早起冥想的事情，感觉有点难以坚持。</p>
                            </div>
                            </div>
                            </div>
                                    <!-- AI Guidance Card Example -->
                                    <div class="ai-guidance-card my-4">
                                        <p class="text-teal-300 font-semibold mb-1 text-left">小提示：</p>
                                        <p class="text-slate-200 text-sm text-left">尝试将冥想融入清晨的例行公事中，比如刷牙后或喝第一杯水前，这样更容易形成习惯哦！</p>
                        </div>

                                    <!-- Quick Reply Capsules Example -->
                                    <div class="quick-reply-capsules flex flex-wrap justify-center gap-2 py-3">
                                        <button class="quick-reply-capsule">好的，我试试</button>
                                        <button class="quick-reply-capsule">还有其他建议吗？</button>
                                        <button class="quick-reply-capsule">我不想谈这个了</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Input Area - Sticks to bottom -->
                            <div class="chat-input-area sticky bottom-0 z-10 p-3 bg-black/50 backdrop-blur-md border-t border-slate-700/50 flex items-center gap-2">
                                <textarea 
                                    id="auraChatInput"
                                    class="flex-grow bg-black/10 text-slate-200 placeholder-slate-400 border border-slate-700/50 rounded-lg p-3 text-sm resize-none focus:ring-1 focus:ring-teal-500 focus:border-teal-500 min-h-[44px]"
                                    rows="1" 
                                    placeholder="在此输入您想对灵光说的话..."></textarea> <!-- Changed background to bg-black/10 -->
                                <button 
                                    id="auraSendButton"
                                    class="flex-shrink-0 w-11 h-11 bg-teal-500 hover:bg-teal-400 active:bg-teal-600 text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                                        <path d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="bottom-tab-nav">
                            <a href="#page-today" class="tab-item" data-mockup-target="mockup-today">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>今日</title><path fill="currentColor" d="M12 5c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-9c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4 4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>
                                <span>今日</span>
                            </a>
                            <a href="#page-atlas" class="tab-item" data-mockup-target="mockup-atlas">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>图鉴</title><path fill="currentColor" d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 15H8V7h8v10zm-1-8h-2v2H9v-2H7V8h2v1.5h2V8h2v1zm4-6H5V4h14v2zM12 10.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5m-3.55 3.17c-.52.2-.88.61-1.09.97C7.11 14.96 7 15.21 7 15.5c0 .27.09.52.25.74.23.32.58.56 1 .71.28.1.58.15.9.15.39 0 .76-.08 1.09-.23.52-.24.93-.66 1.16-1.1.16-.3.24-.63.24-.97s-.09-.68-.25-.98c-.22-.46-.62-.86-1.13-1.09-.31-.14-.65-.21-1-.21s-.69.07-1 .21z"/></svg>
                                <span>图鉴</span>
                            </a>
                            <a href="#page-aura" class="tab-item" data-mockup-target="mockup-auraspace">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>灵境</title><path fill="currentColor" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 3a7 7 0 100 14 7 7 0 000-14zm-1.52 9.436a.75.75 0 01.104-1.054 4.002 4.002 0 014.832 0 .75.75 0 11-.95 1.158 2.501 2.501 0 00-2.932 0 .75.75 0 01-1.054-.104zM12 7.5a1 1 0 110 2 1 1 0 010-2zm-3 3.5a1 1 0 110 2 1 1 0 010-2zm6 0a1 1 0 110 2 1 1 0 010-2z"/></svg>
                                <span>灵境</span>
                            </a>
                            <a href="#page-me" class="tab-item active" data-mockup-target="mockup-me">
                                 <svg class="tab-icon" viewBox="0 0 24 24"> <title>我的</title><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2.01V20H6v-1.99c.2-.72 3.3-2.01 6-2.01M12 4C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
                                <span>我的</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mockup-unit">
            <h2 id="title-me" class="mockup-page-title">我的成就</h2>
            <div id="mockup-me" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:41</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <div class="page-visible-content overflow-y-auto">
                            <div class="light-particle" style="width: 7px; height: 7px; top: 15%; left: 25%; animation-duration: 12s; animation-delay: 0.8s;"></div>
                            <div class="light-particle" style="width: 5px; height: 5px; top: 55%; left: 80%; animation-duration: 14s; animation-delay: 1.8s;"></div>

                            <!-- 页面顶部导航栏 -->
                            <div class="flex items-center justify-between px-4 py-3 sticky top-0 z-10 bg-opacity-20 bg-black/10 backdrop-blur-lg">
                                <div class="w-7 h-7"></div> <!-- 留空作为占位符，可能用于后退按钮或其他元素 -->
                                <h1 class="text-lg font-semibold text-white">我的能量场</h1> <!-- 修改标题 -->
                                <div class="w-7 h-7 flex items-center justify-center">
                                    <a href="#settings" title="设置" class="text-teal-300 hover:text-teal-200 transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6">
                                            <path fill-rule="evenodd" d="M11.49 3.17a.75.75 0 011.02.63l.16 1.034a.75.75 0 001.22.53l.936-.43a.75.75 0 01.908.239l.866 1.5a.75.75 0 01-.24.908l-.953.44a.75.75 0 00-.53 1.22l.16 1.034a.75.75 0 01-.63 1.02l-1.034.16a.75.75 0 00-.53 1.22l.43.936a.75.75 0 01-.239.908l-1.5.866a.75.75 0 01-.908-.24l-.43-.953a.75.75 0 00-1.22-.53l-1.034.16a.75.75 0 01-1.02-.63l-.16-1.034a.75.75 0 00-1.22-.53l-.936.43a.75.75 0 01-.908-.239l-.866-1.5a.75.75 0 01.24-.908l.953-.44a.75.75 0 00.53-1.22l-.16-1.034a.75.75 0 01.63-1.02l1.034-.16a.75.75 0 00.53-1.22l-.43-.936a.75.75 0 01.239-.908l1.5-.866a.75.75 0 01.908.24l.43.953a.75.75 0 001.22.53l1.034-.16zM10 8a2 2 0 100 4 2 2 0 000-4z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </div> <!-- 添加设置图标 -->
                            </div>

                            <!-- 顶部个人信息展示区 -->
                            <div class="me-profile-header relative pt-4 pb-6 px-5">
                                <div class="absolute inset-0 opacity-30" style="background: radial-gradient(ellipse at 50% 30%, rgba(0, 128, 128, 0.5) 0%, transparent 70%), radial-gradient(ellipse at 20% 70%, rgba(72, 181, 163, 0.3) 0%, transparent 60%), radial-gradient(ellipse at 80% 60%, rgba(56, 239, 125, 0.25) 0%, transparent 70%);"></div>
                                <div class="flex items-center relative z-10">
                                    <div class="me-avatar bg-teal-500 flex items-center justify-center text-3xl font-bold text-white" style="background-image: none;">
                                        Y <!-- 默认能量符号 -->
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="me-nickname text-xl">叶同学</h3>
                                        <span class="me-pro-badge !py-0.5 !px-2.5 !text-xs !font-semibold">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-3.5 h-3.5 inline-block mr-1 text-yellow-300 filter drop-shadow(0 0 3px #FFD700aa)"><path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.39-3.423 3.352c-.772.752-.297 2.075.752 2.267l4.34.628 1.953 4.275c.345.752 1.45.752 1.795 0l1.953-4.275 4.34-.628c1.05-.192 1.525-1.515.752-2.267l-3.423-3.352-4.753-.39-1.83-4.401Z" clip-rule="evenodd" /></svg>
                                            探索家 Lv.3
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- 核心数据与成就展示区 -->
                            <div class="px-5 space-y-5">
                                <!-- "我的能量场"卡片 -->
                                <div class="me-stat-card p-5">
                                    <h4 class="text-lg font-semibold text-teal-200 mb-3">我的能量场</h4>
                                    <div class="grid grid-cols-2 gap-4 text-center">
                                        <div>
                                            <p class="stat-value text-coral-400">28</p>
                                            <p class="stat-label text-sm">总坚持天数</p>
                                        </div>
                                        <div>
                                            <p class="stat-value text-coral-400">3</p>
                                            <p class="stat-label text-sm">当前习惯数</p>
                                        </div>
                                        <div>
                                            <p class="stat-value text-coral-400">85%</p>
                                            <p class="stat-label text-sm">平均完成率</p>
                                        </div>
                                        <div>
                                            <p class="stat-value text-coral-400">5</p>
                                            <p class="stat-label text-sm">已获徽章</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- "成就殿堂"卡片 -->
                                <div class="me-stat-card p-5 flex items-center gap-4 cursor-pointer hover:bg-teal-500/30 transition-colors duration-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-lime-400 filter drop-shadow(0 0 5px #a3e635aa)" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm0 14a1 1 0 01-1 1H6.707l-1.707-1.707A.997.997 0 015 15V8a1 1 0 112 0v6.586L8.414 16H9a1 1 0 011 1zm5-8a1 1 0 100-2 1 1 0 000 2zm-2 3a1 1 0 100-2 1 1 0 000 2zm2 3a1 1 0 100-2 1 1 0 000 2zM6 8a1 1 0 100-2 1 1 0 000 2z" opacity="0.6"/>
                                        <path fill-rule="evenodd" d="M6.161 3.03A1.5 1.5 0 017.54 2.19l.92-.531a1.5 1.5 0 011.513-.099l3.806 2.2a1.5 1.5 0 01.757 1.3V8.5a1.5 1.5 0 01-.44 1.06l-2.024 2.024a1.5 1.5 0 01-2.122 0L7.83 9.56A1.5 1.5 0 017.54 8.5V4.33a1.5 1.5 0 01-.622-1.203l-.757-.097zM10 7.5a.5.5 0 00-1 0v.09L8.28 8.31a.5.5 0 000 .707l1.286 1.286a.5.5 0 00.707 0L11.72 8.31a.5.5 0 000-.707L11 7.59V7.5z" clip-rule="evenodd" opacity="0.8"/>
                                        </svg>
                                    <div>
                                        <h4 class="text-lg font-semibold text-lime-300">成就殿堂</h4>
                                        <p class="text-sm text-slate-300">查看已解锁的徽章与荣耀</p>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-slate-400 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </div>
                            
                            <!-- AuraMind Pro 会员中心入口 -->
                            <div class="px-5 mt-6">
                                <div class="me-pro-upsell-card">
                                    <h3 class="text-xl font-bold text-white mb-1">解锁 Evolve Pro</h3> <!-- 修改Pro名称 -->
                                    <p class="text-sm text-white/80 mb-4">释放您的全部潜能</p>
                                    <ul class="me-pro-benefits space-y-2 mb-6">
                                        <li class="me-pro-benefit-item !justify-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-yellow-300"><path d="M10 3.5A1.5 1.5 0 0111.5 2h.001a1.5 1.5 0 011.494 1.306l.163 1.144a1.502 1.502 0 001.41 1.226h1.298a1.5 1.5 0 011.396 2.056l-.324 1.298a1.5 1.5 0 00.09 1.349l.742 1.113a1.5 1.5 0 01-.97 2.475h-1.343a1.501 1.501 0 00-1.247.685l-.774 1.16a1.5 1.5 0 01-2.248 0l-.774-1.16a1.501 1.501 0 00-1.247-.685H5.717a1.5 1.5 0 01-.97-2.475l.742-1.113a1.5 1.5 0 00.09-1.349l-.324-1.298A1.5 1.5 0 016.65 5.678h1.298c.766 0 1.433-.56 1.498-1.32L9.5 3.5A1.5 1.5 0 0110 3.5z"/></svg>
                                            <span>无限微元生态位</span>
                                        </li>
                                        <li class="me-pro-benefit-item !justify-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-sky-300"><path fill-rule="evenodd" d="M10 2c-1.717 0-3.403.344-4.973.994A9.004 9.004 0 002.996 7.966c-.094.439-.16.887-.197 1.34L2.5 12.5l.31.096c.493.153.999.274 1.518.362l.71.125C6.293 14.431 8.086 15 10 15c1.914 0 3.707-.569 4.973-1.592l.71-.125a12.078 12.078 0 001.518-.362l.31-.096.297-3.194c-.037-.453-.103-.901-.197-1.34a9.004 9.004 0 00-2.03-4.972A9.004 9.004 0 0010 2zM4.432 8.527a.75.75 0 00-1.028.098A7.502 7.502 0 002 12c0 1.284.32 2.49.886 3.547.334.622.855 1.145 1.595 ************.***************.***************.**************.678.278 1.038.373.*************.116.03A7.49 7.49 0 0010 18c1.247 0 2.43-.298 3.502-.828.04-.01.078-.02.117-.03.36-.095.708-.22 1.038-.373.029-.012.057-.024.085-.037.013-.006.026-.013.039-.019.74-.365 1.261-.888 1.595-1.51A7.502 7.502 0 0018 12a7.502 7.502 0 00-.804-3.375.75.75 0 00-1.028-.098A6.002 6.002 0 0110 16.5a6.002 6.002 0 01-6.4-7.352.75.75 0 00.832-.62z" clip-rule="evenodd"/></svg>
                                            <span>高级AI洞察与规划</span>
                                        </li>
                                        <li class="me-pro-benefit-item !justify-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-violet-300"><path d="M11.04 3.011a2.5 2.5 0 00-2.08-.002L3.702 5.719A2.5 2.5 0 002.5 7.992v4.016a2.5 2.5 0 001.202 2.273l5.258 2.708a2.5 2.5 0 002.08 0l5.258-2.708a2.5 2.5 0 001.202-2.273V7.992a2.5 2.5 0 00-1.202-2.273L11.04 3.011zM10 13.25a.75.75 0 00.75-.75V8.66l1.928-1.014a.75.75 0 00-.756-1.292L10 7.027l-1.922-1.014a.75.75 0 00-.756 1.292L9.25 8.66V12.5a.75.75 0 00.75.75z"/></svg>
                                            <span>专属Pro主题与徽章</span>
                                        </li>
                                    </ul>
                                    <button class="me-pro-upgrade-button w-full">
                                        立即升级 Evolve Pro <!-- 修改Pro名称 -->
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 设置列表区域已通过注释移除 -->
                            <!-- 
                            <div class="px-5 mt-6 pb-6">
                                <h4 class="text-lg font-semibold text-slate-200 mb-3">设置</h4>
                                <div class="bg-white/5 backdrop-blur-sm rounded-2xl">
                                    <div class="me-setting-item">
                                        <div class="me-setting-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-5.5-2.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zM10 12a5.99 5.99 0 00-4.793 2.39A6.483 6.483 0 0010 16.5a6.483 6.483 0 004.793-2.11A5.99 5.99 0 0010 12z" clip-rule="evenodd" /></svg>
                                        </div>
                                        <span class="me-setting-name">账户与安全</span>
                                        <span class="me-setting-arrow">&rsaquo;</span>
                                    </div>
                                    <div class="me-setting-item">
                                        <div class="me-setting-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" /></svg>
                                        </div>
                                        <span class="me-setting-name">通知管理</span>
                                        <span class="me-setting-arrow">&rsaquo;</span>
                                    </div>
                                    <div class="me-setting-item">
                                        <div class="me-setting-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M7 3.5A1.5 1.5 0 018.5 2h3A1.5 1.5 0 0113 3.5v2.75A2.25 2.25 0 0110.75 8.5h-1.5A2.25 2.25 0 017 6.25V3.5zm3.5 0v2.75a.75.75 0 01-.75.75h-1.5a.75.75 0 01-.75-.75V3.5a.5.5 0 01.5-.5h3a.5.5 0 01.5.5zM4.25 9.75A2.25 2.25 0 016.5 7.5h7A2.25 2.25 0 0115.75 9.75v4.5A2.25 2.25 0 0113.5 16.5h-7A2.25 2.25 0 014.25 14.25v-4.5z" /></svg>
                                        </div>
                                        <span class="me-setting-name">显示与主题</span>
                                        <span class="me-setting-arrow">&rsaquo;</span>
                                    </div>
                                    <div class="me-setting-item" style="border-bottom: none;">
                                        <div class="me-setting-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" /></svg>
                                        </div>
                                        <span class="me-setting-name">关于 AuraMind</span>
                                        <span class="me-setting-arrow">&rsaquo;</span>
                                    </div>
                                </div>
                            </div>
                            -->
                        </div>
                        <div class="bottom-tab-nav">
                            <a href="#page-today" class="tab-item" data-mockup-target="mockup-today">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>今日</title><path fill="currentColor" d="M12 5c-3.87 0-7 3.13-7 7s3.13 7 7 7 7-3.13 7-7-3.13-7-7-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-9c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4 4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>
                                <span>今日</span>
                            </a>
                            <a href="#page-atlas" class="tab-item" data-mockup-target="mockup-atlas">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>图鉴</title><path fill="currentColor" d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 15H8V7h8v10zm-1-8h-2v2H9v-2H7V8h2v1.5h2V8h2v1zm4-6H5V4h14v2zM12 10.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5.67-1.5 1.5-1.5m-3.55 3.17c-.52.2-.88.61-1.09.97C7.11 14.96 7 15.21 7 15.5c0 .27.09.52.25.74.23.32.58.56 1 .71.28.1.58.15.9.15.39 0 .76-.08 1.09-.23.52-.24.93-.66 1.16-1.1.16-.3.24-.63.24-.97s-.09-.68-.25-.98c-.22-.46-.62-.86-1.13-1.09-.31-.14-.65-.21-1-.21s-.69.07-1 .21z"/></svg>
                                <span>图鉴</span>
                            </a>
                            <a href="#page-aura" class="tab-item" data-mockup-target="mockup-auraspace">
                                <svg class="tab-icon" viewBox="0 0 24 24"> <title>灵境</title><path fill="currentColor" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 3a7 7 0 100 14 7 7 0 000-14zm-1.52 9.436a.75.75 0 01.104-1.054 4.002 4.002 0 014.832 0 .75.75 0 11-.95 1.158 2.501 2.501 0 00-2.932 0 .75.75 0 01-1.054-.104zM12 7.5a1 1 0 110 2 1 1 0 010-2zm-3 3.5a1 1 0 110 2 1 1 0 010-2zm6 0a1 1 0 110 2 1 1 0 010-2z"/></svg>
                                <span>灵境</span>
                            </a>
                            <a href="#page-me" class="tab-item active" data-mockup-target="mockup-me">
                                 <svg class="tab-icon" viewBox="0 0 24 24"> <title>我的</title><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2.01V20H6v-1.99c.2-.72 3.3-2.01 6-2.01M12 4C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
                                <span>我的</span>
                            </a>
                            </div>
                            </div>
                </div>
            </div>
                            </div>

    </div>

    <script>
        document.querySelectorAll('.wisdom-core').forEach(core => {
            core.addEventListener('click', () => {
                core.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    core.style.transform = 'scale(1.1)';
                }, 150);
                 setTimeout(() => {
                    core.style.transform = 'scale(1)';
                }, 300);
            });
        });

        document.querySelectorAll('.habit-island').forEach(island => {
            const checkButton = island.querySelector('.habit-check-button');
            const toggleCompletion = () => {
                island.classList.toggle('completed');
                // Simpler: rely on CSS for completed state styling
                if (island.classList.contains('completed')) {
                    console.log(`Habit "${island.querySelector('.habit-title').textContent}" marked as complete!`);
                } else {
                    console.log(`Habit "${island.querySelector('.habit-title').textContent}" marked as incomplete.`);
                }
            };
            if (checkButton) {
                checkButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    toggleCompletion();
                });
            }
        });

        // Function to handle tab activation and scrolling
        function activateTabAndScroll(targetMockupId, clickedTabElement) {
            // Deactivate all tabs in all mockups first
            document.querySelectorAll('.tab-item.active').forEach(activeTab => {
                activeTab.classList.remove('active');
                // Reset icon and text color for inactive tabs
                const icon = activeTab.querySelector('.tab-icon');
                if (icon) {
                    icon.style.color = ''; // Reset to default CSS color
                    icon.style.filter = '';
                    icon.style.transform = '';
                }
                 const span = activeTab.querySelector('span');
                 if (span) {
                    span.style.color = ''; // Reset to default CSS color
                     span.style.fontWeight = '';
                 }
            });

            // Activate the clicked tab (and its counterparts in other mockups)
            document.querySelectorAll(`.tab-item[data-mockup-target="${targetMockupId}"]`).forEach(tabToActivate => {
                tabToActivate.classList.add('active');
                 // Apply active styles directly for immediate visual feedback
                const icon = tabToActivate.querySelector('.tab-icon');
                if (icon) {
                    icon.style.color = '#AFEEEE'; //荧光青色 or from CSS: #40E0D0
                    icon.style.filter = 'drop-shadow(0 0 5px #40E0D0)';
                    icon.style.transform = 'scale(1.1)';
                }
                 const span = tabToActivate.querySelector('span');
                 if (span) {
                     span.style.color = '#AFEEEE'; //荧光青色 or from CSS: #40E0D0
                     span.style.fontWeight = '600';
                 }
            });
            
                    const targetMockupElement = document.getElementById(targetMockupId);
                    const targetTitleElement = document.getElementById('title-' + targetMockupId.split('-')[1]);
                    let elementToScrollTo = targetMockupElement;

                    if (targetTitleElement) {
                       elementToScrollTo = targetTitleElement;
                    }
                    
                    if (elementToScrollTo) {
                        elementToScrollTo.scrollIntoView({ 
                            behavior: 'smooth', 
                    block: 'start', // Aligns to the top of the element
                            inline: 'nearest'
                        });
                    } else {
                        console.error('Target mockup or title not found for scrolling:', targetMockupId);
                    }
        }

        // Add click listeners to all tab items
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', (event) => {
                event.preventDefault();
                const targetMockupId = tab.dataset.mockupTarget;
                if (targetMockupId) {
                    activateTabAndScroll(targetMockupId, tab);
                }
            });
        });

        // Initial active tab setup for "Today" page on load
        // Find the first "Today" tab and activate it.
        const initialTodayTab = document.querySelector('.tab-item[data-mockup-target="mockup-today"]');
        if(initialTodayTab){
            // activateTabAndScroll("mockup-today", initialTodayTab); // Commented out to prevent auto-scroll on load
             // Manually set the first 'Today' tab as active without scrolling
            document.querySelectorAll('.tab-item[data-mockup-target="mockup-today"]').forEach(tabToActivate => {
                tabToActivate.classList.add('active');
                const icon = tabToActivate.querySelector('.tab-icon');
                if (icon) { icon.style.color = '#AFEEEE'; icon.style.filter = 'drop-shadow(0 0 5px #40E0D0)'; icon.style.transform = 'scale(1.1)';}
                const span = tabToActivate.querySelector('span');
                if (span) { span.style.color = '#AFEEEE'; span.style.fontWeight = '600';}
            });
        }

        // Initial active tab setup
        function setInitialActiveTab() {
            const currentHash = window.location.hash;
            let targetMockupId = "mockup-today"; // Default to today
            
            // Determine targetMockupId based on hash or visible mockup
            if (currentHash === "#page-me") {
                targetMockupId = "mockup-me";
            } else if (currentHash === "#page-atlas") {
                targetMockupId = "mockup-atlas";
            } else if (currentHash === "#page-aura") {
                targetMockupId = "mockup-auraspace";
            } else if (currentHash === "#page-today") {
                targetMockupId = "mockup-today";
            } else {
                // Fallback: If no hash or unrecognized hash, try to find the visible mockup
                // This part assumes only one mockup is fully visible or primarily in view if multiple exist
                // For this specific single-html-file setup, the "active" class on a tab is the best indicator if no hash.
                const activeMockupTab = document.querySelector('.bottom-tab-nav .tab-item.active');
                if (activeMockupTab && activeMockupTab.dataset.mockupTarget) {
                    targetMockupId = activeMockupTab.dataset.mockupTarget;
                }
            }

            const activeTabButton = document.querySelector(`.bottom-tab-nav .tab-item[data-mockup-target="${targetMockupId}"]`);
            
            if (activeTabButton) {
                document.querySelectorAll('.tab-item.active').forEach(activeTab => {
                    activeTab.classList.remove('active');
                    const icon = activeTab.querySelector('.tab-icon');
                    if (icon) { icon.style.color = ''; icon.style.filter = ''; icon.style.transform = ''; }
                    const span = activeTab.querySelector('span');
                    if (span) { span.style.color = ''; span.style.fontWeight = ''; }
                });
                
                activeTabButton.classList.add('active');
                const icon = activeTabButton.querySelector('.tab-icon');
                if (icon) { icon.style.color = '#AFEEEE'; icon.style.filter = 'drop-shadow(0 0 5px #40E0D0)'; icon.style.transform = 'scale(1.1)'; }
                const span = activeTabButton.querySelector('span');
                if (span) { span.style.color = '#AFEEEE'; span.style.fontWeight = '600'; }
                
                const titleElement = document.getElementById('title-' + targetMockupId.split('-')[1]);
                if (titleElement && window.manualNav === true) { // Only scroll if navigated manually by click
                    titleElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
                }
            }
        }
        window.manualNav = false; 

        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', (event) => {
                event.preventDefault();
                const targetMockupId = tab.dataset.mockupTarget;
                if (targetMockupId) {
                    window.manualNav = true; 
                    activateTabAndScroll(targetMockupId, tab); 
                    window.location.hash = `#page-${targetMockupId.split('-')[1]}`;
                    window.manualNav = false; // Reset after scroll attempt in activateTabAndScroll
                }
            });
        });
        
        window.addEventListener('DOMContentLoaded', () => {
            setInitialActiveTab(); // Set active tab based on initial hash or default
            // Add a slight delay for scrolling if it's a direct navigation to a hash, 
            // ensuring layout is stable.
            if (window.location.hash) {
                setTimeout(() => {
                    const targetMockupIdFromHash = window.location.hash.substring(1).replace("page-", "mockup-");
                    const titleElement = document.getElementById('title-' + targetMockupIdFromHash.split('-')[1]);
                    if (titleElement) {
                        titleElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
                    }
                }, 100); // 100ms delay
            }
        });
        window.addEventListener('hashchange', setInitialActiveTab);

    </script>

</body>
</html> 