<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图鉴子页面-习惯设定与详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #1a202c; /* 深蓝灰色背景 */
            padding: 30px;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        .mockup-gallery-title {
            text-align: center;
            font-size: 2.5rem; /* 40px */
            font-weight: 700;
            color: #e2e8f0; /* Tailwind slate-200 */
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .mockup-unit {
            margin-bottom: 40px;
        }

        .mockup-page-title { /* 用于模拟器上方的页面总标题 */
            text-align: center;
            font-size: 1.5rem; /* 24px */
            font-weight: 600;
            color: #cbd5e1; /* Tailwind slate-300 */
            margin-bottom: 20px;
        }

        .iphone-mockup {
            width: 393px; /* iPhone 14/15 Pro 逻辑宽度 */
            height: 852px; /* iPhone 14/15 Pro 逻辑高度 */
            background-color: #1c1c1e; /* 深灰色，接近iOS暗模式边框 */
            border-radius: 50px; /* iPhone圆角 */
            padding: 14px; /* 模拟边框厚度 */
            box-shadow: 0 20px 50px rgba(0,0,0,0.4), 0 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #000; /* 纯黑屏幕背景 */
            border-radius: 36px; /* 屏幕圆角 */
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* 确保所有内部内容不会水平溢出 */
        .iphone-screen *, .app-container *, .page-visible-content * {
            max-width: 100%;
            box-sizing: border-box;
        }

        /* 模拟灵动岛 */
        .iphone-mockup::before {
            content: '';
            position: absolute;
            top: 22px; /* 灵动岛距离顶部的位置 */
            left: 50%;
            transform: translateX(-50%);
            width: 130px; /* 灵动岛宽度 */
            height: 30px; /* 灵动岛高度 */
            background-color: #000; /* 灵动岛颜色 */
            border-radius: 15px; /* 灵动岛圆角 */
            z-index: 1010; /* 确保在屏幕内容之上 */
        }
        
        /* 模拟底部指示条 (可选，如果需要更逼真) */
        .iphone-mockup::after {
            content: '';
            position: absolute;
            bottom: 20px; /* 指示条距离底部的位置 */
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 5px;
            background-color: rgba(200, 200, 200, 0.3); /* 半透明指示条 */
            border-radius: 2.5px;
            z-index: 1001;
        }

        .ios-status-bar {
            height: 44px; /* iOS状态栏标准高度 */
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 13px;
            font-weight: 500;
            position: absolute; /* 相对于iphone-screen定位 */
            top: 0;
            left: 0;
            right: 0;
            z-index: 1005; /* 在灵动岛之下，但在页面内容之上 */
            padding-top: 12px; /* 为灵动岛留出垂直空间感 */
        }
        .ios-status-bar .time { margin-left: 5px; }
        .ios-status-bar .signals { display: flex; align-items: center; gap: 5px; margin-right: 5px; }
        .ios-status-bar .signals svg { width: 16px; height: 16px; fill: white; }

        .app-container {
            flex-grow: 1;
            /* 主题背景色和渐变 - 参考 aura_coach_home.html */
            background-color: #002b20; /* 深青色基底 */
            background-image: 
                radial-gradient(ellipse at 15% 25%, rgba(0, 128, 128, 0.35) 0%, transparent 55%),
                radial-gradient(ellipse at 85% 60%, rgba(13, 71, 161, 0.25) 0%, transparent 65%),
                radial-gradient(circle at 20% 85%, rgba(56, 239, 125, 0.18) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 55%, rgba(0, 105, 92, 0.22) 0%, transparent 70%),
                radial-gradient(ellipse at 70% 80%, rgba(72, 181, 163, 0.25) 0%, transparent 60%),
                radial-gradient(ellipse at 30% 10%, rgba(13, 71, 161, 0.2) 0%, transparent 60%),
                linear-gradient(170deg, #0A2F51 0%, #005A4B 55%, #002b20 100%); 
            color: white;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 防止内容溢出app-container */
            position: relative; /* 用于内部绝对定位元素如光点 */
            padding-top: 54px; /* 为iOS状态栏和可能的顶部导航栏预留空间 */
            width: 100%; /* 确保宽度为100% */
            max-width: 100%; /* 确保不超过容器宽度 */
        }

        /* 页面主要内容区域，可滚动 */
        .page-visible-content {
            flex-grow: 1;
            overflow-y: auto; /* 允许垂直滚动 */
            overflow-x: hidden; /* 防止水平滚动 */
            display: flex;
            flex-direction: column; /* 内容垂直排列 */
            width: 100%;
            max-width: 100%; /* 确保内容不超过容器宽度 */
            -webkit-overflow-scrolling: touch; /* 平滑滚动效果 */
            overscroll-behavior: none; /* 避免下拉刷新和反弹效果 */
        }

        /* 顶部导航栏通用样式 */
        .page-top-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 16px; /* 内边距 */
            height: 56px; /* 固定高度 */
            position: sticky; /* 吸顶效果 */
            top: 0; /* 吸附在.page-visible-content的顶部 */
            z-index: 100; /* 确保在滚动内容之上 */
        }
        .page-top-nav .nav-title {
            font-size: 1.125rem; /* 18px */
            font-weight: 600;
            color: #e2e8f0;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .page-top-nav .nav-button {
            padding: 6px;
            color: #94a3b8; /* Tailwind slate-400 */
            background: none;
            border: none;
            cursor: pointer;
            transition: color 0.2s ease;
        }
        .page-top-nav .nav-button:hover {
            color: #e2e8f0; /* Tailwind slate-200 */
        }
        .page-top-nav .nav-button svg {
            width: 24px;
            height: 24px;
        }

        /* 表单和输入元素的基本样式 */
        .form-group { 
            margin-bottom: 20px; 
            width: 100%;
            max-width: 100%;
        }
        .form-label {
            display: block;
            font-size: 0.9rem; /* 14.4px */
            color: #cbd5e1; /* Tailwind slate-300 */
            margin-bottom: 8px;
            font-weight: 500;
        }
        .form-input, .form-textarea, .form-select {
            width: 100%;
            max-width: 100%; /* 确保不超过容器 */
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.05); /* 非常淡的背景 */
            border: 1px solid rgba(255, 255, 255, 0.15); /* 细边框 */
            border-radius: 12px; /* 圆角 */
            color: #e2e8f0; /* Tailwind slate-200 */
            font-size: 1rem; /* 16px */
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            box-sizing: border-box; /* 确保padding不会增加元素宽度 */
        }
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: #40E0D0; /* 荧光青强调色 */
            box-shadow: 0 0 0 2px rgba(64, 224, 208, 0.3); /* 模拟光晕 */
        }
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        /* 主行动按钮 */
        .primary-action-button {
            display: block;
            width: 100%;
            padding: 14px 20px;
            background: linear-gradient(90deg, #FF7F50, #FF6347); /* 温暖的珊瑚粉/日落橙 */
            color: white;
            font-weight: 600;
            font-size: 1rem;
            border: none;
            border-radius: 16px; /* 更大的圆角 */
            cursor: pointer;
            text-align: center;
            transition: transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(255,107,71,0.3); /* 按钮阴影 */
        }
        .primary-action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,107,71,0.4);
        }
        .primary-action-button:active {
            transform: translateY(0px);
            box-shadow: 0 3px 10px rgba(255,107,71,0.3);
        }
        
        /* 次要/操作按钮组 */
        .actions-group {
            display: flex;
            gap: 12px; /* 按钮间距 */
            margin-top: 24px;
        }
        .secondary-action-button {
            flex-grow: 1;
            padding: 10px 15px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #cbd5e1;
            font-weight: 500;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease, border-color 0.2s ease;
        }
        .secondary-action-button:hover {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }
        .secondary-action-button.danger { /* 用于删除等危险操作 */
            border-color: rgba(255, 107, 71, 0.5);
            color: #FF7F50;
        }
        .secondary-action-button.danger:hover {
            background-color: rgba(255, 107, 71, 0.1);
            border-color: #FF7F50;
        }

        /* 图标选择器样式 */
        .icon-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr)); /* 响应式网格 */
            gap: 12px;
            padding: 10px;
            background-color: rgba(255,255,255,0.03);
            border-radius: 12px;
        }
        .icon-option {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            aspect-ratio: 1 / 1; /* 保持正方形 */
            background-color: rgba(255,255,255,0.08);
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }
        .icon-option svg {
            width: 32px; /* 图标大小 */
            height: 32px;
            fill: #94a3b8; /* 默认图标颜色 */
            transition: fill 0.2s ease;
        }
        .icon-option:hover {
            background-color: rgba(255,255,255,0.12);
            border-color: rgba(64, 224, 208, 0.3);
        }
        .icon-option.selected {
            border-color: #40E0D0; /* 荧光青强调色 */
            background-color: rgba(64, 224, 208, 0.1);
        }
        .icon-option.selected svg {
            fill: #40E0D0; /* 选中时图标颜色 */
        }

        /* 习惯详情页特定样式 */
        .habit-main-image-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 6px; /* 从 8px 进一步减小 */
            background: radial-gradient(ellipse at center, rgba(0, 128, 128, 0.2) 0%, transparent 70%); /* 背景光晕 */
            border-radius: 20px;
            margin-bottom: 10px; /* 从 12px 进一步减小 */
        }
        .habit-main-image-container svg { /* 放大的生态主形象 */
            width: 72px; /* 从 80px 减小 */
            height: 72px; /* 从 80px 减小 */
            fill: #40E0D0; /* 荧光青 */
            filter: drop-shadow(0 0 15px rgba(64, 224, 208, 0.5)); /* 光晕效果 */
        }

        .growth-calendar {
            display: grid;
            grid-template-columns: repeat(7, 1fr); /* 7列代表一周 */
            gap: 5px; /* 日期间隔从6px减小 */
            padding: 8px; /* 从10px减小 */
            background-color: rgba(255,255,255,0.03);
            border-radius: 12px;
            margin-bottom: 16px; /* 从20px减小 */
        }
        .calendar-day {
            aspect-ratio: 1 / 1;
            border-radius: 6px;
            background-color: rgba(255,255,255,0.08); /* 默认日期格子背景 */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem; /* 日期数字大小 */
            color: #718096; /* 默认日期数字颜色 */
        }
        .calendar-day.completed { /* 完成的日期 */
            background-color: rgba(64, 224, 208, 0.4); /* 荧光青半透明 */
            color: #e2e8f0;
        }
        .calendar-day.missed { /* 未完成的日期 */
            background-color: rgba(255, 107, 71, 0.2); /* 珊瑚粉/橙色 半透明 */
        }
        .calendar-day.current { /* 当天 */
             border: 1.5px solid #40E0D0;
        }

        .stats-summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr); /* 两列数据 */
            gap: 16px;
            margin-bottom: 20px;
        }
        .stat-item {
            background-color: rgba(255,255,255,0.05);
            padding: 16px;
            border-radius: 12px;
            text-align: center;
        }
        .stat-item .value {
            font-size: 1.75rem; /* 28px */
            font-weight: 700;
            color: #40E0D0; /* 荧光青 */
            margin-bottom: 4px;
        }
        .stat-item .label {
            font-size: 0.8rem; /* 12.8px */
            color: #cbd5e1; /* Tailwind slate-300 */
        }

        .ai-notes-card {
            background-color: rgba(0, 128, 128, 0.1); /* 深青色半透明 */
            padding: 16px;
            border-radius: 12px;
            border: 1px solid rgba(64, 224, 208, 0.2); /* 荧光青弱边框 */
            margin-bottom: 24px;
        }
        .ai-notes-card .title {
            font-size: 1rem;
            font-weight: 600;
            color: #5eead4; /* Tailwind teal-300 */
            margin-bottom: 8px;
        }
        .ai-notes-card .content {
            font-size: 0.9rem;
            color: #cbd5e1;
            line-height: 1.6;
        }
        
        /* 对话气泡样式 */
        .chat-bubble {
            padding: 10px 15px;
            border-radius: 18px;
            margin-bottom: 10px;
            max-width: 80%;
            line-height: 1.5;
            font-size: 0.95rem;
        }
        .ai-bubble {
            background-color: rgba(0, 128, 128, 0.2); /* AI气泡背景 */
            color: #e2e8f0;
            border-bottom-left-radius: 4px; /* AI气泡特色角 */
            align-self: flex-start;
        }
        .user-input-area {
            margin-top: auto; /* 将输入区域推到底部 */
            padding: 16px;
            background-color: rgba(0,0,0,0.2);
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        /* 自定义滚动条样式 */
        /* 对WebKit浏览器 */
        .page-visible-content::-webkit-scrollbar {
            width: 2px; /* 更细的滚动条 */
            background-color: transparent;
        }
        .page-visible-content::-webkit-scrollbar-thumb {
            background-color: rgba(64, 224, 208, 0.2); /* 更透明 */
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }
        .page-visible-content:hover::-webkit-scrollbar-thumb {
            background-color: rgba(64, 224, 208, 0.4); /* 悬停时稍微明显 */
        }
        /* 隐藏水平滚动条 */
        .page-visible-content::-webkit-scrollbar:horizontal {
            display: none;
        }
        /* 对Firefox */
        .page-visible-content {
            scrollbar-width: none; /* 完全隐藏Firefox滚动条 */
            scrollbar-color: rgba(64, 224, 208, 0.2) transparent;
        }
        
        /* 隐藏所有可能的滚动条 */
        .iphone-screen, .app-container {
            -ms-overflow-style: none; /* IE and Edge */
            scrollbar-width: none; /* Firefox */
        }
        .iphone-screen::-webkit-scrollbar, .app-container::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

    </style>
</head>
<body>

    <h1 class="mockup-gallery-title">图鉴子页面-习惯设定与详情</h1>

    <div class="flex flex-wrap justify-center gap-x-8 gap-y-16">

        <!-- 第一个iPhone模拟器: 添加/编辑习惯页面 -->
        <div class="mockup-unit">
            <h2 id="title-sow-seed" class="mockup-page-title">播种新元</h2>
            <div id="mockup-sow-seed" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:41</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        
                        <div class="page-top-nav">
                            <button class="nav-button">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <h1 class="nav-title" id="sow-seed-page-title">播种新元</h1>
                            <div class="w-10 h-10"></div> <!-- 右侧占位，保持标题居中 -->
                        </div>

                        <div class="page-visible-content p-5 space-y-6">
                            <!-- AI引导式对话 (简化为表单优先) -->
                            <div class="form-group">
                                <label for="habit-name" class="form-label">习惯名称 (你的微元想要叫什么？)</label>
                                <input type="text" id="habit-name" name="habit-name" class="form-input" placeholder="例如：清晨正念5分钟">
                            </div>

                            <div class="form-group">
                                <label class="form-label">为它选择一个生命象征 (生态图标)</label>
                                <div class="icon-selector">
                                    <!-- 示例图标，实际应使用SVG -->
                                    <div class="icon-option selected" data-icon-name="plant_seedling">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M10.5 3.5a2.502 2.502 0 00-5 0V4h5v-.5zM15.5 4H13V3.5a4.502 4.502 0 00-9 0V4H1.5a.5.5 0 00-.5.5v1a.5.5 0 00.5.5H2v8.5a3.5 3.5 0 003.5 3.5h9a3.5 3.5 0 003.5-3.5V6h.5a.5.5 0 00.5-.5v-1a.5.5 0 00-.5-.5zM5.5 14.5A1.5 1.5 0 014 13V6h1.5v8.5zM16 13a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 13V6h12v7zM7.508 8.013A.5.5 0 017.013 7.5a.5.5 0 01.495-.52.5.5 0 01.495.52.5.5 0 01-.495.493zm5 0a.5.5 0 01-.495-.52.5.5 0 01.495-.52.5.5 0 01.495.52.5.5 0 01-.495.493z"/></svg>                                        
                                    </div>
                                    <div class="icon-option" data-icon-name="water_drop">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8.836 2.406a1.5 1.5 0 012.328 0l5.75 6.205a1.5 1.5 0 01-.092 2.09l-4.959 4.549a1.5 1.5 0 01-2.133 0L3.824 10.7A1.5 1.5 0 013.732 8.61L8.836 2.406zM10 6.061L6.737 9.524l3.263 2.99 3.263-2.99L10 6.06z" clip-rule="evenodd" /></svg>
                                    </div>
                                    <div class="icon-option" data-icon-name="star_shine">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.39-3.423 3.352c-.772.752-.297 2.075.752 2.267l4.34.628 1.953 4.275c.345.752 1.45.752 1.795 0l1.953-4.275 4.34-.628c1.05-.192 1.525-1.515.752-2.267l-3.423-3.352-4.753-.39-1.83-4.401Z" clip-rule="evenodd" /></svg>
                                    </div>
                                    <div class="icon-option" data-icon-name="book_open">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M7.502 1.667C5.085 2.494 3.333 4.747 3.333 7.5c0 3.038 2.044 5.56 4.793 6.525CF5.625 14.075 4.167 15.55 4.167 17.5h11.666c0-1.95-1.458-3.425-4.042-3.975C14.623 13.06 16.667 10.538 16.667 7.5c0-2.753-1.752-4.995-4.165-5.833A2.5 2.5 0 0010 0c-.77 0-1.459.347-1.921.833-.168.133-.325.272-.477.417Z" /></svg>
                                    </div>
                                     <div class="icon-option" data-icon-name="flame_simple">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M10 2c-1.804 0-3.51.542-4.938 1.527C3.132 4.612 2 6.304 2 8.25c0 2.879 1.973 5.306 4.665 6.084a4.497 4.497 0 01-1.081 1.577 1.001 1.001 0 00.023 1.396l.01.01a.75.75 0 001.062.01l1.483-1.236A6.505 6.505 0 0010 16.5c1.948 0 3.743-.684 5.112-1.836.27-.225.469-.525.56-.858.09-.333.074-.681-.045-1.002-.119-.32-.335-.601-.612-.796a4.486 4.486 0 01-1.081-1.577C16.027 13.556 18 11.13 18 8.25c0-1.946-1.132-3.638-3.063-4.723C13.51 2.542 11.804 2 10 2z"/></svg>
                                    </div>
                                     <div class="icon-option" data-icon-name="moon">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.455 2.004a.75.75 0 01.26.77 7 7 0 009.958 7.967.75.75 0 011.067.853A8.5 8.5 0 116.647 1.921a.75.75 0 01.808.083z" clip-rule="evenodd" /></svg>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="habit-frequency" class="form-label">执行频次 (多久浇灌一次？)</label>
                                <select id="habit-frequency" name="habit-frequency" class="form-input">
                                    <option value="daily">每日</option>
                                    <option value="weekdays">每个工作日</option>
                                    <option value="weekends">仅周末</option>
                                    <option value="weekly">每周一次 (固定某天)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="habit-reminder" class="form-label">提醒时间 (Aura何时呼唤你？)</label>
                                <input type="time" id="habit-reminder" name="habit-reminder" class="form-input">
                            </div>

                            <div class="form-group">
                                <label for="habit-motivation" class="form-label">坚持的动力 (它为何对你闪耀？)</label>
                                <textarea id="habit-motivation" name="habit-motivation" class="form-textarea" rows="3" placeholder="例如：为了更健康的身体，为了内心平静..."></textarea>
                            </div>
                            
                            <button type="submit" class="primary-action-button mt-2">确认播种</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二个iPhone模拟器: 习惯详情与进化史页面 -->
        <div class="mockup-unit">
            <h2 id="title-habit-evolution" class="mockup-page-title">[习惯名称]的进化史</h2>
            <div id="mockup-habit-evolution" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                         <div class="ios-status-bar">
                            <span class="time">9:42</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>

                        <div class="page-top-nav">
                            <button class="nav-button">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <h1 class="nav-title" id="evolution-page-title">晨间正念的进化史</h1>
                             <button class="nav-button">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5"><path d="M2.625 5.375a.75.75 0 01.75-.75h13.25a.75.75 0 010 1.5H3.375a.75.75 0 01-.75-.75zM2.625 9.375a.75.75 0 01.75-.75h13.25a.75.75 0 010 1.5H3.375a.75.75 0 01-.75-.75zM2.625 13.375a.75.75 0 01.75-.75h13.25a.75.75 0 010 1.5H3.375a.75.75 0 01-.75-.75z" /></svg>
                            </button>
                        </div>

                        <div class="page-visible-content p-5 space-y-5">
                            <div class="habit-main-image-container">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M10.5 3.5a2.502 2.502 0 00-5 0V4h5v-.5zM15.5 4H13V3.5a4.502 4.502 0 00-9 0V4H1.5a.5.5 0 00-.5.5v1a.5.5 0 00.5.5H2v8.5a3.5 3.5 0 003.5 3.5h9a3.5 3.5 0 003.5-3.5V6h.5a.5.5 0 00.5-.5v-1a.5.5 0 00-.5-.5zM5.5 14.5A1.5 1.5 0 014 13V6h1.5v8.5zM16 13a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 014 13V6h12v7zM7.508 8.013A.5.5 0 017.013 7.5a.5.5 0 01.495-.52.5.5 0 01.495.52.5.5 0 01-.495.493zm5 0a.5.5 0 01-.495-.52.5.5 0 01.495-.52.5.5 0 01.495.52.5.5 0 01-.495.493z"/></svg>                                        
                            </div>

                            <div class="form-group">
                                <label class="form-label">成长日历 (过去30天)</label>
                                <div class="growth-calendar">
                                    <!-- JS会动态填充这里，以下为示例 -->
                                    <div class="calendar-day completed">1</div><div class="calendar-day completed">2</div><div class="calendar-day missed">3</div><div class="calendar-day completed">4</div><div class="calendar-day">5</div><div class="calendar-day">6</div><div class="calendar-day completed">7</div>
                                    <div class="calendar-day">8</div><div class="calendar-day completed">9</div><div class="calendar-day completed">10</div><div class="calendar-day">11</div><div class="calendar-day missed">12</div><div class="calendar-day completed">13</div><div class="calendar-day">14</div>
                                    <div class="calendar-day completed">15</div><div class="calendar-day">16</div><div class="calendar-day current completed">17</div> <!-- 当天且完成 -->
                                    <div class="calendar-day">18</div><div class="calendar-day">19</div><div class="calendar-day">20</div><div class="calendar-day">21</div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">能量统计</label>
                                <div class="stats-summary-grid">
                                    <div class="stat-item"><div class="value">22</div><div class="label">总完成次数</div></div>
                                    <div class="stat-item"><div class="value">28</div><div class="label">总培育天数</div></div>
                                    <div class="stat-item"><div class="value">15</div><div class="label">最长连续</div></div>
                                    <div class="stat-item"><div class="value">78%</div><div class="label">平均完成率</div></div>
                                </div>
                            </div>

                            <div class="ai-notes-card">
                                <h3 class="title">Aura的观察笔记</h3>
                                <p class="content">看起来你在工作日更容易坚持晨间正念，周末的完成率稍有波动。试着在周末也保持一个相对固定的唤醒时间，可能会有帮助哦！</p>
                            </div>

                            <div class="actions-group">
                                <button class="secondary-action-button">编辑微元</button>
                                <button class="secondary-action-button">暂停培育</button>
                            </div>
                             <button class="primary-action-button danger">彻底移除此微元</button>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // 简单的图标选择器交互
        document.querySelectorAll('.icon-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.icon-option.selected').forEach(selected => selected.classList.remove('selected'));
                option.classList.add('selected');
                console.log('Selected icon:', option.dataset.iconName);
            });
        });

        // 确保页面加载后执行一次检查，隐藏所有滚动条
        document.addEventListener('DOMContentLoaded', function() {
            // 强制隐藏手机模拟器中的所有滚动条
            const styleEl = document.createElement('style');
            styleEl.textContent = `
                .iphone-screen *::-webkit-scrollbar,
                .app-container *::-webkit-scrollbar,
                .page-visible-content *::-webkit-scrollbar {
                    width: 0 !important;
                    height: 0 !important;
                    display: none !important;
                }
            `;
            document.head.appendChild(styleEl);
            
            // 防止内容水平溢出
            const contentElements = document.querySelectorAll('.page-visible-content > *');
            contentElements.forEach(el => {
                if (el.scrollWidth > el.clientWidth) {
                    console.log('检测到可能的水平溢出元素:', el);
                    el.style.maxWidth = '100%';
                    el.style.overflowX = 'hidden';
                }
            });
        });

        // 可以在这里添加更多交互，比如动态修改页面标题等
        // 例如，修改"播种新元"页面的标题为"编辑微元"
        // document.getElementById('sow-seed-page-title').textContent = '编辑微元'; 
        // document.getElementById('title-sow-seed').textContent = '编辑微元'; // 修改模拟器上方的标题

        // 例如，修改"习惯详情"页面的标题
        // document.getElementById('evolution-page-title').textContent = '睡前阅读的进化史';
        // document.getElementById('title-habit-evolution').textContent = '睡前阅读的进化史';


        // 模拟日历填充 (仅为示例)
        const calendar = document.querySelector('.growth-calendar');
        if (calendar) {
            // 清空现有示例
            // calendar.innerHTML = ''; 
            // for (let i = 1; i <= 28; i++) { // 假设展示28天
            //     const dayDiv = document.createElement('div');
            //     dayDiv.classList.add('calendar-day');
            //     dayDiv.textContent = i;
            //     if (i % Math.floor(Math.random() * 4 + 1) === 0 && i < 25) dayDiv.classList.add('completed');
            //     else if (i % 7 === 0 && i < 25) dayDiv.classList.add('missed');
            //     if (i === 22) dayDiv.classList.add('current'); // 假设22号是今天
            //     calendar.appendChild(dayDiv);
            // }
        }
    </script>

</body>
</html> 