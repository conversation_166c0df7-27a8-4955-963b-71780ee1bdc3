<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微光教练 - "我的"子页面预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #0A192F; /* 深邃的背景色，可以根据rules调整 */
            padding: 30px;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            display: flex;
            flex-direction: column; /* 让标题和内容垂直排列 */
            align-items: center; /* 居中容器 */
        }

        .mockup-gallery-container {
            display: flex;
            flex-wrap: wrap; /* 允许换行 */
            justify-content: center; /* 居中模拟器 */
            gap: 40px; /* 模拟器之间的间距 */
            width: 100%;
        }

        .mockup-gallery-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .mockup-unit {
            margin-bottom: 40px;
            /* width: 393px; */ /* Removed to allow natural flow if only one item per row */
        }

        .mockup-page-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .iphone-mockup {
            width: 393px; /* iPhone 14/15 Pro width */
            height: 852px; /* iPhone 14/15 Pro height */
            background-color: #1c1c1e; /* iPhone dark mode body color */
            border-radius: 50px; /* More pronounced rounding for iPhone X+ style */
            padding: 14px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4), 0 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #000; /* Screen off or base black for dynamic island */
            border-radius: 36px; /* Inner screen rounding */
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* Dynamic Island (Notch) */
        .iphone-mockup::before {
            content: '';
            position: absolute;
            top: 22px; /* Adjusted for padding */
            left: 50%;
            transform: translateX(-50%);
            width: 130px; /* Typical width of dynamic island area */
            height: 30px; /* Typical height */
            background-color: #000; /* Black like the island */
            border-radius: 15px; /* Rounded ends of the island */
            z-index: 1010; /* Above screen content but below status bar text if needed */
        }
        
        /* Home indicator (gesture bar) - subtle */
        .iphone-mockup::after {
            content: '';
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 5px;
            background-color: rgba(200, 200, 200, 0.2); /* Light gray, semi-transparent */
            border-radius: 2.5px;
            z-index: 1001; /* Above screen content */
        }

        .ios-status-bar {
            height: 44px; /* Approximate height of iOS status bar area */
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 13px;
            font-weight: 500;
            position: absolute; /* To overlay on screen content */
            top: 0;
            left: 0;
            right: 0;
            z-index: 1005; /* Above notch */
            padding-top: 12px; /* Push content below dynamic island visually */
        }
        .ios-status-bar .time { margin-left: 5px; }
        .ios-status-bar .signals { display: flex; align-items: center; gap: 5px; margin-right: 5px; }
        .ios-status-bar .signals svg { width: 16px; height: 16px; fill: white; }

        /* Base app container style - Deep blue/green, Aurora Gradients */
        .app-container {
            flex-grow: 1;
            background-color: #002b20; /* Fallback: Dark Teal/Forest Green */
            background-image: 
                radial-gradient(ellipse at 15% 25%, rgba(0, 128, 128, 0.45) 0%, transparent 55%), /* Luminous Teal hints */
                radial-gradient(ellipse at 85% 60%, rgba(13, 71, 161, 0.35) 0%, transparent 65%), /* Deep Blue hints */
                radial-gradient(circle at 20% 85%, rgba(56, 239, 125, 0.28) 0%, transparent 50%), /* Mint Green hints */
                radial-gradient(ellipse at 50% 55%, rgba(0, 105, 92, 0.32) 0%, transparent 70%), 
                radial-gradient(ellipse at 70% 80%, rgba(255, 99, 71, 0.1) 0%, transparent 60%), /* Sunset Orange subtle hint */
                linear-gradient(170deg, #0A2F51 0%, #005A4B 55%, #002b20 100%); /* Deep Blue to Forest Green */
            color: white;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            padding-top: 44px; /* Space for iOS status bar */
        }

        .page-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: 0 16px 16px 16px; /* Default padding for page content */
            display: flex;
            flex-direction: column;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 128, 128, 0.5) transparent;
        }
        .page-content::-webkit-scrollbar { width: 6px; }
        .page-content::-webkit-scrollbar-track { background: transparent; }
        .page-content::-webkit-scrollbar-thumb { background-color: rgba(0, 128, 128, 0.5); border-radius: 3px; }

        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 16px; /* Adjusted padding */
            position: sticky;
            top: 0;
            background-color: transparent; /* MODIFIED: Removed gray background */
            backdrop-filter: none; /* MODIFIED: Removed blur effect */
            z-index: 10;
            margin-bottom: 12px; /* MODIFIED: Space below header */
            /* Removed margin: 0 -16px ... and specific padding-left/right overrides */
        }
        .page-header-title {
            font-size: 1.1rem; /* 17-18px */
            font-weight: 600;
            color: #e2e8f0;
            text-align: center;
            flex-grow: 1;
        }
        .header-action-placeholder { width: 30px; height: 30px; } /* For spacing if no button */
        .back-button svg { width: 24px; height: 24px; fill: #e2e8f0; }

        /* "自然启示"与"科技微光" elements */
        .text-luminous-teal { color: #40E0D0; } /*明亮且带有荧光感的青色/薄荷绿*/
        .bg-luminous-teal { background-color: #40E0D0; }
        .border-luminous-teal { border-color: #40E0D0; }
        
        .text-warm-coral { color: #FF7F50; } /*温暖的珊瑚粉/日落橙*/
        .bg-warm-coral { background-color: #FF7F50; }
        .border-warm-coral { border-color: #FF7F50; }

        .btn-primary {
            background: linear-gradient(90deg, #FF7F50, #FF6347);
            color: white;
            font-weight: bold;
            padding: 12px 24px;
            border-radius: 25px; /* More organic shape */
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(255,107,71,0.4);
            text-align: center;
        }
        .btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 6px 20px rgba(255,107,71,0.5);
        }
        .btn-secondary {
            background-color: rgba(0, 128, 128, 0.3); /* Teal, semi-transparent */
            color: #AFEEEE; /* Pale Turquoise */
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid rgba(0, 128, 128, 0.5);
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s ease, transform 0.2s ease;
        }
        .btn-secondary:hover {
            background-color: rgba(0, 128, 128, 0.5);
            transform: translateY(-1px);
        }
        
        /* Card style with organic feel */
        .content-card {
            background-color: rgba(0, 60, 50, 0.4); /* Darker, less transparent than some from home */
            backdrop-filter: blur(10px);
            border-radius: 24px; /* Softer, more organic radius */
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 128, 128, 0.3); /* Subtle border with teal */
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .content-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 50, 40, 0.4);
        }

        /* Badge specific styles */
        .badge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 20px;
        }
        .badge-item {
            background-color: rgba(0, 128, 128, 0.15);
            border-radius: 16px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border: 1px solid rgba(0, 128, 128, 0.2);
            transition: transform 0.2s ease, background-color 0.2s ease;
        }
        .badge-item:hover {
            transform: scale(1.05);
            background-color: rgba(0, 128, 128, 0.25);
        }
        .badge-icon-lg {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 2rem; /* For emoji/text icons */
        }
        .badge-name {
            font-size: 0.9rem;
            font-weight: 500;
            color: #e2e8f0;
            margin-bottom: 4px;
        }
        .badge-status {
            font-size: 0.75rem;
            color: #cbd5e1;
        }

        /* Pro Membership Page Styles */
        .pro-benefit-item { /* Global style for pro-benefit-item */
            display: flex;
            align-items: center;
            gap: 12px; 
            margin-bottom: 12px;
            padding: 10px;
            background-color: rgba(0, 128, 128, 0.1);
            border-radius: 12px;
        }
        /* MODIFIED: Specific adjustments for Evolve Pro Center page */
        #mockup-evolve-pro-center .pro-benefit-item {
            padding: 6px 8px; /* Reduced padding */
            gap: 8px; /* Reduced gap */
            margin-bottom: 6px; /* Reduced margin */
        }
        .pro-benefit-item svg {
            width: 24px; 
            height: 24px;
            flex-shrink: 0;
        }
        /* MODIFIED: Adjust icon size slightly for the tighter layout in Evolve Pro Center */
        #mockup-evolve-pro-center .pro-benefit-item svg {
            width: 20px; 
            height: 20px;
        }
        .pro-benefit-text {
            font-size: 0.95rem;
            color: #e0e0e0;
        }
         /* MODIFIED: Adjust text size slightly for the tighter layout in Evolve Pro Center */
        #mockup-evolve-pro-center .pro-benefit-text {
            font-size: 0.85rem; /* Slightly smaller text */
            line-height: 1.3;
        }

        .subscription-option { /* Global style for subscription-option */
            border: 2px solid rgba(0, 128, 128, 0.5);
            border-radius: 16px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }
        /* MODIFIED: Specific adjustments for Evolve Pro Center page */
        #mockup-evolve-pro-center .subscription-option {
            padding: 10px 12px; /* Reduced padding */
            margin-bottom: 8px; /* Reduced margin */
            border-radius: 12px; /* Slightly smaller radius */
        }
        .subscription-option:hover, .subscription-option.selected {
            border-color: #40E0D0; /* Luminous Teal */
            background-color: rgba(64, 224, 208, 0.1);
        }
        .subscription-option h4 {
            color: #AFEEEE; /* Pale Turquoise for title */
        }
        /* MODIFIED: Adjust h4 margin for tighter layout in Evolve Pro Center */
        #mockup-evolve-pro-center .subscription-option h4 {
            margin-bottom: 2px; 
            font-size: 0.95rem; /* Slightly smaller title */
        }
        /* MODIFIED: Adjust price text for tighter layout in Evolve Pro Center */
        #mockup-evolve-pro-center .subscription-option p.text-2xl {
            font-size: 1.35rem; /* Smaller price text */
            margin-bottom: 2px;
        }
        #mockup-evolve-pro-center .subscription-option p.text-xs {
            font-size: 0.7rem; /* Smaller sub-text */
        }

        .discount-badge {
            background-color: #FF7F50; /* Warm Coral */
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
        }

        /* List items (e.g., for settings or info) */
        .info-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 128, 128, 0.2);
        }
        .info-list-item:last-child {
            border-bottom: none;
        }
        .info-label { color: #cbd5e1; }
        .info-value { color: #e2e8f0; font-weight: 500; }
        
        /* Placeholder for payment icons */
        .payment-icon {
            width: 40px; height: 25px; background-color: #ccc; border-radius: 4px;
            display: inline-block; margin-right: 8px;
        }

    </style>
</head>
<body>

    <h1 class="mockup-gallery-title">"我的" 子页面预览</h1>

    <div class="mockup-gallery-container">

        <!-- 4.1 成就殿堂 (徽章墙) -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">成就殿堂</h2>
            <div id="mockup-hall-of-fame" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:41</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <div class="page-header">
                            <button class="back-button p-2 -ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <h1 class="page-header-title">成就殿堂</h1>
                            <div class="header-action-placeholder"></div> <!-- Placeholder for potential right-side icon -->
                        </div>
                        <div class="page-content">
                            <div class="badge-grid">
                                <div class="badge-item">
                                    <div class="badge-icon-lg bg-gradient-to-br from-yellow-400 to-orange-500">✨</div>
                                    <p class="badge-name">初光者</p>
                                    <p class="badge-status">已获得</p>
                                </div>
                                <div class="badge-item">
                                    <div class="badge-icon-lg bg-gradient-to-br from-green-400 to-teal-500">🌿</div>
                                    <p class="badge-name">萌芽之力</p>
                                    <p class="badge-status">已获得</p>
                                </div>
                                <div class="badge-item">
                                    <div class="badge-icon-lg bg-gradient-to-br from-sky-400 to-blue-500">💧</div>
                                    <p class="badge-name">恒心水滴</p>
                                    <p class="badge-status">已获得</p>
                                </div>
                                <div class="badge-item">
                                    <div class="badge-icon-lg bg-gradient-to-br from-purple-400 to-indigo-500">🔮</div>
                                    <p class="badge-name">智慧水晶</p>
                                    <p class="badge-status">25/50 天</p>
                                </div>
                                <div class="badge-item">
                                    <div class="badge-icon-lg bg-gradient-to-br from-pink-400 to-red-500">🔥</div>
                                    <p class="badge-name">不息之火</p>
                                    <p class="badge-status">10/30 次</p>
                                </div>
                                <div class="badge-item">
                                    <div class="badge-icon-lg bg-gray-500 opacity-60">❓</div>
                                    <p class="badge-name">神秘徽章</p>
                                    <p class="badge-status">未解锁</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4.2 徽章详情弹窗/页面 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">徽章详情</h2>
            <div id="mockup-badge-details" class="iphone-mockup">
                <div class="iphone-screen">
                     <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:42</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <div class="page-header">
                            <button class="back-button p-2 -ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <h1 class="page-header-title">萌芽之力 详情</h1>
                            <div class="header-action-placeholder"></div>
                        </div>
                        <div class="page-content items-center text-center">
                            <div class="badge-icon-lg bg-gradient-to-br from-green-400 to-teal-500 w-32 h-32 text-6xl rounded-full mt-6 mb-6 shadow-xl">🌿</div>
                            <h3 class="text-2xl font-semibold text-luminous-teal mb-2">萌芽之力</h3>
                            <p class="text-slate-300 mb-6">连续完成任意习惯7天</p>
                            
                            <div class="content-card w-full text-left">
                                <h4 class="text-lg font-semibold text-teal-300 mb-3">徽章故事</h4>
                                <p class="text-slate-200 text-sm leading-relaxed mb-4">
                                    每一株参天大树，都始于一颗微小的种子破土而出。这枚徽章象征着你坚持不懈的努力，成功培育了习惯的嫩芽。继续灌溉，它将茁壮成长！
                                </p>
                                <div class="border-t border-teal-700/50 pt-3">
                                    <p class="text-xs text-slate-400">获得时间：2024年06月15日</p>
                                </div>
                            </div>
                             <button class="btn-secondary mt-6 w-full max-w-xs">返回成就殿堂</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4.3 Evolve AI Pro 会员中心页面 (非Pro用户视角) -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">Evolve AI Pro 会员中心</h2>
            <div id="mockup-evolve-pro-center" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:43</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                         <div class="page-header">
                            <button class="back-button p-2 -ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <h1 class="page-header-title">Evolve AI Pro</h1>
                            <div class="header-action-placeholder"></div>
                        </div>
                        <div class="page-content">
                            <div class="text-center mt-1 mb-2">
                                <div class="inline-block p-2 rounded-full bg-gradient-to-br from-teal-500 to-cyan-600 shadow-lg mb-1">
                                     <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        <path d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.39-3.423 3.352c-.772.752-.297 2.075.752 2.267l4.34.628 1.953 4.275c.345.752 1.45.752 1.795 0l1.953-4.275 4.34-.628c1.05-.192 1.525-1.515.752-2.267l-3.423-3.352-4.753-.39-1.83-4.401Z" opacity="0.5" transform="scale(0.4) translate(15,15)"/>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-luminous-teal to-green-400 mb-1">解锁 Evolve Pro</h3>
                                <p class="text-slate-300 text-xs mb-3">释放您的全部潜能，加速习惯进化之旅。</p>
                            </div>

                            <div class="content-card mb-3">
                                <h4 class="text-base font-semibold text-teal-300 mb-2">Pro会员专属权益：</h4>
                                <ul class="space-y-1">
                                    <li class="pro-benefit-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-luminous-teal"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" /></svg>
                                        <span class="pro-benefit-text">无限微元生态位，培养更多可能</span>
                                    </li>
                                    <li class="pro-benefit-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-luminous-teal"><path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-7.5h12v-1.5h-12v1.5z" /></svg>
                                        <span class="pro-benefit-text">高级AI洞察，深度分析您的行为模式</span>
                                    </li>
                                    <li class="pro-benefit-item">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-luminous-teal"><path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4-2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.994 15.994 0 011.622-3.395m3.42 3.42a15.995 15.995 0 004.764-4.764m1.16 1.16a15.993 15.993 0 01-1.622 3.395m-3.42-3.42a15.995 15.995 0 00-4.764 4.764m0 0l-1.16-1.16" /></svg>
                                        <span class="pro-benefit-text">AI定制化习惯路径规划与长期目标辅导</span>
                                    </li>
                                    <li class="pro-benefit-item">
                                       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="text-luminous-teal"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L24 5.25l-.813 2.846a4.5 4.5 0 00-3.09 3.09L18.25 12zm-9 2.25l2.846.813a4.5 4.5 0 003.09 3.09L15 21.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L9 14.25z" /></svg>
                                        <span class="pro-benefit-text">专属Pro主题、徽章及更多个性化内容</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="subscription-option selected">
                                <div class="flex justify-between items-center">
                                    <h4 class="text-lg font-semibold">年度订阅</h4>
                                    <span class="discount-badge">节省30%</span>
                                </div>
                                <p class="text-2xl font-bold text-warm-coral">¥168 <span class="text-sm text-slate-400 font-normal">/ 年</span></p>
                                <p class="text-xs text-slate-400">(相当于 ¥14 / 月)</p>
                            </div>
                            <div class="subscription-option">
                                <h4 class="text-lg font-semibold">月度订阅</h4>
                                <p class="text-2xl font-bold text-warm-coral">¥20 <span class="text-sm text-slate-400 font-normal">/ 月</span></p>
                            </div>
                            
                            <button class="btn-primary w-full mt-3 mb-2 py-2.5 text-sm">立即升级 Evolve Pro</button>
                            <p class="text-xs text-slate-500 text-center">订阅可随时取消。查看<a href="#" class="underline">服务条款</a>。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4.4 Pro会员订阅确认/支付页面 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">Pro会员订阅确认</h2>
            <div id="mockup-pro-confirmation" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="app-container">
                        <div class="ios-status-bar">
                            <span class="time">9:44</span>
                            <span class="signals">
                                <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                                <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                            </span>
                        </div>
                        <div class="page-header">
                             <button class="back-button p-2 -ml-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                            </button>
                            <h1 class="page-header-title">确认订阅</h1>
                            <div class="header-action-placeholder"></div>
                        </div>
                        <div class="page-content">
                            <div class="content-card mb-6">
                                <h3 class="text-xl font-semibold text-luminous-teal mb-4">订单详情</h3>
                                <div class="info-list-item">
                                    <span class="info-label">产品名称:</span>
                                    <span class="info-value">Evolve AI Pro</span>
                                </div>
                                <div class="info-list-item">
                                    <span class="info-label">订阅类型:</span>
                                    <span class="info-value">年度订阅</span>
                                </div>
                                <div class="info-list-item">
                                    <span class="info-label">原价:</span>
                                    <span class="info-value"><del>¥240.00</del></span>
                                </div>
                                <div class="info-list-item">
                                    <span class="info-label">优惠:</span>
                                    <span class="info-value text-warm-coral">- ¥72.00 (30% off)</span>
                                </div>
                                <div class="info-list-item mt-2 pt-3 border-t-2 border-teal-600">
                                    <span class="info-label text-lg font-bold">应付金额:</span>
                                    <span class="info-value text-xl font-bold text-warm-coral">¥168.00</span>
                                </div>
                            </div>

                            <div class="content-card">
                                <h3 class="text-xl font-semibold text-luminous-teal mb-4">选择支付方式</h3>
                                <div class="space-y-3">
                                    <label class="flex items-center p-3 border border-teal-700 rounded-lg hover:border-luminous-teal cursor-pointer">
                                        <input type="radio" name="payment_method" class="form-radio h-5 w-5 text-luminous-teal bg-transparent border-slate-500 focus:ring-luminous-teal" checked>
                                        <span class="ml-3 text-slate-200">微信支付</span>
                                        <div class="payment-icon ml-auto bg-green-500"></div> <!-- Placeholder -->
                                    </label>
                                    <label class="flex items-center p-3 border border-teal-700 rounded-lg hover:border-luminous-teal cursor-pointer">
                                        <input type="radio" name="payment_method" class="form-radio h-5 w-5 text-luminous-teal bg-transparent border-slate-500 focus:ring-luminous-teal">
                                        <span class="ml-3 text-slate-200">支付宝</span>
                                        <div class="payment-icon ml-auto bg-blue-500"></div> <!-- Placeholder -->
                                    </label>
                                </div>
                            </div>
                            
                            <button class="btn-primary w-full mt-8 mb-4">确认支付 ¥168.00</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- End of mockup-gallery-container -->

    <script>
        // Basic interactivity for subscription options
        document.querySelectorAll('.subscription-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.subscription-option.selected').forEach(selected => selected.classList.remove('selected'));
                option.classList.add('selected');
            });
        });
    </script>
</body>
</html> 