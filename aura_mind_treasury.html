<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AuraMind AI - 灵境宝库</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #1a202c; /* 深色背景 */
            padding: 30px;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        .mockup-gallery-title {
            text-align: center;
            font-size: 2.5rem; /* 标题字号 */
            font-weight: 700; /* 标题字重 */
            color: #e2e8f0; /* 标题颜色 */
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .mockup-unit {
            margin-bottom: 40px;
        }

        .mockup-page-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .iphone-mockup {
            width: 393px; /* iPhone 14/15 Pro 宽度 */
            height: 852px; /* iPhone 14/15 Pro 高度 */
            background-color: #1c1c1e; /* iPhone 模拟器外壳颜色 */
            border-radius: 50px; /* 外壳圆角 */
            padding: 14px; /* 内边距 */
            box-shadow: 0 20px 50px rgba(0,0,0,0.4), 0 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #000; /* 屏幕背景色 */
            border-radius: 36px; /* 屏幕圆角 */
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .iphone-mockup::before { /* 灵动岛 */
            content: '';
            position: absolute;
            top: 22px;
            left: 50%;
            transform: translateX(-50%);
            width: 130px;
            height: 30px;
            background-color: #000;
            border-radius: 15px;
            z-index: 1010;
        }
        
        .ios-status-bar {
            height: 44px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 13px;
            font-weight: 500;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1005;
            padding-top: 12px; /* 为灵动岛腾出空间 */
        }
        .ios-status-bar .time { margin-left: 5px; }
        .ios-status-bar .signals { display: flex; align-items: center; gap: 5px; margin-right: 5px; }
        .ios-status-bar .signals svg { width: 16px; height: 16px; fill: white; }

        .app-container { /* 主应用容器背景和粒子效果 */
            flex-grow: 1;
            background-color: #002b20; /* 深青色基底 */
            background-image: 
                radial-gradient(ellipse at 15% 25%, rgba(0, 128, 128, 0.35) 0%, transparent 55%),
                radial-gradient(ellipse at 85% 60%, rgba(13, 71, 161, 0.25) 0%, transparent 65%),
                radial-gradient(circle at 20% 85%, rgba(56, 239, 125, 0.18) 0%, transparent 50%),
                radial-gradient(ellipse at 50% 55%, rgba(0, 105, 92, 0.22) 0%, transparent 70%),
                linear-gradient(170deg, #0A2F51 0%, #005A4B 55%, #002b20 100%); 
            color: white;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            padding-top: 44px; /* 为状态栏留出空间 */
        }

        @keyframes float {
            0% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.5; }
            50% { transform: translateY(-15px) translateX(8px) scale(1.05); opacity: 0.2; }
            100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.5; }
        }
        .light-particle {
            position: absolute;
            background-color: rgba(173, 216, 230, 0.25); /* 淡青色光点 */
            border-radius: 50%;
            animation: float 10s infinite ease-in-out;
            pointer-events: none;
        }

        /* 宝库页面通用样式 */
        .treasury-page-content-wrapper { /* Renamed from treasury-page-content and always visible */
            display: flex; 
            flex-direction: column;
            flex-grow: 1;
            width: 100%;
            color: #e2e8f0; /* 默认文字颜色 */
            position: relative; /* 确保定位上下文 */
            height: calc(100% - 44px); /* 减去状态栏高度 */
            overflow: hidden; /* 确保整体不溢出 */
        }

        .scrollbar-hide {
            -ms-overflow-style: none;  /* IE 和 Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
        }

        .treasury-header {
            display: flex;
            align-items: center;
            padding: 10px 12px; /* 头部内边距 */
            background-color: rgba(16, 22, 32, 0.6); /* 半透明深色背景 */
            backdrop-filter: blur(12px); /* 毛玻璃效果 */
            border-bottom: 1px solid rgba(255, 255, 255, 0.08); /* 细分割线 */
            position: sticky;
            top: 0; /* 固定在滚动区域顶部 */
            z-index: 10;
            height: 50px; /* 固定头部高度 */
        }
        .treasury-back-button svg {
            width: 28px; height: 28px; color: #AFEEEE; /* 返回按钮颜色 */
            transition: transform 0.2s;
        }
        .treasury-back-button:hover svg {
            transform: translateX(-2px);
        }
        .treasury-title {
            font-size: 1.1rem; /* 17.6px */
            font-weight: 600;
            color: #e2e8f0; /* 标题颜色 */
            text-align: center;
            flex-grow: 1;
            line-height: 1.2;
        }
        .treasury-header-action svg { width: 24px; height: 24px; color: #AFEEEE; }

        /* 宝库主页特定样式 */
        .treasury-search-bar {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .treasury-search-bar input::placeholder { color: #94a3b8; }

        .resource-category-title {
            font-size: 1.25rem; /* 20px */
            font-weight: 600;
            color: #a5f3fc; /* 亮青色 */
            margin-bottom: 12px;
            padding-left: 4px;
            border-left: 3px solid #2dd4bf; /* 强调色竖线 */
        }
        .resource-card {
            background-color: rgba(255, 255, 255, 0.07); /* 卡片背景 */
            backdrop-filter: blur(5px);
            border-radius: 18px; /* 更圆润的卡片 */
            padding: 16px;
            transition: transform 0.2s ease, background-color 0.2s ease;
            /* cursor: pointer;  Removed as click navigation is removed for static display */
            border: 1px solid rgba(255,255,255,0.1);
        }
        .resource-card:hover {
            transform: translateY(-3px);
            background-color: rgba(255, 255, 255, 0.12); /* 悬浮时更亮 */
        }
        .resource-card-icon-bg {
            width: 40px; height: 40px;
            border-radius: 10px;
            display: flex; align-items: center; justify-content: center;
            margin-right: 12px;
        }
        .resource-card-icon-bg svg { width: 22px; height: 22px; }
        .resource-card-title { font-size: 1rem; font-weight: 500; color: #f0f9ff; margin-bottom: 2px; }
        .resource-card-snippet { font-size: 0.8rem; color: #cbd5e1; line-height: 1.4; }
        
        .pro-section-banner {
            background: linear-gradient(135deg, #FF7F50, #40E0D0); /* 能量渐变 */
            border-radius: 18px;
            padding: 20px;
            text-align: center;
            /* cursor: pointer; Removed as click navigation is removed */
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .pro-section-banner:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .pro-banner-title { font-size: 1.1rem; font-weight: 600; color: white; }
        .pro-banner-subtitle { font-size: 0.85rem; color: rgba(255,255,255,0.85); }

        /* 详情页特定样式 */
        .detail-content-area {
            padding: 12px; /* 内边距保持 */
            color: #d1d5db; /* 内容区域文字颜色 */
            font-size: 0.875rem; /* 稍微增大基础字体 (约14px) */
            line-height: 1.6; /* 增大行高，改善可读性 */
            overflow-y: auto; /* 允许内容垂直滚动 */
            overflow-x: hidden; /* 防止水平溢出 */
            flex-grow: 1; /* 填充剩余空间 */
            height: calc(100% - 50px); /* 确保内容不会超出屏幕底部，50px 是头部导航的高度 */
            scrollbar-width: none; /* 隐藏滚动条 */
            -ms-overflow-style: none; /* IE 和 Edge */
        }
        .detail-content-area::-webkit-scrollbar {
            display: none; /* 在WebKit浏览器中隐藏滚动条 */
        }
        .detail-content-area h2 { 
            font-size: 1.25rem; /* 增大标题字号 (约20px) */
            font-weight: 600; 
            color: #a5f3fc; 
            margin-bottom: 10px; /* 调整底部边距 */
        }
        .detail-content-area h3 { 
            font-size: 1.05rem; /* 增大副标题字号 (约16.8px) */
            font-weight: 600; 
            color: #7dd3fc; 
            margin-top: 12px; /* 调整顶部边距 */
            margin-bottom: 6px; /* 调整底部边距 */
        }
        .detail-content-area p { 
            margin-bottom: 10px; /* 调整段落间距 */
            word-wrap: break-word; 
        }
        .detail-content-area ul, .detail-content-area ol { 
            margin-left: 16px; /* 调整左边距 */
            margin-bottom: 10px; /* 调整底部边距 */
        }
        .detail-content-area li { 
            margin-bottom: 6px; /* 调整列表项间距 */
            word-wrap: break-word; 
        }
        .exercise-step {
            background-color: rgba(255,255,255,0.05);
            padding: 8px; /* 稍微增大内边距 */
            border-radius: 10px; /* 稍微增大圆角 */
            margin-bottom: 8px; /* 调整间距 */
            border-left: 3px solid #40E0D0;
            word-wrap: break-word; /* 确保长文本会换行 */
        }
        .exercise-step-number {
            font-size: 0.95rem; /* 增大字号 (约15.2px) */
            font-weight: bold; 
            color: #40E0D0; 
            margin-right: 8px; /* 调整右边距 */
        }
    </style>
</head>
<body>

    <h1 class="mockup-gallery-title">AuraMind AI - 灵境宝库 (多视图预览)</h1>

    <div class="flex flex-wrap justify-center gap-x-8 gap-y-16">

        <!-- Mockup 1: Aura的智慧宝库 (灵感源泉) -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">灵感源泉 - 主页</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:41</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 6px; height: 6px; top: 10%; left: 15%; animation-duration: 12s;"></div>
                        <div class="light-particle" style="width: 8px; height: 8px; top: 40%; left: 80%; animation-duration: 15s; animation-delay: 2s;"></div>
                        <div class="light-particle" style="width: 5px; height: 5px; top: 70%; left: 20%; animation-duration: 10s; animation-delay: 1s;"></div>

                        <div id="page-treasury-main" class="treasury-page-content-wrapper">
                            <div class="treasury-header">
                                <button title="返回灵境对话" class="treasury-back-button p-2 -ml-1 opacity-50 cursor-not-allowed">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                                </button>
                                <h1 class="treasury-title">灵感源泉</h1>
                                <button title="搜索" class="treasury-header-action p-2 -mr-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" /></svg>
                                </button>
                            </div>
                            <div class="flex-grow overflow-y-auto overflow-x-hidden p-4 space-y-6 scrollbar-hide">
                                <div class="treasury-search-bar rounded-xl p-3 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-slate-400 mr-2"><path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" /></svg>
                                    <input type="text" placeholder="在宝库中搜索灵感..." class="bg-transparent text-sm text-slate-200 focus:outline-none w-full">
                                </div>
                                <div>
                                    <h2 class="resource-category-title">智慧锦囊</h2>
                                    <div class="space-y-3 mt-3">
                                        <div class="resource-card">
                                            <div class="flex items-start">
                                                <div class="resource-card-icon-bg bg-teal-500/30">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-teal-300"><path d="M7.502 1.667C5.085 2.494 3.333 4.747 3.333 7.5c0 3.038 2.044 5.56 4.793 6.525CF5.625 14.075 4.167 15.55 4.167 17.5h11.666c0-1.95-1.458-3.425-4.042-3.975C14.623 13.06 16.667 10.538 16.667 7.5c0-2.753-1.752-4.995-4.165-5.833A2.5 2.5 0 0 0 10 0c-.77 0-1.459.347-1.921.833-.168.133-.325.272-.477.417Z" /></svg>
                                                </div>
                                                <div>
                                                    <h3 class="resource-card-title">原子习惯的力量</h3>
                                                    <p class="resource-card-snippet">了解微小改变如何带来巨大成果。</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="resource-card">
                                            <div class="flex items-start">
                                                <div class="resource-card-icon-bg bg-sky-500/30">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-sky-300"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" /></svg>
                                                </div>
                                                <div>
                                                    <h3 class="resource-card-title">拖延症的科学解析</h3>
                                                    <p class="resource-card-snippet">了解并克服你的拖延习惯。</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h2 class="resource-category-title">心灵修行</h2>
                                    <div class="space-y-3 mt-3">
                                        <div class="resource-card">
                                            <div class="flex items-start">
                                                <div class="resource-card-icon-bg bg-violet-500/30">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-violet-300"><path d="M10 3.5A1.5 1.5 0 0111.5 2h.001a1.5 1.5 0 011.494 1.306l.163 1.144a1.502 1.502 0 001.41 1.226h1.298a1.5 1.5 0 011.396 2.056l-.324 1.298a1.5 1.5 0 00.09 1.349l.742 1.113a1.5 1.5 0 01-.97 2.475h-1.343a1.501 1.501 0 00-1.247.685l-.774 1.16a1.5 1.5 0 01-2.248 0l-.774-1.16a1.501 1.501 0 00-1.247-.685H5.717a1.5 1.5 0 01-.97-2.475l.742-1.113a1.5 1.5 0 00.09-1.349l-.324-1.298A1.5 1.5 0 016.65 5.678h1.298c.766 0 1.433-.56 1.498-1.32L9.5 3.5A1.5 1.5 0 0110 3.5z" /></svg>
                                                </div>
                                                <div>
                                                    <h3 class="resource-card-title">5分钟正念呼吸练习</h3>
                                                    <p class="resource-card-snippet">快速平静思绪，回归当下。</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="resource-card">
                                            <div class="flex items-start">
                                                <div class="resource-card-icon-bg bg-rose-500/30">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-rose-300"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" /></svg>
                                                </div>
                                                <div>
                                                    <h3 class="resource-card-title">感恩日记引导</h3>
                                                    <p class="resource-card-snippet">培养积极心态，发现生活中的小确幸。</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="pt-4">
                                    <div class="pro-section-banner">
                                        <div class="flex items-center justify-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-6 h-6 text-yellow-300 mr-2"><path fill-rule="evenodd" d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.39-3.423 3.352c-.772.752-.297 2.075.752 2.267l4.34.628 1.953 4.275c.345.752 1.45.752 1.795 0l1.953-4.275 4.34-.628c1.05-.192 1.525-1.515.752-2.267l-3.423-3.352-4.753-.39-1.83-4.401Z" clip-rule="evenodd" /></svg>
                                            <h2 class="pro-banner-title">解锁Pro专属权益</h2>
                                        </div>
                                        <p class="pro-banner-subtitle">获取深度洞察、高级工具与个性化指导。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> 
                </div>
            </div>
        </div>

        <!-- Mockup 2: 文章/知识详情页 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">灵感源泉 - 文章详情</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:42</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 5px; height: 5px; top: 12%; left: 25%; animation-duration: 11s;"></div>
                        <div class="treasury-page-content-wrapper">
                            <div class="treasury-header">
                                <button title="返回宝库" class="treasury-back-button p-2 -ml-1 opacity-50 cursor-not-allowed">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                                </button>
                                <h1 class="treasury-title truncate px-2">原子习惯的力量</h1>
                                <button title="收藏" class="treasury-header-action p-2 -mr-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.5 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0111.186 0z" /></svg>
                                </button>
                            </div>
                            <div class="detail-content-area scrollbar-hide">
                                <h2>原子习惯的力量</h2>
                                <p>微小的改变，持续坚持，将带来巨大成果。这就是原子习惯的核心理念。</p>
                                <h3>关键策略一：习惯叠加</h3>
                                <p>将新习惯与已有习惯绑定。例如："冲咖啡后，冥想一分钟。"旧习惯成为新习惯的触发器。</p>
                                <h3>关键策略二：环境设计</h3>
                                <p>让好习惯变得简单，坏习惯变得困难。想多喝水？放满水杯。想少吃零食？不要存放零食。</p>
                                <h3>关键策略三：即时奖励</h3>
                                <p>习惯需要正反馈。对于未来才见效的习惯（如健身），给自己即时小奖励来强化行为。</p>
                                <p>重点在于过程，而非完美。某天未完成，不要放弃整个系统，第二天重新开始。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mockup 3: 心理学练习引导详情页 -->
        <div class="mockup-unit">
            <h2 class="mockup-page-title">灵感源泉 - 练习引导</h2>
            <div class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="ios-status-bar">
                        <span class="time">9:43</span>
                        <span class="signals">
                            <svg viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none"/><path d="M2 22h20V2z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M12.01 21.49L23.64 7c-.45-.34-4.93-4-11.64-4C5.28 3 .81 6.66.36 7l11.63 ***********.01-.01z"/></svg>
                            <svg viewBox="0 0 24 24"><path d="M17 4h-3V2h-4v2H7v18h10V4zm-5 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>
                        </span>
                    </div>
                    <div class="app-container">
                        <div class="light-particle" style="width: 7px; height: 7px; top: 20%; left: 70%; animation-duration: 13s;"></div>
                        <div class="treasury-page-content-wrapper">
                            <div class="treasury-header">
                                <button title="返回宝库" class="treasury-back-button p-2 -ml-1 opacity-50 cursor-not-allowed">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" /></svg>
                                </button>
                                <h1 class="treasury-title truncate px-2">5分钟正念呼吸练习</h1>
                                <button title="开始练习" class="treasury-header-action p-2 -mr-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5"><path fill-rule="evenodd" d="M2 10a8 8 0 1116 0 8 8 0 01-16 0zm6.39-2.908a.75.75 0 01.766.027l3.5 2.5a.75.75 0 010 1.262l-3.5 2.5A.75.75 0 018 12.5v-5a.75.75 0 01.39-.658z" clip-rule="evenodd" /></svg>
                                </button>
                            </div>
                            <div class="detail-content-area scrollbar-hide">
                                <h2>5分钟正念呼吸练习</h2>
                                <p>这个简短练习可帮助您快速平静思绪，集中注意力，减轻压力和焦虑。</p>
                                <h3>引导步骤：</h3>
                                <ol class='list-none space-y-2'>
                                    <li class='exercise-step'><span class='exercise-step-number'>1.</span> 找一个舒适安静的地方坐下，保持脊柱挺直但放松。</li>
                                    <li class='exercise-step'><span class='exercise-step-number'>2.</span> 轻闭眼睛，或将目光柔和地投向前方地板。</li>
                                    <li class='exercise-step'><span class='exercise-step-number'>3.</span> 将注意力带到呼吸上，感受气息进出身体。</li>
                                    <li class='exercise-step'><span class='exercise-step-number'>4.</span> 不控制呼吸节奏，只观察其自然流动和腹部起伏。</li>
                                    <li class='exercise-step'><span class='exercise-step-number'>5.</span> 思绪飘走时，温和地将注意力重新带回呼吸。</li>
                                    <li class='exercise-step'><span class='exercise-step-number'>6.</span> 持续5分钟，然后慢慢将意识带回环境，轻轻活动并睁开眼睛。</li>
                                </ol>
                                <p class="mt-3">练习后，感受当下身心状态。祝您拥有平静时刻。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- End flex flex-wrap -->

    <script>
        // All JavaScript for page switching has been removed as per the new static multi-mockup display requirement.
        // If any specific interactions are needed within a mockup (e.g., a button click effect), they can be added here.
    </script>
</body>
</html> 