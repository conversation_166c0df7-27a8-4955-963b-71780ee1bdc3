<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微光教练 - 启动引导页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入 ZCOOL KuaiLe 字体 -->
    <link href="https://fonts.googleapis.com/css2?family=ZCOOL+KuaiLe&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #0A192F; /* 深邃背景 */
            padding: 30px;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .mockup-gallery-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 40px;
            width: 100%;
        }

        .mockup-gallery-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 40px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .mockup-unit {
            margin-bottom: 40px;
        }

        .mockup-page-title { /* Not used for onboarding screens, but kept for consistency if needed elsewhere */
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .iphone-mockup {
            width: 393px;
            height: 852px;
            background-color: #1c1c1e;
            border-radius: 50px;
            padding: 14px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4), 0 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #000; 
            border-radius: 36px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .iphone-mockup::before { /* Dynamic Island */
            content: '';
            position: absolute;
            top: 22px;
            left: 50%;
            transform: translateX(-50%);
            width: 130px;
            height: 30px;
            background-color: #000;
            border-radius: 15px;
            z-index: 1010;
        }
        
        .iphone-mockup::after { /* Home indicator */
            content: '';
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 5px;
            background-color: rgba(200, 200, 200, 0.2);
            border-radius: 2.5px;
            z-index: 1001;
        }

        .ios-status-bar { /* Kept for visual consistency, content might be minimal on onboarding */
            height: 44px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 13px;
            font-weight: 500;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1005;
            padding-top: 12px; 
        }
        .ios-status-bar .time { margin-left: 5px; }
        .ios-status-bar .signals { display: flex; align-items: center; gap: 5px; margin-right: 5px; }
        .ios-status-bar .signals svg { width: 16px; height: 16px; fill: white; }

        .onboarding-app-container {
            flex-grow: 1;
            color: white;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            /* Unique background for each screen will be applied inline or via specific classes */
        }

        .onboarding-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Vertically center content */
            align-items: center; /* Horizontally center content */
            padding: 40px 25px; /* Generous padding */
            text-align: center;
            position: relative; /* For absolute positioned pseudo-elements like glows */
        }
        
        .onboarding-title {
            font-family: 'ZCOOL KuaiLe', cursive; /* 应用新字体 */
            font-size: 2.4rem; /* 针对新字体微调大小，原为2.25rem */
            font-weight: normal; /* ZCOOL KuaiLe 通常只有一个字重，设为normal或400 */
            line-height: 1.3; /* 调整行高以适应新字体 */
            margin-bottom: 20px; 
            letter-spacing: 0.05em; /* 保持或微调字间距 */
            text-shadow: 0 1px 2px rgba(0,0,0,0.15),
                         0 0 10px rgba(200, 225, 255, 0.25), 
                         0 0 20px rgba(220, 235, 255, 0.2);  
        }

        .onboarding-subtitle {
            font-size: 1rem; /* 16px */
            font-weight: 400; /* Regular */
            color: #cbd5e1; /* Light gray-blue */
            line-height: 1.6;
            margin-bottom: 32px;
            max-width: 300px; /* Constrain width for readability */
        }

        .onboarding-button {
            background: linear-gradient(90deg, #FF7F50, #FF6347); /* Warm Coral to Sunset Orange */
            color: white;
            font-weight: 600; /* Semibold */
            padding: 14px 32px;
            border-radius: 30px; /* Pill shape */
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 20px rgba(255,107,71,0.4);
            margin-top: 24px; /* Space above button */
        }
        .onboarding-button:hover {
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 6px 25px rgba(255,107,71,0.5);
        }
        
        .pagination-dots {
            position: absolute;
            bottom: 50px; /* Position above home indicator */
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }
        .pagination-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            transition: background-color 0.3s ease, transform 0.3s ease;
        }
        .pagination-dot.active {
            background-color: rgba(255, 255, 255, 0.8);
            transform: scale(1.2);
        }

        /* Screen 1: 初识微光 - 核心价值宣言 */
        .onboarding-screen-1 {
             background: linear-gradient(170deg, #0A2F51 0%, #005A4B 35%, #008080 70%, #002b20 100%),
                        radial-gradient(ellipse at 10% 15%, rgba(56, 239, 125, 0.3) 0%, transparent 50%), /* Mint glow */
                        radial-gradient(ellipse at 80% 85%, rgba(0, 128, 128, 0.4) 0%, transparent 60%); /* Teal glow */
        }
        .seed-glow { /* Abstract seed/light point */
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(173, 255, 230, 0.5) 0%, rgba(56, 239, 125, 0.2) 40%, transparent 70%);
            border-radius: 50%;
            position: absolute;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0.7;
            animation: subtlePulse 5s infinite ease-in-out;
        }
        @keyframes subtlePulse {
            0%, 100% { transform: translateX(-50%) scale(1); opacity: 0.6; }
            50% { transform: translateX(-50%) scale(1.1); opacity: 0.8; }
        }

        /* Screen 2: AI赋能 - 特色功能展示 */
        .onboarding-screen-2 {
             background: linear-gradient(170deg, #003E54 0%, #006064 40%, #00838F 75%, #004D40 100%),
                        radial-gradient(ellipse at 50% 30%, rgba(0, 200, 200, 0.5) 0%, transparent 60%), /* Cyan wisdom core area */
                        radial-gradient(ellipse at 20% 70%, rgba(255, 127, 80, 0.15) 0%, transparent 60%); /* Coral subtle accent */
        }
        .wisdom-core-onboarding { /* Similar to home page but adapted */
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(56,239,125,0.9) 0%, rgba(0,200,200,0.8) 70%, rgba(0,128,128,0.6) 100%);
            border-radius: 50%;
            margin-bottom: 24px;
            box-shadow: 0 0 25px 8px rgba(56,239,125,0.4), 0 0 35px 15px rgba(0,200,200,0.3);
            animation: breatheWisdomCoreShadowOnboarding 4s ease-in-out infinite;
        }
        @keyframes breatheWisdomCoreShadowOnboarding {
            0%, 100% { box-shadow: 0 0 20px 6px rgba(56,239,125,0.4), 0 0 30px 12px rgba(0,200,200,0.3); opacity: 0.85; }
            50% { box-shadow: 0 0 30px 10px rgba(56,239,125,0.6), 0 0 45px 20px rgba(0,200,200,0.45); opacity: 1; }
        }
        .feature-icon-list {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 24px;
            margin-bottom: 32px;
        }
        .feature-icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 80px;
        }
        .feature-icon-item svg {
            width: 36px;
            height: 36px;
            margin-bottom: 8px;
            color: #AFEEEE; /* Pale Turquoise for icons */
            filter: drop-shadow(0 0 5px rgba(173,255,230,0.5));
        }
        .feature-icon-item span {
            font-size: 0.75rem; /* 12px */
            color: #cbd5e1;
            line-height: 1.3;
        }

        /* Screen 3: 即刻启程 - 行为召唤与授权引导 */
        .onboarding-screen-3 {
            background: linear-gradient(170deg, #0D47A1 0%, #00695C 45%, #FF8F00 85%, #F57C00 100%), /* Deep blue to teal to sunset orange */
                        radial-gradient(ellipse at 50% 90%, rgba(255, 165, 0, 0.4) 0%, transparent 60%), /* Sunset glow from bottom */
                        radial-gradient(ellipse at 20% 15%, rgba(0, 200, 200, 0.3) 0%, transparent 50%); /* Cyan accent */
        }
        .pathway-visual { /* Abstract path or horizon */
            width: 100%;
            height: 150px;
            position: absolute;
            bottom: 100px; /* Above button and dots */
            left:0;
            opacity: 0.15;
            background: 
                linear-gradient(to top, rgba(255,224,189,0.5) 0%, transparent 70%), /* Light emanating from bottom */
                repeating-linear-gradient(
                    45deg,
                    rgba(255,255,255,0.05),
                    rgba(255,255,255,0.05) 10px,
                    rgba(255,255,255,0.02) 10px,
                    rgba(255,255,255,0.02) 20px
                );
        }
        .permission-text {
            font-size: 0.75rem;
            color: #b0bec5; /* Lighter gray */
            margin-top: 20px;
            max-width: 280px;
        }

    </style>
</head>
<body>

    <h1 class="mockup-gallery-title">应用启动与引导</h1>

    <div class="mockup-gallery-container">

        <!-- Screen 1: 初识微光 -->
        <div class="mockup-unit">
            <!-- <h2 class="mockup-page-title">引导页 1/3</h2> Removed for cleaner onboarding look -->
            <div id="mockup-onboarding-1" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="onboarding-app-container onboarding-screen-1">
                        <div class="ios-status-bar"> <!-- Minimal status bar for onboarding -->
                            <span class="time"></span> <span class="signals"></span>
                        </div>
                        <div class="seed-glow"></div>
                        <div class="onboarding-content">
                            <h1 class="onboarding-title text-teal-200">遇见 Aura</h1>
                            <p class="onboarding-subtitle">点亮改变的每一刻。一个专注于微小进步的AI伙伴，与你共同雕琢理想生活。</p>
                            <!-- Button could lead to next screen, or be omitted if auto-advancing -->
                        </div>
                        <div class="pagination-dots">
                            <div class="pagination-dot active"></div>
                            <div class="pagination-dot"></div>
                            <div class="pagination-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Screen 2: AI赋能 -->
        <div class="mockup-unit">
            <!-- <h2 class="mockup-page-title">引导页 2/3</h2> -->
            <div id="mockup-onboarding-2" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="onboarding-app-container onboarding-screen-2">
                        <div class="ios-status-bar">
                            <span class="time"></span> <span class="signals"></span>
                        </div>
                        <div class="onboarding-content">
                            <div class="wisdom-core-onboarding"></div>
                            <h1 class="onboarding-title text-cyan-200">AI驱动的个性化微旅程</h1>
                            <p class="onboarding-subtitle">Aura 不仅是向导，更是伙伴。它倾听你的心声，理解你的节奏，定制专属你的成长路径。</p>
                            <div class="feature-icon-list">
                                <div class="feature-icon-item">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" /></svg>
                                    <span>智能习惯建议</span>
                                </div>
                                <div class="feature-icon-item">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.006 3 11.25c0 4.556 4.03 8.25 9 8.25z" /><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 12.75c0 .414.336.75.75.75h7.5a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H8.25a.75.75 0 00-.75.75v.75z" /></svg>
                                    <span>共情对话反馈</span>
                                </div>
                                <div class="feature-icon-item">
                                     <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" /></svg>
                                    <span>可视化激励</span>
                                </div>
                            </div>
                        </div>
                        <div class="pagination-dots">
                            <div class="pagination-dot"></div>
                            <div class="pagination-dot active"></div>
                            <div class="pagination-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Screen 3: 即刻启程 -->
        <div class="mockup-unit">
            <!-- <h2 class="mockup-page-title">引导页 3/3</h2> -->
            <div id="mockup-onboarding-3" class="iphone-mockup">
                <div class="iphone-screen">
                    <div class="onboarding-app-container onboarding-screen-3">
                        <div class="ios-status-bar">
                             <span class="time"></span> <span class="signals"></span>
                        </div>
                        <div class="pathway-visual"></div>
                        <div class="onboarding-content">
                            <!-- "初萌之光" (Emergent Spark) SVG Icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-16 h-16 text-orange-300 mb-6 filter drop-shadow(0 0 10px #FFD54Fcc)">
                                <!-- Central Core -->
                                <circle cx="12" cy="12" r="2.2" opacity="0.9"/>
                            
                                <!-- Spark 1: Small, slightly elongated, angled top-left -->
                                <ellipse cx="9.5" cy="9" rx="1.8" ry="1.2" transform="rotate(-30 9.5 9)"/>
                                
                                <!-- Spark 2: Medium, more elongated, angled top-right -->
                                <ellipse cx="15" cy="8.5" rx="2.2" ry="1.4" transform="rotate(40 15 8.5)"/>
                            
                                <!-- Spark 3: Small, rounder, bottom-right -->
                                <ellipse cx="14.5" cy="15" rx="1.6" ry="1.3" transform="rotate(-20 14.5 15)"/>
                                
                                <!-- Spark 4: Medium, slightly elongated, bottom-left -->
                                <ellipse cx="8" cy="14.5" rx="2" ry="1.5" transform="rotate(25 8 14.5)"/>
                            
                                <!-- Spark 5: Tiny, almost circular, off-center for asymmetry -->
                                <ellipse cx="12.5" cy="15.5" rx="1" ry="0.8" transform="rotate(10 12.5 15.5)"/>
                            </svg>
                            <h1 class="onboarding-title text-orange-200">从第一束微光开始</h1>
                            <p class="onboarding-subtitle">让 Aura 陪伴你，将每一个微小的习惯，汇聚成生命中璀璨的星河。</p>
                            <button class="onboarding-button">开启我的微光之旅</button>
                            <p class="permission-text">为了更好地陪伴与提醒，Aura可能需要获取通知权限，让每个重要时刻都不被错过。</p>
                        </div>
                        <div class="pagination-dots">
                            <div class="pagination-dot"></div>
                            <div class="pagination-dot"></div>
                            <div class="pagination-dot active"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- End of mockup-gallery-container -->

    <script>
        // Placeholder for potential JS for swiping/navigation between onboarding screens
        // For now, it's a static display of all screens.
        // To make the dots interactive or to enable swiping, JS would be needed.
    </script>
</body>
</html> 