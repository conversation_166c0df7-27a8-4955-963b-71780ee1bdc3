
1.每次点击Tab社区页面进入页面的评论功能页面了显示的是黑屏，需多次下滑刷新页面或者点击其他帖子的评论才能正常显示评论页面"
彻底重构社区Tab评论弹窗机制：
用 .sheet(item: $selectedPost) 绑定帖子对象，每次点击不同帖子都会重建详情页和ViewModel，彻底解决Sheet缓存导致的黑屏问题。
2. 导致无线循环编译错误的原因：
   示例：架构不一致问题
✅ 新组件：使用现代依赖注入模式（构造器参数）
❌ 现有View：还是旧的模式（直接实例化ViewModel）
🔄 结果：新旧架构混合导致编译错误

3.现在已经进入到了一个无法完成编译错误修复的情况，请你读取上下文记录，审查你的修复方案是否正确？重新审查相关联的文件并制定的系统性修复方案修复。
例如1:遗漏了关键文件的审查、没有进行全面的相关文件检查
例如2:重复犯错的根本原因：
我在修复过程中一直在犯同样的错误：
调用不存在的Repository方法：如fetchAllContents、fetchContent(by:)、saveContent等
使用不存在的属性：如payment.user、report.targetId、user.lastActiveDate等
引用不存在的错误类型：如SharingReminderError、CommunityError等

4.所有涉及数据变更的页面都建议用这种“依赖注入+通知驱动刷新”的模式，能最大程度避免数据不同步问题。
5.数据已变但UI没自动刷新

6.我刚才在使用模拟器测试的时候，出现页面报错，在进行使用cursor的Agent审查的时候，发现了以下问题，我担心使用cursor的Agent模式进行修复的时候，它会把我其他的正常的UI、组件、持久数据库、其他页面或者已经开发的正常数据架构造成影响，我该给它什么提示词或者如何下达指令能让它在修复的时候不会影响到其他正常代码呢？并且修复后符合项目审查规则规范。(给我参考的指令或提示词要简短、讲重点）

7.状态栏遮挡页面内容实现方案：使用Safe Area Layout Guides
这是最标准和推荐的做法：
class ViewController: UIViewController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置背景色
        view.backgroundColor = UIColor.systemBackground
        
        // 创建滚动视图
        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)
        
        // 约束到整个视图边缘（包括状态栏区域）
        scrollView.topAnchor.constraint(equalTo: view.topAnchor).isActive = true
        scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor).isActive = true
        scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor).isActive = true
        scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor).isActive = true
        
        // 内容视图约束到safe area
        let contentView = UIView()
        contentView.backgroundColor = UIColor.systemBlue
        contentView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(contentView)
        
        // 内容区域从safe area开始
        contentView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor).isActive = true
        contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor).isActive = true
        contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor).isActive = true
        contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor).isActive = true
        contentView.heightAnchor.constraint(equalToConstant: 1000).isActive = true // 足够高以便滚动
    }
}