请严格按照.cursorrules中的"编译错误系统性修复规范"执行架构重构。

🚨 强制要求：
1. 禁止单点修复模式 - 不得逐个修复问题
2. 必须采用系统性一次性修复策略
3. 按照以下顺序批量修复所有相关文件：

## 📋 修复阶段与编译测试策略

### 第一阶段：数据模型层修复（最高优先级）
**修复内容：**
- 一次性修复所有外键模式违规（EAPayment.swift、EAAnalytics.swift、EAPath.swift、EAAIMessage.swift）
- 改为标准SwiftData @Relationship关系模式
- 确保所有关系遵循单端inverse规则

**🔧 分批执行策略：**
- **批次1**：修复EAPayment.swift + EAAnalytics.swift → 立即编译测试
- **批次2**：修复EAPath.swift + EAAIMessage.swift → 立即编译测试
- **阶段验证**：完整编译测试确认数据模型层无错误

### 第二阶段：Repository层建立（高优先级）
**修复内容：**
- 一次性创建所有缺失的Repository协议和实现
- 建立EARepositoryContainer统一管理
- 确保所有数据访问通过Repository层

**🔧 分批执行策略：**
- **批次1**：创建Repository协议文件 → 编译测试
- **批次2**：实现Repository类 → 编译测试
- **批次3**：创建EARepositoryContainer → 编译测试
- **阶段验证**：完整编译测试确认Repository层无错误

### 第三阶段：Service层重构（中优先级）
**修复内容：**
- 一次性重构所有8个违规Service文件
- 移除ModelContext直接操作，改为Repository依赖注入
- 保持现有API接口不变

**🔧 分批执行策略：**
- **批次1**：重构4个核心Service文件 → 编译测试
- **批次2**：重构剩余4个Service文件 → 编译测试
- **阶段验证**：完整编译测试确认Service层无错误

### 第四阶段：ViewModel层重构（中优先级）
**修复内容：**
- 一次性重构所有6个违规ViewModel文件  
- 移除ModelContext直接操作，改为Repository依赖注入
- 确保@MainActor标记正确

**🔧 分批执行策略：**
- **批次1**：重构3个核心ViewModel文件 → 编译测试
- **批次2**：重构剩余3个ViewModel文件 → 编译测试
- **阶段验证**：完整编译测试确认ViewModel层无错误

### 第五阶段：单例模式替换（低优先级）
**修复内容：**
- 一次性替换所有13个单例模式为依赖注入
- 通过Environment传递服务依赖

**🔧 分批执行策略：**
- **批次1**：替换6个核心单例模式 → 编译测试
- **批次2**：替换剩余7个单例模式 → 编译测试
- **阶段验证**：完整编译测试确认依赖注入无错误

## 🔒 执行要求与错误处理机制

### 基本执行要求：
- 每个阶段必须同时修改所有相关文件
- 修复前先分析所有文件的依赖关系
- 确保修复方案的一致性和完整性
- 遵循项目既有的EA命名规范

### ⚠️ 编译测试与错误处理：
**每次编译测试规则：**
1. **立即分析**：发现错误后立即分析错误类型和影响范围
2. **批次内修复**：在当前批次内一次性修复所有相关错误
3. **重新验证**：重新编译确认无误后再进入下一批次

**错误阈值控制：**
- 🚨 **熔断机制**：如果某个批次的编译错误超过3个，立即停止并重新评估修复策略
- 🔄 **回滚策略**：如果连续2个批次都出现超过3个错误，回滚到上一个成功状态
- 📊 **进度跟踪**：记录每个批次的修复进度和错误情况

**特殊情况处理：**
- **SwiftData关系错误**：优先处理，必须遵循单端inverse规则
- **循环依赖错误**：立即分析依赖链，采用接口抽象解决
- **类型冲突错误**：检查EA命名前缀一致性

## 🎯 修复完成验证清单

**每阶段完成后必须检查：**
- [ ] 所有目标文件编译无错误
- [ ] 遵循.cursorrules开发规范
- [ ] 保持现有API接口兼容性
- [ ] SwiftData关系正确建立
- [ ] 依赖注入正确配置

**全部修复完成后验证：**
- [ ] 整个项目编译无错误无警告
- [ ] 所有外键模式已消除
- [ ] Repository模式完全实施
- [ ] 单例模式完全替换
- [ ] 架构一致性检查通过

请开始执行第一阶段：数据模型层修复。